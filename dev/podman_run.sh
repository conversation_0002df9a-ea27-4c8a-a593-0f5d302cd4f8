#!/bin/bash

# Default values
IMAGE_NAME=${1:-"zrdds-core"}
IMAGE_TAG=${2:-"1.4.3"}
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
HOST=$(hostname)

# Display usage information
usage() {
    echo "Usage: $0 [image_name] [image_tag]"
    echo "  image_name: Name of the image (default: zrdds-core)"
    echo "  image_tag:  Tag of the image (default: 1.4.3)"
    exit 1
}

# Check if help is requested
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    usage
fi

echo "Building image ${FULL_IMAGE_NAME} image..."

# Build the Docker image
podman build -t ${FULL_IMAGE_NAME} -f Dockerfile.dev .
if [ $? -ne 0 ]; then
    echo "Error: Failed to build the image"
    exit 1
fi

echo "Starting services..."

# Remove existing pod and containers if they exist
podman pod rm -f zrdds-pod 2>/dev/null
podman rm -f postgres zrdds-core 2>/dev/null

# Create podman pod
podman pod create --name zrdds-pod -p 8080:8080 -p 5432:5432
if [ $? -ne 0 ]; then
    echo "Error: Failed to create pod"
    exit 1
fi

# Start postgres with the same credentials as in the application.properties
podman run -d \
    --pod zrdds-pod \
    --name postgres \
    -e POSTGRES_DB=discovery \
    -e POSTGRES_USER=postgres \
    -e POSTGRES_PASSWORD=Discovery4postgres \
    localhost/postgres:17.5
if [ $? -ne 0 ]; then
    echo "Error: Failed to start postgres container"
    exit 1
fi

# Wait for Postgres to be ready
echo "Waiting for Postgres to be ready..."
for i in {1..20}; do
    podman exec postgres pg_isready -U postgres && break
    sleep 2
done

echo "Starting zrdds-core..."
podman run -d \
    --pod zrdds-pod \
    --name zrdds-core \
    -e "SPRING_DATASOURCE_PRIMARY_URL=**************************************************************" \
    -e "PGSQL_TOKEN=$(echo -n "postgres:Discovery4postgres" | base64)" \
    ${FULL_IMAGE_NAME}
if [ $? -ne 0 ]; then
    echo "Error: Failed to start zrdds-core container"
    exit 1
fi

echo "Allow few seconds for the Services to start..."
sleep 20
echo "Services started successfully!"

echo "You can access the application at https://localhost:8080"
