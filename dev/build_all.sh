#!/bin/bash

# Set the base directory (parent directory of Eddard)
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
PROJECTS=("<PERSON><PERSON><PERSON>" "Sansa" "Robb" "JCL-Discovery-Plugin")
BUILD_DIR="${BASE_DIR}/Eddard/target"
VERSION="1.4.3"
ROBB_VERSION="${VERSION}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Print colored messages
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Check if a directory exists
check_directory() {
    if [ ! -d "$1" ]; then
        print_message "${RED}" "Error: Directory $1 does not exist!"
        exit 1
    fi
}

# Clean directory of .DS_Store files (mac users)
clean_directory() {
    local dir=$1
    if [ -d "$dir" ]; then
        find "$dir" -name ".DS_Store" -delete
        print_message "${GREEN}" "Cleaned .DS_Store files from $dir"
    fi
}

# Create build directory
mkdir -p "${BUILD_DIR}"

# Create plugins directory if it doesn't exist
PLUGINS_DIR="${BASE_DIR}/Eddard/plugins"
mkdir -p "$PLUGINS_DIR"

clean_directory "$PLUGINS_DIR"

# Check if all required projects exist
for project in "${PROJECTS[@]}"; do
    check_directory "${BASE_DIR}/${project}"
done

print_message "${GREEN}" "Starting build process..."

# First, build and package Catelyn
print_message "${YELLOW}" "Building and packaging Catelyn..."
cd "${BASE_DIR}/Catelyn" || exit 1

# Check if mvnw exists, if not use regular mvn
if [ -f "./mvnw" ]; then
    ./mvnw clean package -DskipTests
else
    mvn clean package -DskipTests
fi

if [ $? -ne 0 ]; then
    print_message "${RED}" "Failed to build Catelyn"
    exit 1
fi

# Find Catelyn's jar
CATELYN_JAR="target/catelyn-${VERSION}.jar"
if [ ! -f "$CATELYN_JAR" ]; then
    print_message "${RED}" "Could not find Catelyn's JAR file: ${CATELYN_JAR}"
    exit 1
fi

# Copy Catelyn's jar to other projects' lib folders
for project in "Sansa" "Robb" "Eddard" "JCL-Discovery-Plugin"; do
    mkdir -p "${BASE_DIR}/${project}/lib"
    cp "$CATELYN_JAR" "${BASE_DIR}/${project}/lib/"
    print_message "${GREEN}" "Copied catelyn-${VERSION}.jar to ${project}/lib/"
done

# Build remaining projects
for project in "Sansa" "Robb" "JCL-Discovery-Plugin"; do
    print_message "${YELLOW}" "Building ${project}..."

    cd "${BASE_DIR}/${project}" || exit 1

    # Check if mvnw exists, if not use regular mvn
    if [ -f "./mvnw" ]; then
        ./mvnw clean package -DskipTests
    else
        mvn clean package -DskipTests
    fi

    if [ $? -ne 0 ]; then
        print_message "${RED}" "Failed to build ${project}"
        exit 1
    fi

    # Copy the built artifacts to the build directory with correct name
    project_lower=$(echo "$project" | tr '[:upper:]' '[:lower:]')

    # Handle different versions for Robb and other projects
    if [ "$project" = "Robb" ]; then
        cp "target/${project_lower}-${ROBB_VERSION}.jar" "${BUILD_DIR}/${project_lower}-${ROBB_VERSION}.jar"
    else
        cp "target/${project_lower}-${VERSION}.jar" "${BUILD_DIR}/${project_lower}-${VERSION}.jar"
    fi

    # Copy JARs to Eddard's plugins folder
    if [ "$project" = "Sansa" ] || [ "$project" = "Robb" ] || [ "$project" = "JCL-Discovery-Plugin" ]; then
        mkdir -p "${BASE_DIR}/Eddard/plugins"
        if [ "$project" = "Robb" ]; then
            cp "target/${project_lower}-${ROBB_VERSION}.jar" "${BASE_DIR}/Eddard/plugins/"
            print_message "${GREEN}" "Copied ${project_lower}-${ROBB_VERSION}.jar to Eddard/plugins/"
        elif [ "$project" = "JCL-Discovery-Plugin" ]; then
            cp "target/jcl-discovery-${VERSION}.jar" "${BASE_DIR}/Eddard/plugins/"
            print_message "${GREEN}" "Copied jcl-discovery-${VERSION}.jar to Eddard/plugins/"
        else
            cp "target/${project_lower}-${VERSION}.jar" "${BASE_DIR}/Eddard/plugins/"
            print_message "${GREEN}" "Copied ${project_lower}-${VERSION}.jar to Eddard/plugins/"
        fi
    fi

    print_message "${GREEN}" "Successfully built ${project}"
done

# Before building Eddard, clean all .DS_Store files
clean_directory "${BASE_DIR}/Eddard"
clean_directory "$PLUGINS_DIR"

# Build Eddard
print_message "${YELLOW}" "Building Eddard..."
cd "${BASE_DIR}/Eddard" || exit 1

if [ -f "./mvnw" ]; then
    ./mvnw clean package -DskipTests
else
    mvn clean package -DskipTests
fi

if [ $? -ne 0 ]; then
    print_message "${RED}" "Failed to build Eddard"
    exit 1
fi

# Final cleanup of any .DS_Store files
clean_directory "$PLUGINS_DIR"

print_message "${GREEN}" "Build completed successfully!"
