// Using Scripted Pipeline for maximum flexibility

String gitBranch = "${env.BRANCH_NAME}"
String fallbackBranch = 'develop'
// For service stream, do not allow downstream component builds to use a different fallback branch
// Should that branch not exist, we have a real problem and we want the end-to-end build to fail
if(gitBranch == 'service_v5.1.0') {
    fallbackBranch = gitBranch
}
String baseImgJob = 'NewArch-Common-base-images'
String osPluginJob = 'NewArch-Common-os-plugins'
String dockerPrebuildJob = 'NewArch-Common-docker-prebuild'
String dockerJob = 'NewArch-Common-docker'
String piCliJob = 'NewArch-Common-pi-cli'
String piDashJob = 'NewArch-Common-pi-dashboard'
String piFrameworkJob = 'NewArch-Common-piframework'
String uiLibJob = 'NewArch-Common-ui-lib'
String pkgJobDist = 'NewArch-Common-opensys-packaging-dist'
String pkgJobHost = 'NewArch-Common-opensys-packaging-host'
String detectSecretsJob = 'NewArch-Common-detect-secrets'
// String pushToMain = 'ZLDA-Push_To_Main-Call-file'

String baseImgRepo = 'zoa-docker-base-images'
String osPluginRepo = 'zoa-os_plugin-customizations'
String dockerRepo = 'zoa-common-docker-MF'
String piCliRepo = 'picli-MF'
String piDashRepo = 'pi-izoa-dashboard-MF'
String piFrameworkRepo = 'piframework-MF'
String uiLibRepo = 'pi-ui-common-MF'
String pkgRepo = 'izoa-packaging'
String detectSecretsRepo = 'izoa-packaging'

def baseImgBranch
def osPluginBranch
def dockerBranch
def piCliBranch
def piDashBranch
def piFrameworkBranch
def uiLibBranch
def pkgBranch
def detectSecretsBranch

def checkBranch = { org, repo ->
    queryString = 'git ls-remote --heads ******************:' + org + '/' + repo + ' ' + gitBranch + '| awk \'{ print $2 }\''
    String branch = fallbackBranch
    String branchTest = sh(
        script: queryString,
        returnStdout: true
    )
    if (branchTest.trim().endsWith(gitBranch)) {
        branch = gitBranch
    }
    return branch
}

def triggers = []
if(gitBranch == 'develop') {
    triggers << cron('0 18 * * *') // run scheduled build at 6:00pm
} else {
    // no scheduled build
}

properties ([
  pipelineTriggers(triggers),
  [ $class: 'BuildDiscarderProperty', strategy: 
    [ $class: 'LogRotator', 
        artifactDaysToKeepStr: '10', 
        artifactNumToKeepStr: '10',
        daysToKeepStr: '10',
        numToKeepStr: '10'
    ]
  ]
])

node('docker-x86_64') {
    try {
        stage('Initialization'){
            try {
                // Initial Slack notification
                slackSend (channel: 'zitoa_izoa_build', color: '#00FF00', message: "STARTED: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]' (${env.BUILD_URL})")
            }
            catch (ignored) {
                unstable(message: "'${STAGE_NAME}' failed.")
                currentBuild.result = 'FAILURE'
            }
        }
        // Build common UI library
        stage("${uiLibJob}") {
            uiLibBranch = checkBranch('IZOA', uiLibRepo)
            String UILIB_JOB = uiLibJob + '/' + uiLibBranch
            echo 'Common UI Library job is: ' + UILIB_JOB
            def uiLibBuild = build job: UILIB_JOB, propagate: false
            def buildResult = uiLibBuild.getResult()
            echo 'Build result of ' + UILIB_JOB + ': ' + buildResult
            if(buildResult == 'NOT_BUILT' || buildResult == 'SUCCESS') {
                echo "'${STAGE_NAME}' completed with result ${buildResult}."
                stageResult = "SUCCESS"
                currentBuild.result = "SUCCESS"
            } else {
                error(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
            }
        }
        // Build new architecture PI Dashboard artifacts
        stage("${piDashJob}") {
            piDashBranch = checkBranch('IZOA', piDashRepo)
            String PIDASH_JOB = piDashJob + '/' + piDashBranch
            echo 'PI Dashboard job (new architecture) is: ' + PIDASH_JOB
            def piDashBuild = build job: PIDASH_JOB, propagate: false
            def buildResult = piDashBuild.getResult()
            if(buildResult == 'NOT_BUILT' || buildResult == 'SUCCESS') {
                echo "'${STAGE_NAME}' completed with result ${buildResult}."
                stageResult = "SUCCESS"
                currentBuild.result = "SUCCESS"
            } else {
                error(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
            }
        }
        // Build new architecture PI CLI
        stage("${piCliJob}") {
            piCliBranch = checkBranch('IZOA', piCliRepo)
            String PICLI_JOB = piCliJob + '/' + piCliBranch
            echo 'PI CLI job (new architecture) is: ' + PICLI_JOB
            def piCliBuild = build job: PICLI_JOB, propagate: false
            def buildResult = piCliBuild.getResult()
            if(buildResult == 'NOT_BUILT' || buildResult == 'SUCCESS') {
                echo "'${STAGE_NAME}' completed with result ${buildResult}."
                stageResult = "SUCCESS"
                currentBuild.result = "SUCCESS"
            } else {
                error(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
            }
        }
        // Build new architecture PI framework
        stage("${piFrameworkJob}") {
            piFrameworkBranch = checkBranch('IZOA', piFrameworkRepo)
            String PIF_JOB = piFrameworkJob + '/' + piFrameworkBranch
            echo 'PI Framework job (new architecture) is: ' + PIF_JOB
            def piFrameworkBuild = build job: PIF_JOB, propagate: false
            def buildResult = piFrameworkBuild.getResult()
            if(buildResult == 'NOT_BUILT' || buildResult == 'SUCCESS') {
                echo "'${STAGE_NAME}' completed with result ${buildResult}."
                stageResult = "SUCCESS"
                currentBuild.result = "SUCCESS"
            } else {
                error(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
            }
        }
        // Build common OCI base images
        stage("${baseImgJob}") {
            // Only 'develop' should build base images; no point in having them built from multiple branches
            if(gitBranch == 'develop') {
                baseImgBranch = checkBranch('IZOA', baseImgRepo)
                String BASE_IMG_JOB = baseImgJob + '/' + baseImgBranch
                echo 'Common OCI base image job is: ' + BASE_IMG_JOB
                def baseImgBuild = build job: BASE_IMG_JOB, propagate: false
                def buildResult = baseImgBuild.getResult()
                if(buildResult == 'NOT_BUILT' || buildResult == 'SUCCESS') {
                    echo "'${STAGE_NAME}' completed with result ${buildResult}."
                    stageResult = "SUCCESS"
                    currentBuild.result = "SUCCESS"
                } else {
                    error(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
                }
            } else {
                echo 'Not creating base images for branch ${gitBranch}.'
            }
        }
        // Build OpenSearch plugins
        stage("${osPluginJob}") {
            osPluginBranch = checkBranch('IZOA', osPluginRepo)
            String DOCKER_JOB = osPluginJob + '/' + osPluginBranch
            echo 'Common OCI production image job is: ' + DOCKER_JOB
            def osPluginBuild = build job: DOCKER_JOB, propagate: false
            def buildResult = osPluginBuild.getResult()
            if(buildResult == 'NOT_BUILT' || buildResult == 'SUCCESS') {
                echo "'${STAGE_NAME}' completed with result ${buildResult}."
                stageResult = "SUCCESS"
                currentBuild.result = "SUCCESS"
            } else {
                error(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
            }
        }
        // Pre-build distributions for some OCI images
        stage("${dockerPrebuildJob}") {
            dockerBranch = checkBranch('IZOA', dockerRepo)
            String DOCKER_PREBUILD_JOB = dockerPrebuildJob + '/' + dockerBranch
            echo 'OCI prebuild job is: ' + DOCKER_PREBUILD_JOB
            def dockerPrebuild = build job: DOCKER_PREBUILD_JOB, propagate: false
            def buildResult = dockerPrebuild.getResult()
            if(buildResult == 'NOT_BUILT' || buildResult == 'SUCCESS') {
                echo "'${STAGE_NAME}' completed with result ${buildResult}."
                stageResult = "SUCCESS"
                currentBuild.result = "SUCCESS"
            } else if(buildResult == 'UNSTABLE') {
                unstable(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
            } else {
                error(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
            }
        }
        // Build common OCI production images
        stage("${dockerJob}") {
            dockerBranch = checkBranch('IZOA', dockerRepo)
            String DOCKER_JOB = dockerJob + '/' + dockerBranch
            echo 'Common OCI production image job is: ' + DOCKER_JOB
            def dockerBuild = build job: DOCKER_JOB, propagate: false
            def buildResult = dockerBuild.getResult()
            if(buildResult == 'NOT_BUILT' || buildResult == 'SUCCESS') {
                echo "'${STAGE_NAME}' completed with result ${buildResult}."
                stageResult = "SUCCESS"
                currentBuild.result = "SUCCESS"
            } else {
                error(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
            }
        }
        // Run distributed packaging process
        stage("${pkgJobDist}") {
            // At this time, allow packaging process only for 'develop' and 'service_v5.1.0' branch
            if(gitBranch == 'develop' || gitBranch == 'service_v5.1.0') {
                String PKG_JOB_DIST = pkgJobDist + '/' + gitBranch
                echo 'Common packaging (distributed) job is: ' + PKG_JOB_DIST
                def pkgBuildDist = build job: PKG_JOB_DIST, propagate: false
                def buildResult = pkgBuildDist.getResult()
                if(buildResult == 'NOT_BUILT' || buildResult == 'SUCCESS') {
                    echo "'${STAGE_NAME}' completed with result ${buildResult}."
                    stageResult = "SUCCESS"
                    currentBuild.result = "SUCCESS"
                } else {
                    error(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
                }
            } else {
                echo 'Not running packaging for branch ${gitBranch}.'
            }
        }
        // Run host packaging process
        stage("${pkgJobHost}") {
            // At this time, allow packaging process only for 'develop' and 'service_v5.1.0' branch
            if(gitBranch == 'develop' || gitBranch == 'service_v5.1.0') {
                String PKG_JOB_HOST = pkgJobHost + '/' + gitBranch
                echo 'Common packaging (host) job is: ' + PKG_JOB_HOST
                def pkgBuildHost = build job: PKG_JOB_HOST, propagate: false
                def buildResult = pkgBuildHost.getResult()
                if(buildResult == 'NOT_BUILT' || buildResult == 'SUCCESS') {
                    echo "'${STAGE_NAME}' completed with result ${buildResult}."
                    stageResult = "SUCCESS"
                    currentBuild.result = "SUCCESS"
                } else {
                    error(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
                }
            } else {
                echo 'Not running packaging for branch ${gitBranch}.'
            }
        }
        // Run Detect Secrets process
        stage("${detectSecretsJob}") {
            String DETECT_SECRETS_JOB = detectSecretsJob + '/' + gitBranch
            echo 'Common Detect Secrets job is: ' + DETECT_SECRETS_JOB
            def detectSecretsBuild = build job: DETECT_SECRETS_JOB, propagate: false
            def buildResult = detectSecretsBuild.getResult()
            if(buildResult == 'NOT_BUILT' || buildResult == 'SUCCESS') {
                echo "'${STAGE_NAME}' completed with result ${buildResult}."
                stageResult = "SUCCESS"
                currentBuild.result = "SUCCESS"
            } else {
                error(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
            }
        }
        /*
        // Call Push to Main
        stage("${pushToMain}") {
            // Only 'develop' branch content should be pushed to 'main'
            if(gitBranch == 'develop') {
                echo 'Push to Main job is: ' + pushToMain
                def ptmBuild = build job: pushToMain, propagate: false
                def buildResult = ptmBuild.getResult()
                if(buildResult == 'NOT_BUILT' || buildResult == 'SUCCESS') {
                    echo "'${STAGE_NAME}' completed with result ${buildResult}."
                    stageResult = "SUCCESS"
                    currentBuild.result = "SUCCESS"
                } else {
                    error(message: "'${STAGE_NAME}' completed with result ${buildResult}.")
                }
            } else {
                echo 'Not running Push to Main for branch ${gitBranch}.'
            }
        } */
    }
    catch (exc) {
        echo 'Build failed.'
        currentBuild.result = 'FAILURE'
    }
    finally {
        echo 'Build result: ' + currentBuild.currentResult
        if (currentBuild.currentResult == 'FAILURE') {
            slackSend (channel: 'zitoa_izoa_build', color: '#FF0000', message: "FAILED: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]' (${env.BUILD_URL}) -- <@DFAQ1DJHL> <@W4WHG50LC> <@W8G4YUF7X> <@W8FAS42SC>")
        } else if (currentBuild.currentResult == 'SUCCESS') {
            slackSend (channel: 'zitoa_izoa_build', color: '#00FF00', message: "SUCCESSFUL: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]' (${env.BUILD_URL})")
        } else if (currentBuild.currentResult == 'ABORTED') {
            slackSend (channel: 'zitoa_izoa_build', color: '#C0C0C0', message: "ABORTED: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]' (${env.BUILD_URL})")
        }
    }
}
