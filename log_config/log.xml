<configuration>
    <appender name="ROLLING" class="ch.qos.logback.core.rolling.RollingFileAppender">
      <file>logs/zrdds-core.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
          <fileNamePattern>logs/zrdds-core-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <!-- WARNING: Do NOT set maxHistory to 0; it can cause logging to fail or behave incorrectly. Use a positive integer to retain log files. -->
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date %level %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%date %level %msg%n</pattern>
        </encoder>
    </appender>
    <logger name="com.ibm.palantir" level="info">
        <appender-ref ref="ROLLING" />
    </logger>
    <root level="info">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>