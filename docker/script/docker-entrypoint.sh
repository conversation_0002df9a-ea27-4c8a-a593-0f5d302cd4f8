#! /bin/bash

POSTGRES_DATABASE_USER=$(echo ${PGSQL_TOKEN} | base64 -d | cut -f 1 -d ':')
POSTGRES_DATABASE_PASSWORD=$(echo ${PGSQL_TOKEN} | base64 -d | cut -f 2 -d ':')
POSTGRES_ADMIN=$(echo ${PGSQL_ADMIN_TOKEN} | base64 -d | cut -f 1 -d ':')
POSTGRES_ADMIN_PASSWORD=$(echo ${PGSQL_ADMIN_TOKEN} | base64 -d | cut -f 2 -d ':')

KAFKA_BOOTSTRAP_SERVER_HOST="${KAFKA_BOOTSTRAP_SERVER_HOST:-kafkabroker}"
KAFKA_BOOTSTRAP_SERVER_PORT="${KAFKA_BOOTSTRAP_SERVER_PORT:-19092}"

# Configure connect-standalone.properties and connect-elasticsearch-sink.properties
# bootstrap.servers=localhost:9092 on connect-standalone.properties
# es.connection=localhost:9200 on connect-elasticsearch-sink.properties
configfilename=connect-standalone.properties
# sinkfilename=connect-elasticsearch-sink.properties
srcConfigFilePath=/var/kafka/config/${configfilename}
# srcSinkFilePath=/var/kafka/config/${sinkfilename}
destConfigFilePath=/opt/kafka/config/${configfilename}
# destSinkFilePath=/opt/kafka/config/${sinkfilename}
if [[ -f "${srcConfigFilePath}" ]]; then
    if [[ -n "${KAFKA_BOOTSTRAP_SERVER_HOST}" && -n "${KAFKA_BOOTSTRAP_SERVER_PORT}" ]]; then
        sed "s|bootstrap.servers=localhost:9092|bootstrap.servers=${KAFKA_BOOTSTRAP_SERVER_HOST}:${KAFKA_BOOTSTRAP_SERVER_PORT}| ;" ${srcConfigFilePath} >${destConfigFilePath}
    else
        exit 1
    fi
else
    exit 1
fi

CONNECT_POSTGRES_DATABASE_HOSTNAME="${POSTGRES_DATABASE_HOSTNAME:-host.docker.internal}"
CONNECT_POSTGRES_DATABASE_PORT="${POSTGRES_DATABASE_PORT:-5432}"

CONNECT_POSTGRES_DATABASE_USER="${POSTGRES_DATABASE_USER:-dbzuser}"
CONNECT_POSTGRES_DATABASE_PASSWORD="${POSTGRES_DATABASE_PASSWORD:-dbzpass}"

CONNECT_POSTGRES_ADMIN="${POSTGRES_ADMIN:-postgres}"
CONNECT_POSTGRES_ADMIN_PASSWORD="${POSTGRES_ADMIN_PASSWORD:-Discovery4postgres}"

DISCOVERY_POSTGRES_DATABASE_USER="${POSTGRESQL_USER:-discovery}"

CONNECT_POSTGRES_DATABASE_DBNAME="${POSTGRES_DATABASE_DBNAME:-discovery}"
CONNECT_TOPIC_PREFIX="${CONNECT_TOPIC_PREFIX:-IBM-ZRDDS-SNOW}"
CONNECT_TABLE_INCLUDE_LIST="${CONNECT_TABLE_INCLUDE_LIST:-public.z_series_computer,public.sysplex,public.zos,public.lpar,public.db2_data_sharing_group,public.db2_subsystem,public.db2_database,public.db2_stored_procedure,public.cics_region,public.cics_transaction,public.ims_subsystem,public.ims_database,public.ims_transaction,public.ims_sysplex_group,public.mq_subsystem,public.mq_local_queue,public.mq_model_queue,public.mq_alias_queue,public.mq_remote_queue,public.mq_queue_sharing_group,public.jcl_operation_data,public.jcl_dynamic_data,public.relationship_service_now}"
CONNECT_COLUMN_EXCLUDE_LIST="${CONNECT_COLUMN_EXCLUDE_LIST:''}"
CONNECT_SKIP_MESSAGES_WITHOUT_CHANGE="${CONNECT_SKIP_MESSAGES_WITHOUT_CHANGE:-true}"
CONNECT_TRANSFORMS="${CONNECT_TRANSFORMS:-sieve,unwrap}"
CONNECT_TRANSFORMS_UNWRAP_TYPE="${CONNECT_TRANSFORMS_UNWRAP_TYPE:-io.debezium.transforms.ExtractNewRecordState}"
CONNECT_TRANSFORMS_SIEVE_TYPE="${CONNECT_TRANSFORMS_SIEVE_TYPE:-com.ibm.palantir.transforms.KafkaSieveTransform}"
CONNECT_TRANSFORMS_SIEVE_IGNORE_COLUMNS="${CONNECT_TRANSFORMS_SIEVE_IGNORE_COLUMNS:-scan_date,kafka_send_date}"

connectPropertyFileName=connect-postgres-source.properties
srcConnectPropertyFilePath=/var/kafka/config/${connectPropertyFileName}
connectPropertyFilePath=/opt/kafka/config/${connectPropertyFileName}

if [[ -f "${srcConnectPropertyFilePath}" ]]; then
    sed -e "s|database.hostname=.*|database.hostname=${CONNECT_POSTGRES_DATABASE_HOSTNAME}| ;\
      s|database.port=.*|database.port=${CONNECT_POSTGRES_DATABASE_PORT}| ;\
      s|database.user=.*|database.user=${CONNECT_POSTGRES_DATABASE_USER}| ;\
      s|database.password=.*|database.password=${CONNECT_POSTGRES_DATABASE_PASSWORD}| ;\
      s|database.dbname=.*|database.dbname=${CONNECT_POSTGRES_DATABASE_DBNAME}| ;\
      s|topic.prefix=.*|topic.prefix=${CONNECT_TOPIC_PREFIX}| ;\
      s|table.include.list=.*|table.include.list=${CONNECT_TABLE_INCLUDE_LIST}| ;\
      s|column.exclude.list=.*|column.exclude.list=${CONNECT_COLUMN_EXCLUDE_LIST}| ;\
      s|skip.messages.without.change=.*|skip.messages.without.change=${CONNECT_SKIP_MESSAGES_WITHOUT_CHANGE}| ;\
      s|transforms=.*|transforms=${CONNECT_TRANSFORMS}| ;\
      s|transforms.unwrap.type=.*|transforms.unwrap.type=${CONNECT_TRANSFORMS_UNWRAP_TYPE}| ;\
      s|transforms.sieve.type=.*|transforms.sieve.type=${CONNECT_TRANSFORMS_SIEVE_TYPE}| ;\
      s|transforms.sieve.ignore.columns=.*|transforms.sieve.ignore.columns=${CONNECT_TRANSFORMS_SIEVE_IGNORE_COLUMNS}| ;" \
        ${srcConnectPropertyFilePath} >${connectPropertyFilePath}
else
    exit 1
fi

echo "Waiting for Postgres to be ready. ${CONNECT_POSTGRES_DATABASE_HOSTNAME}:${CONNECT_POSTGRES_DATABASE_PORT}"
until $(wait-for-it.sh --quiet --strict --timeout=60 ${CONNECT_POSTGRES_DATABASE_HOSTNAME}:${CONNECT_POSTGRES_DATABASE_PORT} >/dev/null); do
    echo "Waiting for Postgres to be ready. ${CONNECT_POSTGRES_DATABASE_HOSTNAME}:${CONNECT_POSTGRES_DATABASE_PORT}"
done
echo "Postgres ready. ${CONNECT_POSTGRES_DATABASE_HOSTNAME}:${CONNECT_POSTGRES_DATABASE_PORT}"

# Create user and role for Postgres connector
export PGPASSWORD="${CONNECT_POSTGRES_ADMIN_PASSWORD}"
psql -U ${CONNECT_POSTGRES_ADMIN} -h ${CONNECT_POSTGRES_DATABASE_HOSTNAME} -p ${CONNECT_POSTGRES_DATABASE_PORT} <<EOF
    CREATE ROLE $CONNECT_POSTGRES_DATABASE_USER LOGIN;
    ALTER ROLE $CONNECT_POSTGRES_DATABASE_USER WITH PASSWORD '$CONNECT_POSTGRES_DATABASE_PASSWORD';
    ALTER ROLE $CONNECT_POSTGRES_DATABASE_USER WITH REPLICATION;
    CREATE ROLE rgroup;
    GRANT rgroup TO postgres;
    GRANT rgroup TO $CONNECT_POSTGRES_DATABASE_USER;
    GRANT rgroup TO $DISCOVERY_POSTGRES_DATABASE_USER;
    \c discovery
    DO \$\$
    DECLARE
      count integer := 0;
    BEGIN
    LOOP
        EXIT WHEN count >= 300;
        EXIT WHEN (SELECT count(table_name) FROM information_schema.tables WHERE table_schema = 'public') >= 65;
        PERFORM pg_sleep(10);
        count := count + 10;
        RAISE NOTICE 'Wait % seconds for creating tables', count;
    END LOOP;
    END\$\$;

    CREATE PUBLICATION dbz_publication FOR ALL TABLES;
    DO \$\$DECLARE x record;
        BEGIN
        FOR x IN SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name
        LOOP
            EXECUTE 'ALTER TABLE ' || quote_ident(x.table_name) || ' OWNER TO rgroup';
        END LOOP;
    END\$\$;
EOF

psql_exit_status=$?
if [ $psql_exit_status != 0 ]; then
    echo "psql failed while trying to run this sql script" 1>&2
    exit $psql_exit_status
fi

# Check if Kafka Broker is running.
# Wait for 60 sec for Kafka Broker to come up.
# If it is not up by the set 60sec timeout we will continue checking for Kafka Broker to be up.
echo "Waiting for Kafka Broker to be ready. ${KAFKA_BOOTSTRAP_SERVER_HOST}:${KAFKA_BOOTSTRAP_SERVER_PORT}"
until $(wait-for-it.sh --quiet --strict --timeout=60 ${KAFKA_BOOTSTRAP_SERVER_HOST}:${KAFKA_BOOTSTRAP_SERVER_PORT} >/dev/null); do
    echo "Waiting for Kafka Broker to be ready. ${KAFKA_BOOTSTRAP_SERVER_HOST}:${KAFKA_BOOTSTRAP_SERVER_PORT}"
done
echo "Kafka Broker ready. ${KAFKA_BOOTSTRAP_SERVER_HOST}:${KAFKA_BOOTSTRAP_SERVER_PORT}"

# Starting Kafka Connect
exec "$@"
