# OpenSSL v3 build stage
FROM registry.access.redhat.com/ubi8/ubi-minimal:8.10 AS builder

ARG OSSL_VER=see_.buildenv
ARG CURL_VER=see_.buildenv
ARG PSL_VER=see_.buildenv
ARG GZIP_VER=see_.buildenv
ARG TAR_VER=see_.buildenv
ARG ZLIB_VER=see_.buildenv
ARG GREP_VER=see_.buildenv
ARG GAWK_VER=see_.buildenv
ARG FIND_VER=see_.buildenv
ARG SHADOW_VER=see_.buildenv
ARG SED_VER=see_.buildenv
ARG BC_VER=see.buildenv
ARG TEXINFO_VER=see.buildenv
ARG BINUTILS_VER=see.buildenv

COPY zoa-docker-base-images/patches/gzip.patch /tmp/

RUN microdnf update && \
    microdnf install -y glibc-langpack-en xz bzip2 wget gzip tar curl make gcc gcc-c++ perl findutils shadow-utils util-linux python3 git diffutils && \
    microdnf clean all && \
    HERE=$( pwd ) && \
    CURL_VER_UNDERSCORE=$( echo ${CURL_VER} | tr '.' '_' ) && \
    curl -s -L https://github.com/openssl/openssl/releases/download/openssl-${OSSL_VER}/openssl-${OSSL_VER}.tar.gz -O && \
    curl -s -L https://github.com/rockdaboot/libpsl/releases/download/${PSL_VER}/libpsl-${PSL_VER}.tar.gz -O && \
    curl -s -L https://github.com/curl/curl/releases/download/curl-${CURL_VER_UNDERSCORE}/curl-${CURL_VER}.tar.gz -O && \
    curl -s -L https://mirrors.ibiblio.org/gnu/gzip/gzip-${GZIP_VER}.tar.gz -O && \
    curl -s -L https://ftp.gnu.org/gnu/tar/tar-${TAR_VER}.tar.gz -O && \
    curl -s -L https://zlib.net/zlib-${ZLIB_VER}.tar.gz -O && \
    curl -s -L https://mirrors.ibiblio.org/gnu/grep/grep-${GREP_VER}.tar.gz -O && \
    curl -s -L https://mirrors.ibiblio.org/gnu/gawk/gawk-${GAWK_VER}.tar.gz -O && \
    curl -s -L https://mirrors.ibiblio.org/gnu/findutils/findutils-${FIND_VER}.tar.xz -O && \
    curl -s -L https://github.com/shadow-maint/shadow/releases/download/${SHADOW_VER}/shadow-${SHADOW_VER}.tar.xz -O && \
    curl -s -L https://mirrors.ibiblio.org/gnu/sed/sed-${SED_VER}.tar.gz -O && \
    curl -s -L https://mirrors.ibiblio.org/gnu/texinfo/texinfo-${TEXINFO_VER}.tar.xz -O && \
    curl -s -L https://mirrors.ibiblio.org/gnu/bc/bc-${BC_VER}.tar.gz -O && \
    curl -s -L https://mirrors.ibiblio.org/pub/mirrors/gnu/binutils/binutils-${BINUTILS_VER}.tar.xz -O && \
    curl -s -L https://sourceforge.net/projects/infozip/files/UnZip%206.x%20%28latest%29/UnZip%206.0/unzip60.tar.gz/download -o unzip60.tar.gz && \
    curl -s -L https://sourceforge.net/projects/infozip/files/Zip%203.x%20%28latest%29/3.0/zip30.tar.gz/download -o zip30.tar.gz && \
    mkdir build && \
    tar xf openssl-${OSSL_VER}.tar.gz -C build && \
    tar xf libpsl-${PSL_VER}.tar.gz -C build && \
    tar xf curl-${CURL_VER}.tar.gz -C build && \
    tar xf gzip-${GZIP_VER}.tar.gz -C build && \
    tar xf tar-${TAR_VER}.tar.gz -C build && \
    tar xf zlib-${ZLIB_VER}.tar.gz -C build && \
    tar xf grep-${GREP_VER}.tar.gz -C build && \
    tar xf gawk-${GAWK_VER}.tar.gz -C build && \
    tar xf findutils-${FIND_VER}.tar.xz -C build && \
    tar xf shadow-${SHADOW_VER}.tar.xz -C build && \
    tar xf sed-${SED_VER}.tar.gz -C build && \
    tar xf texinfo-${TEXINFO_VER}.tar.xz -C build && \
    tar xf bc-${BC_VER}.tar.gz -C build && \
    tar xf binutils-${BINUTILS_VER}.tar.xz -C build && \
    tar xf zip30.tar.gz -C build && \
    tar xf unzip60.tar.gz -C build && \
    rm *.tar.gz && \
    rm *.tar.xz && \
    # Build OpenSSL \
    cd ${HERE} && \
    cd build/openssl-${OSSL_VER} && \
    ./Configure && \
    sed -i -e "s%^install:.*$%install:\ install_sw\ install_ssldirs%g" Makefile && \
    make && \
    # make test && \
    make install && \
    echo '/usr/local/lib' >> /etc/ld.so.conf.d/openssl.conf && \
    echo '/usr/local/lib64' >> /etc/ld.so.conf.d/openssl.conf && \
    ldconfig && \
    # Build libpsl \
    cd ${HERE} && \
    cd build/libpsl-${PSL_VER} && \
    ./configure && \
    make && \
    # make check && \
    make install && \
    # Build curl \
    cd ${HERE} && \
    cd build/curl-${CURL_VER} && \
    ./configure --with-openssl && \
    make && \
    # make test && \
    make install && \
    # Build gzip \
    cd ${HERE} && \
    cd build/gzip-${GZIP_VER} && \
    git apply < /tmp/gzip.patch && \
    ./configure && \
    make && \
    # make check && \
    make install && \
    # Build tar \
    cd ${HERE} && \
    cd build/tar-${TAR_VER} && \
    export FORCE_UNSAFE_CONFIGURE=1 && \
    ./configure && \
    make && \
    # make check && \
    make install && \
    # Build unzip \
    cd ${HERE} && \
    cd build/unzip60 && \
    cp unix/Makefile . && \
    make generic && \
    # make test && \
    make install && \
    # Build zip \
    cd ${HERE} && \
    cd build/zip30 && \
    cp unix/Makefile . && \
    make generic && \
    make install && \
    # Build zlib \
    cd ${HERE} && \
    cd build/zlib-${ZLIB_VER} && \
    ./configure && \
    make && \
    # make test && \
    make install && \
    # Build grep \
    cd ${HERE} && \
    cd build/grep-${GREP_VER} && \
    ./configure && \
    make && \
    # make check && \
    make install && \
    # Build find \
    cd ${HERE} && \
    cd build/findutils-${FIND_VER} && \
    ./configure && \
    make && \
    # make check && \
    make install && \
    # Build shadow tools \
    cd ${HERE} && \
    cd build/shadow-${SHADOW_VER} && \
    ./configure --disable-static --without-libbsd --with-{b,yes}crypt && \
    make && \
    # make check && \
    make install && \
    cp src/groupadd /usr/local/bin && \
    cp src/useradd /usr/local/bin && \
    # Build sed \
    cd ${HERE} && \
    cd build/sed-${SED_VER} && \
    ./configure && \
    make && \
    # make check && \
    make install && \
    #Build texinfo \
    cd ${HERE} && \
    cd build/texinfo-${TEXINFO_VER} && \
    ./configure && \
    make && \
    # make check && \
    make install && \
    #Build bc \
    cd ${HERE} && \
    cd build/bc-${BC_VER} && \
    ./configure && \
    make && \
    # make check && \
    make install && \
    #Build binutils \
    cd ${HERE} && \
    cd build/binutils-${BINUTILS_VER} && \
    ./configure && \
    make && \
    # make check && \
    make install && \
    # Build gawk \
    cd ${HERE} && \
    cd build/gawk-${GAWK_VER} && \
    ./configure && \
    make && \
    # make check && \
    make install && \
    rpm -ql ca-certificates | tar cf /usr/local/ca.tar -T - 2>/dev/null || echo "Created CA archive." && \
    rpm -ql libstdc++ | tar cf /usr/local/libstdcplusplus.tar -T - 2>/dev/null || echo "Created libstdc++ archive."

# Final stage
FROM registry.access.redhat.com/ubi8/ubi-micro:8.10

LABEL feature="IBM Z AIOps - Base Image"

COPY --from=builder /usr/local /usr/local
COPY zoa-docker-base-images/utils/wait-for-it.sh zoa-docker-base-images/utils/changeLogLevel.sh /usr/local/bin/
RUN echo '/usr/local/lib' >> /etc/ld.so.conf.d/locallibs.conf && \
    echo '/usr/local/lib64' >> /etc/ld.so.conf.d/locallibs.conf && \
    ldconfig && \
    tar xf /usr/local/ca.tar -C / && \
    tar xf /usr/local/libstdcplusplus.tar -C / && \
    rm /usr/local/ca.tar /usr/local/libstdcplusplus.tar && \
    chmod 755 /usr/local/bin/*.sh

CMD ["bash"]
