#!/usr/bin/env groovy
/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021, 2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/


echo "Branch: ${env.BRANCH_NAME}"

env.COMPONENT = "zrdds-api"
env.VERSION = "1.4.3"
def gitRepo = "Baratheon"

def dockerRegistry = "icr.io/zoa-oci"

def now = new Date()
def buildTime = now.format("yyyyMMdd.HHmmss", TimeZone.getTimeZone('UTC'))
def tagPrefix = "${env.VERSION}.${buildTime}"

env.BASE_PATH = "com/ibm/nazare/discovery"
def twistlockScanResultFolder = "$BASE_PATH/twistlock_scans/$COMPONENT/$VERSION"

def jfUrl = "https://na.artifactory.swg-devops.com/artifactory"
def jfCredentialsId = 'zoa-artifactory-token'
def jfServer = Artifactory.newServer url: "${jfUrl}", credentialsId: "${jfCredentialsId}"


if (env.BRANCH_NAME ==~ /(release|master|dev)/) {
  tagPrefix = "${env.VERSION}"
}

pipeline {
   agent none

   options {
      //skipDefaultCheckout()
      disableConcurrentBuilds()
      // Only keep the last 5 builds to save space. Always keeps the last successful build.
      buildDiscarder(logRotator(numToKeepStr: '5', artifactNumToKeepStr: '5'))
   }

   tools {
      maven 'Nazare_MVN'
      jdk 'temurin-21'
   }

   environment {
     DOCKER_REGISTRY = "${dockerRegistry}"
     COMPONENT = "${env.COMPONENT}"
     TAG_PREFIX = "${tagPrefix}"
     REPO = "${gitRepo}"
     BRANCH = "${env.BRANCH_NAME}"
   }

   // Code checkout is implicit
   stages {
      stage('Parallel Build') {
         matrix {
            agent {
               label "docker-${ARCH}"
            }
            axes {
               axis {
                  name 'ARCH'
                  values 'amd64', 's390x'
               }
            }
            stages {
               stage('Compile') {
                  steps {
                     sh "mvn clean compile"
                  }
               }
               stage('Test') {
                  environment {
                     scannerHome = tool 'SonarQubeScanner'
                  }
                  steps {
                     withSonarQubeEnv('ZDSSonarQube') {
                        echo "Testing & Analyzing"
                        sh "mvn org.jacoco:jacoco-maven-plugin:prepare-agent -Dmaven.test.failure.ignore=false -Dproject.version=${env.VERSION}-${buildTime} package sonar:sonar"
                     }
                  }
               }
              stage('Build') {
                 steps {
                    withCredentials([
                       string(
                          credentialsId: 'zldazaadevfunc',
                          variable: 'IBMCLOUD_API_KEY'
                       )
                    ])
                    {
                      script {
                        echo "Docker Build"
                        sh '''
                          ibmcloud login -r us-east
                          ibmcloud cr login --client docker
                          ENV_ARCH=$( uname -m )
                          if [ "${ENV_ARCH}" = "s390x" ]
                          then
                            sed -i -e "s%-x86_64%-s390x%g" Dockerfile
                            TAG_ARCH=s390x
                          elif [ "${ENV_ARCH}" = "x86_64" ]
                          then
                            TAG_ARCH=amd64
                          else
                            TAG_ARCH=${ENV_ARCH}
                          fi
                          REPO_U=`echo ${REPO} | tr "-" "_" | tr [[:upper:]] [[:lower:]]`
                          COMMIT_HASH=`git rev-parse --verify origin/${BRANCH}`
                          docker build --build-arg COMMIT=${COMMIT_HASH} --build-arg REPO=${REPO_U} -f Dockerfile -t ${DOCKER_REGISTRY}/${COMPONENT}:${TAG_PREFIX}-${TAG_ARCH} .
                        '''
                      }
                    }
                 }
              }

              stage('Push') {
                steps {
                  script {
                    if (env.BRANCH_NAME ==~ /^release.*/ ||  env.BRANCH_NAME == 'dev') {
                      sh "docker push ${dockerRegistry}/${env.COMPONENT}:${tagPrefix}-${ARCH}"
                    } else {
                      echo "Push stage is skipped for feature branches!"
                    }
                  }
                }
              }
              stage('Clean') {
                steps {
                  script {
                    sh "docker rmi ${dockerRegistry}/${env.COMPONENT}:${tagPrefix}-${ARCH}"
                  }
                }
              }
            }

            post {
               success {
                  echo "Build ${buildTime} passed!"
                  notifyBuild(buildTime, "SUCCESS")
                  deleteDir()
               }
               unsuccessful {
                  echo "Build ${buildTime} failed!"
                  notifyBuild(buildTime, "FAILED")
                  deleteDir()
               }
            }
         }
      }

      stage('Twistlock Scan') {
         when {
            expression {  (env.BRANCH_NAME ==~ /^release.*/ || env.BRANCH_NAME == 'dev') }
         }
         agent {
            label "docker-amd64"
         }

         steps {
            withCredentials([
               usernamePassword(credentialsId: 'Twistlock-Token', usernameVariable: 'TWISTLOCK_USER', passwordVariable: 'TWISTLOCK_PASS'),
               string(credentialsId: 'jfrog-4-twistlock', variable: 'ARTIFACTORY_ACCESS_TOKEN'),
               string(credentialsId: 'zldazaadevfunc', variable: 'IBMCLOUD_API_KEY'), string(credentialsId: 'zrdds-control-group', variable: 'ZRDDS_CONTROL_GROUP')
            ]) {
               script {

                    if (env.BRANCH_NAME ==~ /^release.*/ ||  env.BRANCH_NAME == 'dev') {

                      sh script: """
                        #!/bin/bash
                        echo "=========================================================="
                        echo "Downloading Twistlock latest executable file"
                        rm -rf tt_client tt_latest.zip
                        curl -fSL -H "Authorization: Bearer \${ARTIFACTORY_ACCESS_TOKEN}" "https://na.artifactory.swg-devops.com/artifactory/css-ets-scs-consec-team-public-generic-local/Twistlock%20Executable/tt_latest.zip" --output tt_latest.zip

                        echo "=========================================================="
                        echo "extracting files . . ."
                        unzip -q tt_latest.zip -d tt_client

                        echo "=========================================================="
                        echo "preparing tt executable . . ."
                        TT_DIR=\$(echo tt_client/tt_*/linux_x86_64)
                        chmod +x \${TT_DIR}/tt

                        echo "=========================================================="
                        echo "Installing uuid-runtime and file (if available)"
                        if command -v apt-get >/dev/null 2>&1; then
                          if sudo -n true 2>/dev/null; then SUDO=sudo; else SUDO=""; fi
                          \$SUDO apt-get update -y && \$SUDO apt-get install -y uuid-runtime file || true
                        fi

                        echo "=========================================================="
                        echo "checking tt dependencies . . ."
                        \${TT_DIR}/tt check-dependencies || true

                        echo "=========================================================="
                        echo "Scan Running"
                        AMD_IMAGE_TO_SCAN="\${DOCKER_REGISTRY}/\${COMPONENT}:\${TAG_PREFIX}-amd64"
                        ZLINUX_IMAGE_TO_SCAN="\${DOCKER_REGISTRY}/\${COMPONENT}:\${TAG_PREFIX}-s390x"

                        \${TT_DIR}/tt images ps --user "\${TWISTLOCK_USER}:\${TWISTLOCK_PASS}" --control-group "\${ZRDDS_CONTROL_GROUP}" "\${AMD_IMAGE_TO_SCAN}" --iam-api-key "\${IBMCLOUD_API_KEY}" --imagetype prod --output-format csv --output-file ${COMPONENT}_amd64_twistlock_scan

                         \${TT_DIR}/tt images ps --user "\${TWISTLOCK_USER}:\${TWISTLOCK_PASS}" --control-group "\${ZRDDS_CONTROL_GROUP}" "\${ZLINUX_IMAGE_TO_SCAN}" --iam-api-key "\${IBMCLOUD_API_KEY}" --imagetype prod --output-format csv --output-file ${COMPONENT}_s390x_twistlock_scan


                      """, shell: '/bin/bash'

                      // Archive and upload CSV files immediately after scan
                        archiveArtifacts artifacts: '*.csv', allowEmptyArchive: false

                        def uploadSpec = """{
                          "files": [
                            {
                              "pattern": "*.csv",
                              "target": "sys-izoa-zds-maven-local/${twistlockScanResultFolder}/${buildTime}/"
                            }
                          ]
                        }"""
                        jfServer.upload spec: uploadSpec
                      }
                    }
                  }
                }
              }
   }
}

// Notify slack for branch builds
def notifyBuild(String buildTime, String buildStatus = 'STARTED')
{
    // build status of null means successful
      buildStatus = buildStatus ?: 'SUCCESS'

   // Default values
   def colorName = 'RED'
   def colorCode = '#FF0000'
   def gitCommitMessage = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()

    def subject = "${env.JOB_NAME} - Build #${env.BUILD_NUMBER} : ${buildStatus}"
    def innerText = "Build started at: ${buildTime}"

    if (buildStatus == 'STARTED')
    {
        colorName = 'YELLOW'
        colorCode = '#FFFF00'
    }
    else if (buildStatus == 'SUCCESS')
    {
        colorName = 'GREEN'
        colorCode = '#00FF00'
    }
    else
    {
        colorName = 'RED'
        colorCode = '#FF0000'
    }

   def attachments = [
      [
         color: colorCode,
         pretext: 'IBM Z Discovery CI/CD',
         title: subject,
         title_link: env.BUILD_URL,
         text: innerText,
         fields: [[
               title: 'Container',
               value: env.COMPONENT,
               short: true
            ],
            [
               title: 'Branch',
               value: env.BRANCH_NAME,
               short: true
            ],
            [
               title: 'Version',
               value: env.VERSION,
               short: true
            ],
            [
               title: 'Node',
               value: env.NODE_NAME,
               short: true
            ]],
         footer: 'IBM Z Discovery',
         footer_icon: 'https://platform.slack-edge.com/img/default_application_icon.png'
      ]
   ]

   slackSend(attachments: attachments)
}
