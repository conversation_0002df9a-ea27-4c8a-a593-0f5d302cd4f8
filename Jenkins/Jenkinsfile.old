#!/usr/bin/env groovy
/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

import groovy.json.JsonOutput

echo "Branch: ${env.BRANCH_NAME}"

env.BASE_PATH = "com/ibm/nazare/discovery"
env.COMPONENT = "eddard"
env.VERSION = "*******"
def uploadSubFolder = "$BASE_PATH/$COMPONENT/$VERSION"

def jfUrl = "https://na.artifactory.swg-devops.com/artifactory"
def jfCredentialsId = 'discovery-application-jfrog-idtoken'
def jfServer = Artifactory.newServer url: "${jfUrl}", credentialsId: "${jfCredentialsId}"

def artifactory_api_url = "https://na.artifactory.swg-devops.com/artifactory/api/storage"
def artifactory_repo = "sys-nazare-cicd-ext-maven-local"
def attifactory_path = "com/ibm/nazare/discovery"
def discovery_repo = "${artifactory_api_url}/${artifactory_repo}/${attifactory_path}"

def artifactoryDockerRegistry = "docker-eu.artifactory.swg-devops.com"

def now = new Date()
def buildTime = now.format("yyyyMMdd.HHmmss", TimeZone.getTimeZone('UTC'))
def buildVersion = "*******"

pipeline {
   agent {
      node {
         label 'zds-node'
      }
   }
   options {
      disableConcurrentBuilds()
   }

   // Code checkout is implicit
   stages {
      stage('Preparation') {
         steps {
           withCredentials([string(credentialsId: '32689d01-67b8-4306-a71a-7e416449d52a', variable: 'TOKEN')]) {
            echo "Download catelyn jar..."
            downloadArtifact(TOKEN, "${discovery_repo}/catelyn/*******", './lib/catelyn-*******.jar')
            
            echo "Download sansa plugin..."
            downloadArtifact(TOKEN, "${discovery_repo}/sansa/*******", './plugins/sansa-*******.jar')

            echo "Dowbload robb plugin..."
            downloadArtifact(TOKEN, "${discovery_repo}/robb/0.1.0-SNAPSHOT", './plugins/robb-0.1.0-SNAPSHOT.jar')

            echo "Download arya plugin..."
            downloadArtifact(TOKEN, "${discovery_repo}/arya/0.1.0-SNAPSHOT", './plugins/arya-0.1.0-SNAPSHOT.jar')

            echo "Download jcl-discovery plugin..."
            downloadArtifact(TOKEN, "${discovery_repo}/jcl-discovery/plugin/*******", './plugins/jcl-discovery-*******.jar')
           }
         }
      }

      stage('Build') {
         steps {
            sh """
            echo "Building"
            mvn compile
            """
         }
      }
      
      stage('Test') {
         environment {
            scannerHome = tool 'SonarQubeScanner'
         }
         steps {
            withSonarQubeEnv('ZDSSonarQube') { 
               echo "Testing & Analyzing"
               sh "mvn org.jacoco:jacoco-maven-plugin:prepare-agent -Dmaven.test.failure.ignore=false -Dproject.version=${env.VERSION}-${buildTime} package sonar:sonar"
            }
         }
      }
      
      stage('Docker Build') {
         steps {
            script {
                echo "Docker Build"

                def buildTag = buildTime
                if (env.BRANCH_NAME == 'release'){
                    buildTag = buildVersion
                }

                sh """
                docker build -f Dockerfile -t ${artifactoryDockerRegistry}/sys-nazare-cicd-team-wazi-discovery-docker-local/discovery/zrdds-core:${buildTime} .
                docker build -f standalone/Dockerfile -t ${artifactoryDockerRegistry}/sys-nazare-cicd-team-wazi-discovery-docker-local/discovery/zrdds-core-standalone:${buildTag} .
                """
            }
         }

      }
      stage('Archive') {
         steps {
            script {
               if (env.BRANCH_NAME ==~ /(release|develop|v\d+(\.\d+)*-FP\d+)/) {
                  archiveArtifacts 'target/*.jar'
                  //archiveArtifacts 'target/*.tar.gz'

                  def uploadSpec = """{
                  "files": [
                        {
                           "pattern": "target/*.tar.gz",
                           "target": "sys-nazare-cicd-ext-maven-local/${uploadSubFolder}/"
                        }
                     ]
                  }"""
                  jfServer.upload spec: uploadSpec
               
                  def rtDocker = Artifactory.docker server: jfServer
                  rtDocker.addProperty("project-name", "discovery").addProperty("status", "dev")

                  def buildInfo = rtDocker.push "${artifactoryDockerRegistry}/sys-nazare-cicd-team-wazi-discovery-docker-local/discovery/zrdds-core:${buildTime}", 'sys-nazare-cicd-team-wazi-discovery-docker-local'
                  //jfServer.publishBuildInfo buildInfo

                  if (env.BRANCH_NAME == 'release'){
                     buildInfo = rtDocker.push "${artifactoryDockerRegistry}/sys-nazare-cicd-team-wazi-discovery-docker-local/discovery/zrdds-core-standalone:${buildVersion}", 'sys-nazare-cicd-team-wazi-discovery-docker-local'
                  } else{
                     buildInfo = rtDocker.push "${artifactoryDockerRegistry}/sys-nazare-cicd-team-wazi-discovery-docker-local/discovery/zrdds-core-standalone:${buildTime}", 'sys-nazare-cicd-team-wazi-discovery-docker-local'
                  }
                  //jfServer.publishBuildInfo buildInfo

               } else {
                  echo "Archive stage is skipped for feature branches!"
               }
            }
         }
      }

      stage ('Deploy') {
         steps {
            script {
               if (env.BRANCH_NAME == 'develop') {
                  def playbookFileName = "${env.COMPONENT}-dev.yml"

                  sh "mkdir -p automation"
                  dir("automation") {
                     git credentialsId: '2253d4dc-49c4-4083-b442-f1967c61b3f7', url: 'https://github.ibm.com/palantir/automation.git'
                     dir("deploy") {
                        withCredentials([string(credentialsId: '32689d01-67b8-4306-a71a-7e416449d52a', variable: 'TOKEN')]) {
                           sh "ansible-playbook ${playbookFileName} -i hosts --extra-vars \"artifactory_api_key=${TOKEN}\""
                        }
                     }
                  }
               } else {
                  // echo "UCD deployment for feature branches..."

                  // step([$class: 'UCDeployPublisher',
                  // siteName: 'udep05',
                  // component: [
                  //    $class: 'com.urbancode.jenkins.plugins.ucdeploy.VersionHelper$VersionBlock',
                  //    componentName: 'ND-Backend',
                  //    createComponent: [
                  //       $class: 'com.urbancode.jenkins.plugins.ucdeploy.ComponentHelper$CreateComponentBlock'
                  //    ],
                  //    delivery: [
                  //          $class: 'com.urbancode.jenkins.plugins.ucdeploy.DeliveryHelper$Push',
                  //          pushVersion: "${buildTime}",
                  //          baseDir: "${env.WORKSPACE}/target",
                  //          fileIncludePatterns: '*tar.gz',
                  //          fileExcludePatterns: '',
                  //          pushProperties: 'jenkins.server=Local\njenkins.reviewed=false',
                  //          pushDescription: 'Pushed from Jenkins Pipeline',
                  //          pushIncremental: false
                  //    ],
                  // ]
                  // ])
                  // step([$class: 'UCDeployPublisher',
                  //       siteName: 'udep05',
                  //       deploy: [
                  //          $class: 'com.urbancode.jenkins.plugins.ucdeploy.DeployHelper$DeployBlock',
                  //          deployApp: 'Nazare-Discovery',
                  //          deployEnv: 'Experimental Box',
                  //          deployProc: 'Deploy ND-Backend',
                  //          deployVersions: "ND-Backend:${buildTime}",
                  //          deployOnlyChanged: false
                  //    ]
                  // ])
               }
            }
         }
      }

      stage('APITest'){
         steps{
            script{
               def discovery_url = ""               
               if (env.BRANCH_NAME == 'develop') {
                  discovery_url = "disco-dev1.fyre.ibm.com"
                  dir("automation") {
                     sleep 30                        
                     sh "newman run ./testing/API/DataSeedAPI.json --env-var \"discovery-url=${discovery_url}\" -k"
                  }               
               } else if (env.BRANCH_NAME == 'release') {
                    echo " Require machine to do API test for release branch. "
               } else {
                   echo " Skip over API test for feature branches..."
               }
            }
         }
      }
   }

   post {
      success {
         echo "Build ${buildTime} passed"
         notifyBuild(buildTime, "SUCCESS")
         deleteDir()         
      }
      failure {
         echo "Build ${buildTime} failed" 
         notifyBuild(buildTime, "FAILED")
         deleteDir()
      }
   }
}

// Download artifacts
def downloadArtifact(String token, String downloadUrl, String savePath) {
   sh """
   ARTIFACT_URI=\$(curl -s -H 'Authorization: Bearer $token' $downloadUrl?lastModified | jq -r '.uri')
   DOWNLOAD_URI=\$(curl -s -H 'Authorization: Bearer $token' \${ARTIFACT_URI} | jq -r '.downloadUri')
   curl -sSf -H 'Authorization: Bearer $token' \${DOWNLOAD_URI} -o '$savePath'
   """
}

// Notify slack for branch builds
def notifyBuild(String buildTime, String buildStatus = 'STARTED')
{
    // build status of null means successful
    buildStatus = buildStatus ?: 'SUCCESS'

    // Default values
    def colorName = 'RED'
    def colorCode = '#FF0000'
    def gitCommitMessage = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()

    def subject = "${env.JOB_NAME} - Build #${env.BUILD_NUMBER} : ${buildStatus}"
    def innerText = "Build started at: ${buildTime}"

    if (buildStatus == 'STARTED')
    {
        colorName = 'YELLOW'
        colorCode = '#FFFF00'
    }
    else if (buildStatus == 'SUCCESS')
    {
        colorName = 'GREEN'
        colorCode = '#00FF00'
    }
    else
    {
        colorName = 'RED'
        colorCode = '#FF0000'
    }

   def attachments = [
      [
         color: colorCode,
         pretext: 'Resource Discovery CI/CD',
         title: subject,
         title_link: env.BUILD_URL,
         text: innerText,
         fields: [[
               title: 'Component',
               value: env.COMPONENT,
               short: true
            ],
            [
               title: 'Branch',
               value: env.BRANCH_NAME,
               short: true
            ]],
         footer: 'Discovery Devops',
         footer_icon: 'https://platform.slack-edge.com/img/default_application_icon.png'
      ]
   ]

   slackSend(
      attachments: attachments
   )
}
