# Development
dev/
docs/
.git/
.gitignore
.idea/
.vscode/
*.iml
*.iws
*.ipr

# Logs
logs/
*.log

# Build artifacts - keep necessary files
target/
!target/*.tar.gz
!target/*.jar
build/
out/
bin/
.gradle/

# Test files
test/
**/test/
**/tests/
**/*Test.java
**/*Tests.java

# Documentation
docs/
*.md
!README.md
!README.processes.md

# Configuration
.env
.env.*
*.properties
!application.properties
!application-*.properties

# Editor files
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Debug files
*.hprof
heapdump*
*.dump
*.trace
