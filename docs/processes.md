# Eddard Processes Documentation

> For general setup and configuration instructions, see [README.md](README.md).

This document describes the available processes in Eddard and their configurations.

## Available Processes

### 1. CopyIntoOrient

```json
{
  "id": 0,
  "process": "CopyIntoOrient",
  "invoke": [{
    "plugin": "sansa",
    "pipeline": "CopyIntoOrient",
    "persist": ["0"]  // Uses Graph (OrientDB) persistence
  }]
}
```

- **Purpose**: Copies data from PostgreSQL to OrientDB
- **Plugin**: Sansa
- **Persistence**: OrientDB (persist id "0")

### 2. PopulateDLA

```json
{
  "id": 1,
  "process": "PopulateDLA",
  "invoke": [{
    "plugin": "sansa",
    "pipeline": "PopulateDLA"
  }]
}
```

- **Purpose**: Processes IDML books and stores in PostgreSQL
- **Plugin**: Sansa
- **Persistence**: No explicit persistence (uses default PostgreSQL)

### 3. TransformDLAToKafka

```json
{
  "id": 2,
  "process": "TransformDLAToKafka",
  "invoke": [
    {
      "pipeline": "PopulateDLA"  // First step
    },
    {
      "pipeline": "ExportDLAToKafka",  // Second step
      "persist": ["1"]  // Uses Kafka persistence
    }
  ]
}
```

- **Purpose**: Two-step process
  1. Populates DLA data
  2. Exports to Kafka
- **Plugin**: Sansa
- **Persistence**: Kafka (persist id "1")

### 4. ExportDLAFromDBToKafka

```json
{
  "id": 3,
  "process": "ExportDLAFromDBToKafka",
  "invoke": [{
    "pipeline": "ExportDLAToKafka",
    "persist": ["1"]  // Uses Kafka persistence
  }]
}
```

- **Purpose**: Exports existing DLA data from DB to Kafka
- **Plugin**: Sansa
- **Persistence**: Kafka (persist id "1")

### 5. DetectExpiredDLAItems

```json
{
  "id": 4,
  "process": "DetectExpiredDLAItems",
  "invoke": [{
    "pipeline": "DetectExpiredDLAItems"
  }]
}
```

- **Purpose**: Identifies expired DLA items
- **Plugin**: Sansa
- **Persistence**: No explicit persistence

### 6. PopulateDeltaDLA

```json
{
  "id": 5,
  "process": "PopulateDeltaDLA",
  "invoke": [{
    "pipeline": "PopulateDeltaDLA"
  }]
}
```

- **Purpose**: Processes incremental DLA updates
- **Plugin**: Sansa
- **Persistence**: No explicit persistence

## Configuration Structure

1. `extract`: Defines data sources (empty in this config)

2. `persist`: Defines storage targets
   - `"0"`: OrientDB configuration
   - `"1"`: Kafka configuration

3. `process`: Defines available processes
   Each process has:
   - `id`: Unique identifier
   - `version`: Process version
   - `schedule`: Scheduling configuration
   - `invoke`: List of pipeline steps
     - `plugin`: Plugin name (e.g., "sansa")
     - `pipeline`: Specific pipeline to run
     - `extract`: Input sources
     - `persist`: Output targets

## Process Flow

1. When a process is triggered:
   - ProcessController receives request
   - ProcessManager loads configuration
   - PipelineManager executes pipelines

2. Pipeline Execution:
   - Loads specified plugin (e.g., "sansa")
   - Executes pipeline in plugin
   - Uses configured persistence

3. Data Flow:
   - Input: IDML books or database
   - Processing: Through specified pipeline
   - Output: To configured persistence (PostgreSQL, OrientDB, or Kafka)

## Main Plugin Pipelines

The main plugin "sansa" implements these pipelines:

- `PopulateDLA`: Process IDML books
- `CopyIntoOrient`: Copy to OrientDB
- `ExportDLAToKafka`: Export to Kafka
- `DetectExpiredDLAItems`: Check expiration
- `PopulateDeltaDLA`: Process incremental updates
