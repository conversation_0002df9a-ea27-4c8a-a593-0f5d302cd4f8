error_log /usr/local/nginx/logs/messages.log info;
worker_processes auto;

events {
}

http {
    # General configuration options
    ssl_session_cache   shared:SSL:10m;
    ssl_session_timeout 10m;
    access_log          /usr/local/nginx/logs/access.log;
    # Reverse proxy configuration
    server {
        listen              __GATEWAY_PORT__ ssl;
        server_name         __GATEWAY_HOST__;
        keepalive_timeout   70;
        ssl_password_file   /usr/local/nginx/ssl_pwd;
        ssl_certificate     /usr/local/nginx/ssl/zdap.crt;
        ssl_certificate_key /usr/local/nginx/ssl/zdap.key;
        ssl_protocols       TLSv1.2;

        location /oauth2/ {
            proxy_pass          http://oauth2:4180;
            proxy_set_header    Host                    $host;
            proxy_set_header    X-Real-IP               $remote_addr;
            proxy_set_header    X-Scheme                $scheme;
            proxy_set_header    X-Auth-Request-Redirect $request_uri;
        }
        location = /oauth2/auth {
            proxy_pass          http://oauth2:4180;
            proxy_set_header    Host                    $host;
            proxy_set_header    X-Real-IP               $remote_addr;
            proxy_set_header    X-Scheme                $scheme;
            proxy_set_header    X-Auth-Request-Redirect $request_uri;
            proxy_set_header    Content-Length          "";
            proxy_pass_request_body                     off;
        }
        location /insights/ {
            auth_request /oauth2/auth;
            error_page 401 = /oauth2/sign_in;
            # The following require oauth2-proxy to run with set_xauthrequest = true
            auth_request_set    $user                   $upstream_http_x_auth_request_user;
            auth_request_set    $email                  $upstream_http_x_auth_request_email;
            proxy_set_header    X-User                  $user;
            proxy_set_header    X-Email                 $email;
            # The following require oauth2-proxy to run with pass_access_token = true
            auth_request_set    $token                  $upstream_http_x_auth_request_access_token;
            proxy_set_header    X-Access-Token          $token;
            ## Handle cookies > 4kB
            #auth_request_set    $auth_cookie_name_upstream_1 $upstream_cookie_auth_cookie_name_1;
            #if ($auth_cookie ~* "(; .*)") {
            #    set $auth_cookie_name_0 $auth_cookie;
            #    set $auth_cookie_name_1 "auth_cookie_name_1=$auth_cookie_name_upstream_1$1";
            #}
            #if ($auth_cookie_name_upstream_1) {
            #    add_header Set-Cookie $auth_cookie_name_0;
            #    add_header Set-Cookie $auth_cookie_name_1;
            #}
            # The actual destination
            proxy_pass          http://dashboards:5601/;
        }
        location /datastore/ {
            # The actual destination
            proxy_pass          http://datastore:9200/;
        }
    }
}

