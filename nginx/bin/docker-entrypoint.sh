#!/bin/bash

# Set some environment variables
SSL_PASS=`echo "${ZDAP_PASS}" | base64 -d`
GATEWAY_PORT=${ZDAP_GATEWAY_PORT}
GATEWAY_HOST=${ZDAP_GATEWAY_HOST}
CONFIG_FILE="/etc/nginx.conf"
CONFIG_TMP="/tmp/config.tmp"
PATH=${PATH}:/usr/local/nginx/bin:/usr/local/nginx/sbin

# Set values where needed
echo ${SSL_PASS} > /usr/local/nginx/ssl_pwd

# Update config file from environment variables
cp ${CONFIG_FILE} ${CONFIG_TMP}
sed -i -e "s%__GATEWAY_PORT__%${GATEWAY_PORT}%g" ${CONFIG_TMP}
sed -i -e "s%__GATEWAY_HOST__%${GATEWAY_HOST}%g" ${CONFIG_TMP}
cat ${CONFIG_TMP} > ${CONFIG_FILE} && rm ${CONFIG_TMP}

nginx -c ${CONFIG_FILE} -g "daemon off;"
