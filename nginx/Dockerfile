##############################################################################################################
# Base Image
FROM registry.access.redhat.com/ubi8/ubi-minimal:8.10 AS builder

ARG NGINX_VER=see_.buildenv
ARG PCRE_VER=see_.buildenv
ARG OSSL_VER=see_.buildenv

# Build nginx
RUN microdnf update \
 && microdnf install -y xz tar gzip gcc gcc-c++ make perl zlib-devel findutils shadow-utils util-linux diffutils \
 && HERE=$( pwd ) \
 && curl -s -L https://github.com/openssl/openssl/releases/download/openssl-${OSSL_VER}/openssl-${OSSL_VER}.tar.gz -O \
 && curl -s -L https://github.com/PCRE2Project/pcre2/releases/download/pcre2-${PCRE_VER}/pcre2-${PCRE_VER}.tar.gz -O \
 && curl -s -L https://nginx.org/download/nginx-${NGINX_VER}.tar.gz -O \
 && curl -s -L https://github.com/besser82/libxcrypt/releases/download/v4.4.38/libxcrypt-4.4.38.tar.xz -O \
 && mkdir build \
 && tar xvf nginx-${NGINX_VER}.tar.gz -C build \
 && tar xvf pcre2-${PCRE_VER}.tar.gz -C build \
 && tar xvf openssl-${OSSL_VER}.tar.gz -C build \
 && tar xvf libxcrypt-4.4.38.tar.xz -C build \
 && cd build/nginx-${NGINX_VER} \
 && ./configure --with-threads --with-http_ssl_module --with-http_v2_module --with-http_realip_module --with-http_addition_module --with-http_sub_module --with-http_dav_module --with-http_gunzip_module --with-http_gzip_static_module --with-http_auth_request_module --with-http_secure_link_module --with-http_stub_status_module --with-stream --with-stream_realip_module --with-stream_ssl_module --with-stream_ssl_preread_module --with-pcre=../pcre2-${PCRE_VER} --with-openssl=../openssl-${OSSL_VER} \
 && make \
 && make install \
 # Build sqlite \
 && cd ${HERE} \
 && cd build/libxcrypt-4.4.38 \
 && ./configure \
 && make \
 # make test && \
 && make install

##############################################################################################################
# Final Image
FROM icr.io/zoa-oci/zoacommon-base-micro:8.10-x86_64

LABEL feature="IBM Z AIOps - Base Image"

COPY --from=builder /usr/local /usr/local
COPY --from=builder  --chown=1000:0 /usr/local/nginx /usr/local/nginx

RUN chown 1000:0 /usr/local/nginx && \
    cp /usr/local/lib/libcrypt* /usr/lib64

CMD ["bash"]
