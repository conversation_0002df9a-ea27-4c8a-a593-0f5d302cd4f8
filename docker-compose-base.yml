services:
  base_ubi8_micro:
    build:
      context: ..
      dockerfile: zoa-docker-base-images/base_ubi8_micro/Dockerfile
      args:
        OSSL_VER: ${OSSL_VER}
        CURL_VER: ${CURL_VER}
        PSL_VER: ${PSL_VER}
        GZIP_VER: ${GZIP_VER}
        TAR_VER: ${TAR_VER}
        ZLIB_VER: ${ZLIB_VER}
        GREP_VER: ${GREP_VER}
        GAWK_VER: ${GAWK_VER}
        FIND_VER: ${FIND_VER}
        SHADOW_VER: ${SHADOW_VER}
        SED_VER: ${SED_VER}
        BC_VER: ${BC_VER}
        BINUTILS_VER: ${BINUTILS_VER}
        TEXINFO_VER: ${TEXINFO_VER}
    image: icr.io/zoa-oci/zoacommon-base-micro:8.10-x86_64
    container_name: zoacommon-base
    hostname: base
  jre21:
    build:
      context: ..
      dockerfile: zoa-docker-base-images/jre21/Dockerfile
      args:
        OPENJDK_ARCH: ${OPENJDK_ARCH}
    image: icr.io/zoa-oci/zoacommon-jre21-micro:${J21TAG}-x86_64
    container_name: zoacommon-jre21
    hostname: jre21
  jdk21:
    build:
      context: ..
      dockerfile: zoa-docker-base-images/jdk21/Dockerfile
      args:
        OPENJDK_ARCH: ${OPENJDK_ARCH}
    image: icr.io/zoa-oci/zoacommon-jdk21-micro:${J21TAG}-x86_64
    container_name: zoacommon-jdk21
    hostname: jdk21
  nginx:
    build:
      context: ..
      dockerfile: zoa-docker-base-images/nginx/Dockerfile
      args:
        NGINX_VER: ${NGTAG}
        PCRE_VER: ${PCRE_VER}
        OSSL_VER: ${OSSL_VER}
    image: icr.io/zoa-oci/zoacommon-nginx-micro:${NGTAG}-x86_64
    container_name: zoacommon-nginx
    hostname: nginx
  python:
    build:
      context: ..
      dockerfile: zoa-docker-base-images/python/Dockerfile
      args:
        PYTHON_VER: ${PYTAG}
        OSSL_VER: ${OSSL_VER}
    image: icr.io/zoa-oci/zoacommon-python-micro:${PYTAG}-x86_64
    container_name: zoacommon-python
    hostname: python
  mvn21:
    build:
      context: ..
      dockerfile: zoa-docker-base-images/mvn21/Dockerfile
      args:
        OPENJDK_ARCH: ${OPENJDK_ARCH}
        MAVEN_VER: ${MVN21_VER}
    image: icr.io/zoa-oci/zoatools-maven:${MVN21_VER}-x86_64
    container_name: zoatools-maven
    hostname: maven
  gcc:
    build:
      context: ..
      dockerfile: zoa-docker-base-images/gcc/Dockerfile
      args:
        GCC_VER: ${GCC_VER}
    image: icr.io/zoa-oci/zoatools-gcc:${GCC_VER}-x86_64
    container_name: zoatools-gcc
    hostname: gcc
