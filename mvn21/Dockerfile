FROM registry.access.redhat.com/ubi8/ubi-minimal:8.10

ARG OPENJDK_ARCH=see_.buildenv
ARG MAVEN_VER=see_.buildenv

LABEL feature="IBM Z AIOps - Base Image"

RUN microdnf update && \
    microdnf install -y git tar gzip findutils && \
    microdnf clean all && \
    curl -s -L https://github.com/ibmruntimes/semeru21-binaries/releases/download/jdk-@@J21TAG_ESCAPED@@_openj9-@@OPEN_J9_BUILD@@/ibm-semeru-open-jdk_${OPENJDK_ARCH}_linux_@@J21TAG@@_openj9-@@OPEN_J9_BUILD@@.tar.gz -O && \
    mkdir -p /opt/java && \
    tar xvf ibm-semeru-open-jdk_${OPENJDK_ARCH}_linux_@@J21TAG@@_openj9-@@OPEN_J9_BUILD@@.tar.gz -C /opt/java && \
    rm -f ibm-semeru-open-jdk_${OPENJDK_ARCH}_linux_@@J21TAG@@_openj9-@@OPEN_J9_BUILD@@.tar.gz && \
    mv /opt/java/jdk-@@J21TAG_PLUS@@ /opt/java/openjdk && \
    mkdir /opt/java/.scc && \
    chmod 777 /opt/java/.scc && \
    curl -s -L https://archive.apache.org/dist/maven/maven-3/${MAVEN_VER}/binaries/apache-maven-${MAVEN_VER}-bin.tar.gz -O && \
    mkdir -p /opt/java && \
    tar xvf apache-maven-${MAVEN_VER}-bin.tar.gz -C /opt/java && \
    rm -f apache-maven-${MAVEN_VER}-bin.tar.gz && \
    mv /opt/java/apache-maven-${MAVEN_VER} /opt/java/maven

ENV JAVA_HOME=/opt/java/openjdk
ENV PATH=$JAVA_HOME/bin:/opt/java/maven/bin:$PATH
ENV JAVA_TOOL_OPTIONS="-XX:+IgnoreUnrecognizedVMOptions -XX:+IdleTuningGcOnIdle -Xshareclasses:name=openj9_system_scc,cacheDir=/opt/java/.scc,nonFatal"
ENV JAVA_VERSION=jdk-@@J21TAG_PLUS@@_openj9-@@OPEN_J9_BUILD@@

CMD ["bash"]
