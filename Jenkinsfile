// Using Scripted Pipeline for maximum flexibility 

// This is a custom data structure we'll use to define our parallel builds:
List<StageDef> stageDefs = [
        new StageDef("s390x"),
        new StageDef("x86_64")]

// The 'branches' structure is a map from branch name to branch code. This is the
// argument we'll give to the 'parallel' build step later:
def branches = [:]
def lastSuccessfulBuild(buildsNotPassed, build) {
    if ((build != null) && (build.result != 'SUCCESS')) {
        buildsNotPassed.add(build)
        lastSuccessfulBuild(buildsNotPassed, build.getPreviousBuild())
    }
}

properties ([
  [ $class: 'BuildDiscarderProperty', strategy:
    [ $class: 'LogRotator',
        artifactDaysToKeepStr: '10',
        artifactNumToKeepStr: '10',
        daysToKeepStr: '10',
        numToKeepStr: '10'
    ]
  ]
])

@NonCPS
def getChangeLog(buildsNotPassed) {
    def log = ""
    for (int x = 0; x < buildsNotPassed.size(); x++) {
        def currentBuild = buildsNotPassed[x];
        def changeLogSets = currentBuild.changeSets
        for (int i = 0; i < changeLogSets.size(); i++) {
            def entries = changeLogSets[i].items
            for (int j = 0; j < entries.length; j++) {
                def entry = entries[j]
                def date = new Date(entry.timestamp)
                def sdf = date.format("yyyy-MM-dd hh:mm:ss").toString()
                // Exclude commits by functional ID since they don't reflect real code changes
                // Nobody checking in real code changes has any business using this ID
                String entryAuthor = entry.author
                String entryMsg = entry.msg
                if (entryAuthor.equals('c-nx1u897') && entryMsg.startsWith('Build ID')) {
                    echo "Discarding non-functional commit ${entry.commitId}."
                } else {
                    log += "* ${entry.commitId} ${entry.msg} (${entry.author} @ ${sdf}) \n"
                }
            }
        }
    }
    return log;
}

buildsNotPassed = []
lastSuccessfulBuild(buildsNotPassed, currentBuild);
echo "Number of reviewed builds: " + buildsNotPassed.size()
echo "List of reviewed builds:"
for (int i = 0; i < buildsNotPassed.size(); i++) {
    echo "    #" + buildsNotPassed[i].number
}
// Loop through the stage definitions and define the parallel stages:
for (stageDef in stageDefs) {

    // Never inline this!
    String interp = stageDef.interp

    String gitOrg = "IZOA"
    String gitRepo = "zoa-docker-base-images"
    String branchName = "Build ZOA common base images for ${interp}"
    String labelName = "docker-${interp}-backup"
    String gitBranch = "${env.BRANCH_NAME}"
    String fallbackBranch = "develop"
    String buildId = "${env.BUILD_NUMBER}"
    String shortBranch = gitBranch.replaceAll(/[^a-zA-Z0-9]/, '').toLowerCase()
    String version = "test-" + shortBranch.substring(0, (shortBranch.length() > 10) ? 10 : shortBranch.length())
    String vrmf = "5.1.1-dev"
    if (gitBranch.equals("develop")) {
      version = "latest"
      labelName = "docker-${interp}"
    }
    if (gitBranch ==~ /.*_release$/) {
        version = gitBranch.substring(0,4).replaceAll(/([0-9])([0-9])([0-9])([0-9])/, '$1.$2.$3-$4')
        labelName = "docker-${interp}"
    }
    if (gitBranch.equals("service_v5.1.0")) {
        version = "5.1.1-22"
        vrmf = "5.1.1-22"
        fallbackBranch = gitBranch
        labelName = "docker-${interp}"
    }
    def nowDate = new Date()
    final buildDate = nowDate.format('yyyyMMdd-HHmm')
    print("Label name is ${labelName}.")
    print("Git branch is ${gitBranch}.")
    print("Version is ${version}.")
    print("Jenkins build ID is ${buildId}.")
    print("Build date is ${buildDate}.")

    branches[branchName] = {

        // Start the branch with a node definition. We explicitly exclude the
        // master node, so only the two slaves will do builds:
        node(labelName) {
            def workspace = env.WORKSPACE
            print("Current workspace is ${workspace}.")
            String targetDir = 'GITWORK/' + gitRepo
            sh(script: 'rm -Rf GITWORK && mkdir -p ' + targetDir, returnStdout: true)
            checkout([
                    $class: 'GitSCM',
                    branches: [[name: 'refs/heads/' + gitBranch]],
                    extensions: [
                            [$class: 'RelativeTargetDirectory', relativeTargetDir: targetDir],
                            [$class: 'LocalBranch', localBranch: "**"]
                    ],
                    userRemoteConfigs: [[url: '******************:' + gitOrg + '/' + gitRepo + '.git']]
            ])
            def changeLog = getChangeLog(buildsNotPassed)
            echo "Change log since last successful build:"
            echo "---------------------------------------"
            echo "\'${changeLog}\'"
            // First build cannot determine a 'last successful' build and must always run to establish a baseline
            // VBS: Build base images every night to ensure we get latest ubi-minimal
            // if ((changeLog == '') && (currentBuild.number > 1)) {
            //    echo "No changes found; will not run build."
            if (0 > 1) {
                echo "Pigs have learned how to fly; this build is no longer needed."
                currentBuild.result = 'NOT_BUILT'
            } else {
                sh(script: 'rm -Rf GITWORK && mkdir -p ' + targetDir, returnStdout: true)
                sh(script: 'cd GITWORK && <NAME_EMAIL>:' + gitOrg + '/' + gitRepo + '.git -b ' + gitBranch, returnStdout: true)
                def buildProps = readProperties file: workspace + '/GITWORK/' + gitRepo + '/.buildenv'
                withEnv(["GITBRANCH=" + gitBranch,
                        "FALLBACK_BRANCH=" + fallbackBranch,
                        "ARCH=" + interp,
                        "VER=" + version,
                        "VRMF=" + vrmf,
                        "J21TAG=" + buildProps['J21TAG'],
                        "OPEN_J9_BUILD=" + buildProps['OPEN_J9_BUILD'],
                        "NGTAG=" + buildProps['NGTAG'],
                        "OPENSSL_VER=" + buildProps['OSSL_VER'],
                        "CURL_VER=" + buildProps['CURL_VER'],
                        "PSL_VER=" + buildProps['PSL_VER'],
                        "GZIP_VER=" + buildProps['GZIP_VER'],
                        "TAR_VER=" + buildProps['TAR_VER'],
                        "PCRE_VER=" + buildProps['PCRE_VER'],
                        "ZLIB_VER=" + buildProps['ZLIB_VER'],
                        "GREP_VER=" + buildProps['GREP_VER'],
                        "GAWK_VER=" + buildProps['GAWK_VER'],
                        "FIND_VER=" + buildProps['FIND_VER'],
                        "SHADOW_VER=" + buildProps['SHADOW_VER'],
                        "SED_VER=" + buildProps['SED_VER'],
                        "BC_VER=" + buildProps['BC_VER'],
                        "TEXINFO_VER=" + buildProps['TEXINFO_VER'],
                        "GCC_VER=" + buildProps['GCC_VER'],
                        "BINUTILS_VER=" + buildProps['BINUTILS_VER'],
                        "PYTAG=" + buildProps['PYTAG'],
                        "MAVEN_VER=" + buildProps['MAVEN_VER'],
                        "MVN21_VER=" + buildProps['MVN21_VER'],
                        "BUILD_DATE=" + buildDate,
                        "BUILD_ID=" + buildId
                ]) {
                    stage("Update GitHub repo content on ${interp}") {
                        try {
                            // For Linux on Z, update architecture suffixes for Docker image names
                            sh '''
                                J21TAG_PLUS=`echo ${J21TAG} | tr '_' '+'`
                                J21TAG_ESCAPED=`echo ${J21TAG} | sed -e "s|_|%2B|g"`
                                for DFILE in `find -name Dockerfile*`
                                do
                                    sed -i -e "s%@@J21TAG@@%${J21TAG}%g" ${DFILE}
                                    sed -i -e "s%@@OPEN_J9_BUILD@@%${OPEN_J9_BUILD}%g" ${DFILE}
                                    sed -i -e "s%@@J21TAG_PLUS@@%${J21TAG_PLUS}%g" ${DFILE}
                                    sed -i -e "s|@@J21TAG_ESCAPED@@|${J21TAG_ESCAPED}|g" ${DFILE}
                                done
                                if [ "${ARCH}" = "s390x" ]
                                then
                                    echo "Building for s390x; need to update architecture suffixes for Docker image names."
                                    cd GITWORK/zoa-docker-base-images
                                    for DFILE in `find -name Dockerfile*`
                                    do
                                        sed -i -e "s%-x86_64$%-s390x%g" ${DFILE}
                                        sed -i -e "s%-x86_64\\ AS%-s390x\\ AS%g" ${DFILE}
                                        sed -i -e "s%@@J21TAG@@%${J21TAG}%g" ${DFILE}
                                        sed -i -e "s%@@OPEN_J9_BUILD@@%${OPEN_J9_BUILD}%g" ${DFILE}
                                        sed -i -e "s%@@J21TAG_PLUS@@%${J21TAG_PLUS}%g" ${DFILE}
                                        sed -i -e "s|@@J21TAG_ESCAPED@@|${J21TAG_ESCAPED}|g" ${DFILE}
                                    done
                                    for DCFILE in *docker-compose*yml
                                    do
                                        sed -i -e "s%-x86_64$%-s390x%g" ${DCFILE}
                                    done
                                else
                                    echo "Building for x86_64; no Dockerfile customization required."
                                fi
                            '''
                        }
                        catch (ignored) {
                            unstable(message: "'${STAGE_NAME}' failed.")
                            currentBuild.result = 'FAILURE'
                        }
                    }
                    stage("Create OCI base images on ${interp}") {
                        try {
                            // Purge docker builder cache
                            sh '''
                                set +e
                                docker builder prune -f
                                true
                            '''
                            // Clean up any containers left behind from prior work
                            sh '''
                                set +e
                                CONTAINER_LIST=`docker ps -a -q`
                                if [ "${CONTAINER_LIST}x" != "x" ]
                                then
                                  docker rm -vf $(docker ps -a -q)
                                fi
                                true
                            '''
                            // Clean up any images left behind from prior work
                            sh '''
                                set +e
                                IMAGE_LIST=`docker images -a -q`
                                if [ "${IMAGE_LIST}x" != "x" ]
                                then
                                  docker rmi -f $(docker images -a -q)
                                fi
                                true
                            '''
                            // Launch docker compose build process
                            sh '''
                                export PATH=/usr/local/bin:/usr/local/sbin:${PATH}
                                cd GITWORK
                                THISDIR=`pwd`
                                export TAG=${VER}
                                export J21TAG
                                export OPEN_J9_BUILD
                                export NGTAG
                                export PYTAG
                                export VRMF
                                export OSSL_VER=${OPENSSL_VER}
                                export CURL_VER
                                export PSL_VER
                                export GZIP_VER
                                export TAR_VER
                                export PCRE_VER
                                export ZLIB_VER
                                export GREP_VER
                                export GAWK_VER
                                export FIND_VER
                                export SHADOW_VER
                                export SED_VER
                                export TEXINFO_VER
                                export GCC_VER
                                export BINUTILS_VER
                                export BC_VER
                                export MAVEN_VER
                                export MVN21_VER
                                if [ "${ARCH}" = "x86_64" ]
                                then
                                  export OPENJDK_ARCH="x64"
                                else
                                  export OPENJDK_ARCH=${ARCH}
                                fi
                                cd zoa-docker-base-images
                                echo "Creating base images..."
                                docker compose -f docker-compose-base.yml build base_ubi8_micro
                                docker compose -f docker-compose-base.yml build jre21
                                docker compose -f docker-compose-base.yml build jdk21
                                docker compose -f docker-compose-base.yml build nginx
                                docker compose -f docker-compose-base.yml build python
                                docker compose -f docker-compose-base.yml build mvn21
                                echo ""
                            '''
                        }
                        catch (ignored) {
                            unstable(message: "'${STAGE_NAME}' failed.")
                            currentBuild.result = 'FAILURE'
                        }
                    }
                    stage("Publish OCI base images for ${interp} to Artifactory") {
                        try {
                          withCredentials([string(credentialsId: 'zldazaadevfunc',
                                variable: 'IBMCLOUD_API_KEY')]) {
                            sh '''
                              ibmcloud login -r us-east
                              ibmcloud cr login --client docker
                              set +x
                              . ${HOME}/.af
                              set -x
                              for IMG in $(docker images -f "label=feature=IBM Z AIOps - Base Image" -q)
                              do
                                IMGINFO=`docker inspect --format=\'{{.RepoTags}}\' ${IMG} | tr -d \'[]\'`
                                if [ "${IMGINFO}x" = "x" ]
                                  then echo "No image info found for ${IMG}."
                                else
                                  for IMAGE in ${IMGINFO}
                                  do
                                    docker push ${IMAGE}
                                  done
                                fi
                              done
                            '''
                          }
                        }
                        catch (ignored) {
                            unstable(message: "'${STAGE_NAME}' failed.")
                            currentBuild.result = 'FAILURE'
                        }
                    }
                }
            }
        }
    }
}

parallel branches

class StageDef implements Serializable {

    String interp

    StageDef(final String interp) {
        this.interp = interp
    }
}
