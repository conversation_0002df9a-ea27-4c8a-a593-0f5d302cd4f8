#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2025
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            clear_postgres.sh
#
# Description:     IBM Z Resources Discovery Data Service
#
# Product version: v1.4.0, v1.4.1, v1.4.2
#
# Purpose:         <PERSON><PERSON><PERSON> used to clear all the databases used with ZRDDS
#
# Syntax:          clear_postgres.sh
#
#
# MAINTENANCE:
#
#--------------------------------------------------------------------------



# Color definitions
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color



CONTAINER_NAME="zrdds-postgresql"
DATABASE_NAME="discovery"



#Verify user input
echo -e "${RED}Clearing PostgreSQL database...All actions are irreversible${NC}"
echo -e "${RED}Are you sure you want to clear all the PostgreSQL database used with ZRDDS? (y/n)${NC}"
read input

if [ "$input" != "y" ] && [ "$input" != "Y" ]; then
    echo "Aborting..."
    exit 1
fi

# Clearing PostgreSQL
echo "Clearing PostgreSQL database..."

# Truncating all tables
TRUNCATE_SCRIPT=$(cat <<EOF
DO
\$\$
DECLARE
  l_stmt text;
BEGIN
  SELECT 'TRUNCATE ' || string_agg(format('%I.%I', schemaname, tablename), ',') || ' CASCADE'
    INTO l_stmt
  FROM pg_tables
  WHERE schemaname IN ('public');

  IF l_stmt IS NOT NULL THEN
    EXECUTE l_stmt;
    RAISE NOTICE 'All tables truncated successfully';
  ELSE
    RAISE NOTICE 'No tables found to truncate';
  END IF;
END;
\$\$;
EOF
)

echo "Truncating all tables in the ${DATABASE_NAME} database..."
docker exec -i ${CONTAINER_NAME} psql -U postgres -d ${DATABASE_NAME} -c "${TRUNCATE_SCRIPT}"

if [ $? -eq 0 ]; then
  echo "All tables truncated successfully."
else
  echo "Error truncating tables."
  exit 1
fi