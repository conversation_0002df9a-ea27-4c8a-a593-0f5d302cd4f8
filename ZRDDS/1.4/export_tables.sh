#!/bin/bash

USER="dbzuser"
DB="discovery"
OUTPUT_DIR="/tmp/exported_tables"
mkdir -p "$OUTPUT_DIR"

# Get all tables excluding 'meta' and 'dla_data_trace'
tables=$(psql -U "$USER" -d "$DB" -t -c "
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
  AND table_type = 'BASE TABLE'
  AND table_name NOT ILIKE '%meta%'
  AND table_name NOT ILIKE '%dla_data_trace%';
")

for table in $tables; do
  table=$(echo "$table" | xargs)
  echo "Exporting: $table"
  exclude_columns=()

  # Conditional exclusions
  if [[ "$(psql -U "$USER" -d "$DB" -t -c "
    SELECT 1 FROM information_schema.columns
    WHERE table_name = '$table' AND column_name = 'kafka_send_date';
  " | xargs)" == "1" ]]; then
    exclude_columns+=("kafka_send_date")
  fi

  case "$table" in
    "relationship" | "relationship_service_now")
      exclude_columns+=("scan_date")
      ;;
    "file_trace")
      exclude_columns+=("failed_dlaids_count" "scan_date")
      ;;
  esac

  if [[ ${#exclude_columns[@]} -gt 0 ]]; then
    exclude_sql=$(printf "'%s'," "${exclude_columns[@]}")
    exclude_sql=${exclude_sql%,}
    columns=$(psql -U "$USER" -d "$DB" -t -c "
      SELECT string_agg('\"' || column_name || '\"', ', ')
      FROM information_schema.columns
      WHERE table_name = '$table'
        AND column_name NOT IN ($exclude_sql);
    " | xargs)
  else
    columns="*"
  fi

  if [[ "$columns" == "*" ]]; then
    query="SELECT * FROM public.\"$table\""
  else
    query="SELECT $columns FROM public.\"$table\""
  fi

  psql -U "$USER" -d "$DB" -c "COPY ($query) TO STDOUT WITH CSV HEADER" > "$OUTPUT_DIR/$table.csv"
  echo "Backed up table: $table"
done

echo "All exports completed. Files saved in $OUTPUT_DIR"
