#!/bin/bash

USER="dbzuser"
DB="discovery"
CSV_DIR="/tmp/exported_tables"

if [ ! -d "$CSV_DIR" ]; then
  echo "CSV directory not found: $CSV_DIR"
  exit 1
fi

echo "Starting restore from CSV files in $CSV_DIR"

for csvfile in "$CSV_DIR"/*.csv; do
  table=$(basename "$csvfile" .csv)
  echo "Restoring table: $table"

  psql -U "$USER" -d "$DB" -c "\COPY public.\"$table\" FROM '$csvfile' WITH CSV HEADER"

  if [ $? -eq 0 ]; then
    echo "Restored: $table"

    # Reset sequence for 'file_trace'
    if [ "$table" == "file_trace" ]; then
      echo "Resetting sequence for $table"
      psql -U "$USER" -d "$DB" -c "
        SELECT setval(pg_get_serial_sequence('public.file_trace', 'id'), COALESCE(MAX(id), 1)) FROM public.file_trace;
      "
    fi
  else
    echo "Failed to restore: $table"
  fi
done

echo "Restore process completed."
