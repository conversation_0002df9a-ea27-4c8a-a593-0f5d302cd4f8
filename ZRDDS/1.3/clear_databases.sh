#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ANA, 5698-LDA (C) Copyright IBM Corp. 2025
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            clear_databases.sh
#
# Description:     IBM Z Resources Discovery Data Service
#
# Product version: v1.3.1.2
#
# Purpose:         <PERSON><PERSON><PERSON> used to clear all the databases used with ZRDDS
#
# Syntax:          clear_databases.sh
#
#
# MAINTENANCE:
#
#--------------------------------------------------------------------------



# Color definitions
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Pre-requisites

echo -e "${BLUE}The script must be run from within the docker container. It should be placed in the same directory as the zrdds-core folder${NC}"
echo -e "${BLUE}you can docker cp to transfer this script into your container${NC}"
echo "-----------------------------------------------------------"
echo "-----------------------------------------------------------"

echo -e "${BLUE}Do you meet these pre-requisites? (y/n)${NC}"
echo "-----------------------------------------------------------"
echo "-----------------------------------------------------------"
read input

if [ "$input" != "y" ] && [ "$input" != "Y" ]; then
    echo "Aborting..."
    exit 1
fi


#Verify user input
echo -e "${RED}Clearing databases...All actions are irreversible${NC}"
echo -e "${RED}Are you sure you want to clear all the databases used with ZRDDS? (y/n)${NC}"
read input

if [ "$input" != "y" ] && [ "$input" != "Y" ]; then
    echo "Aborting..."
    exit 1
fi






# Clearing OrientDB
echo "Clearing OrientDB database..."

# Get OrientDB password from environment
ORIENT_PASSWORD=$(echo ${ORIENT_TOKEN} | base64 -d | cut -f 2 -d ":")
if [ -z "$ORIENT_PASSWORD" ]; then
    ORIENT_PASSWORD="rootpwd"  # fallback to default
fi

# Fix OrientDB memory parameter issue
export ORIENTDB_OPTS_MEMORY="-Xms2G -Xmx2G"

# Connect to OrientDB and clear all data
/orientdb/bin/console.sh <<EOF
CONNECT remote:localhost/Demo root $ORIENT_PASSWORD
DELETE VERTEX V
EXIT
EOF


if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to clear OrientDB database${NC}"
    exit 1
fi

echo -e "${GREEN}OrientDB cleared successfully${NC}"

# Clearing PostgreSQL
echo "Clearing PostgreSQL database..."

# Clear PostgreSQL tables (keeping schema)
psql -U postgres -d discovery -c "
DO \$\$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
        EXECUTE 'TRUNCATE TABLE ' || quote_ident(r.tablename) || ' CASCADE';
    END LOOP;
END \$\$;
"


if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to clear PostgreSQL database${NC}"
    exit 1
fi

echo -e "${GREEN}PostgreSQL cleared successfully${NC}"
echo -e "${GREEN}All databases have been cleared!${NC}"