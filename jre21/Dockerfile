FROM icr.io/zoa-oci/zoacommon-base-micro:8.10-x86_64

ARG OPENJDK_ARCH=x64

LABEL feature="IBM Z AIOps - Base Image"

RUN curl -s -L https://github.com/ibmruntimes/semeru21-binaries/releases/download/jdk-@@J21TAG_ESCAPED@@_openj9-@@OPEN_J9_BUILD@@/ibm-semeru-open-jre_${OPENJDK_ARCH}_linux_@@J21TAG@@_openj9-@@OPEN_J9_BUILD@@.tar.gz -O && \
    mkdir -p /opt/java && tar xvf ibm-semeru-open-jre_${OPENJDK_ARCH}_linux_@@J21TAG@@_openj9-@@OPEN_J9_BUILD@@.tar.gz -C /opt/java && \
    rm -f ibm-semeru-open-jre_${OPENJDK_ARCH}_linux_@@J21TAG@@_openj9-@@OPEN_J9_BUILD@@.tar.gz && \
    mv /opt/java/jdk-@@J21TAG_PLUS@@-jre /opt/java/openjdk && \
    mkdir /opt/java/.scc && chmod 777 /opt/java/.scc && \
    chmod +x /usr/local/bin/wait-for-it.sh /usr/local/bin/changeLogLevel.sh
# COPY zoa-docker-base-images/jre17/java.security /opt/java/openjdk/conf/security/

ENV JAVA_HOME=/opt/java/openjdk
ENV PATH=$JAVA_HOME/bin:$PATH
ENV JAVA_TOOL_OPTIONS="-XX:+IgnoreUnrecognizedVMOptions -XX:+IdleTuningGcOnIdle -Xshareclasses:name=openj9_system_scc,cacheDir=/opt/java/.scc,nonFatal"
ENV JAVA_VERSION=jdk-@@J21TAG_PLUS@@_openj9-@@OPEN_J9_BUILD@@

CMD ["bash"]
