/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2024-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.pipeline;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import org.hibernate.Session;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.entity.manage.FileTrace;
import com.ibm.palantir.catelyn.jpa.repository.manage.FileTraceRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.sansa.exception.ServiceExceptionStore;
import com.ibm.palantir.sansa.extractor.ElementExtractor;
import com.ibm.palantir.sansa.parser.BaseInfoParser;
import com.ibm.palantir.sansa.parser.DLADataParser;
import com.ibm.palantir.sansa.parser.DLATraceParser;
import com.ibm.palantir.sansa.parser.DeltaDLAGenerator;
import com.ibm.palantir.sansa.utils.CommonUtils;
import com.ibm.palantir.sansa.utils.PipeCounter;

public class PopulateDeltaDLA extends AbstractPipeline {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();

    private static final String CLASSNAME = PopulateDeltaDLA.class.getSimpleName();
    private static final String REPONAME = "Sansa";
    private static final ServiceExceptionStore errorStore = ServiceExceptionStore.getInstance();
    private final int CACHE_SIZE = 10;
    private BaseInfoParser baseInfoParser = new BaseInfoParser();
    private PipeCounter totalCounter;
    private PipeCounter totalMetaCounter;
    private DeltaDLAGenerator deltaDLAGenerator;
    private CommonUtils commonUtils;
    private FileTraceRepository fileTraceRepository;
    private StringBuilder infoLog;
    private StringBuilder warnLog;
    private StringBuilder errorLog;

    public String run(PipelineConf pipelineConf) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", "Running PopulateDeltaDLA");
        String parameter = pipelineConf.getParameter();
        // Clear any previously stored errors
        errorStore.clear();
        String paramFormatErrorCode = ErrorCode.PluginParamFormatError.getCodeStr();
        String paramFormatErrorMsg = MessageFormat.format(MsgTemp.get(paramFormatErrorCode), "Sansa",
                "PopulateDeltaDLA", parameter);

        infoLog = new StringBuilder(); // For collecting info logs
        warnLog = new StringBuilder(); // For collecting warn logs
        errorLog = new StringBuilder(); // For collecting error logs

        if (parameter == null) {
            String errCode = ErrorCode.PluginNullParamError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "PopulateDeltaDLA");
            throw new ServiceException(errCode, msg + "Parameter passed to run the Delta mode is Null.", REPONAME,
                    CLASSNAME, "run");
        }

        JsonElement jsonElement = JsonParser.parseString(parameter);
        if (jsonElement.isJsonObject()) {
            JsonObject jsonObject = jsonElement.getAsJsonObject();

            if (!jsonObject.has("fileList")) {
                throw new ServiceException(paramFormatErrorCode,
                        paramFormatErrorMsg + "There is no fileList key in parameter json object", REPONAME, CLASSNAME,
                        "run");
            }

            JsonArray jsonArray = jsonObject.get("fileList").getAsJsonArray();

            try {
                this.totalCounter = new PipeCounter();
                this.totalMetaCounter = new PipeCounter();
                this.deltaDLAGenerator = new DeltaDLAGenerator();

                List<String> identicalFileList = new ArrayList<>();
                List<String> noTraceFileList = new ArrayList<>();
                List<String> failedFileList = new ArrayList<>();
                List<String> failedMetaFileList = new ArrayList<>();
                // Process the file list except for the ZOSTASK
                for (int i = 0; i < jsonArray.size(); i++) {
                    String filePath = jsonArray.get(i).getAsString();
                    this.commonUtils = new CommonUtils();
                    // skip the ZOSTASK file
                    if (filePath.toLowerCase().contains("zostask")) {
                        String logMessage = "Skip to parse delta data for the ZOSTASK: " + filePath;
                        logMessage(logMessage, infoLog, warnLog, errorLog, "info", "run");
                        continue;
                    }

                    ElementExtractor currentElementExtractor = new ElementExtractor(filePath);

                    // failed to create ElementExtractor
                    if (currentElementExtractor.isEmpty) {
                        failedFileList.add(filePath);
                        failedMetaFileList.add(filePath);
                        String logMessage = "Pipeline PopulateDeltaDLA failed to create ElementExtractor from "
                                + filePath;
                        logMessage(logMessage, infoLog, warnLog, errorLog, "warn", "run");
                        continue;
                    }
                    HashMap<String, String> baseInfo = baseInfoParser.extractBaseInfo(currentElementExtractor);
                    // failed to extract the baseInfo
                    if (baseInfo == null) {
                        failedFileList.add(filePath);
                        failedMetaFileList.add(filePath);
                        // Call the generic log method with appropriate log level and message
                        logMessage(String
                                .format("Pipeline PopulateDeltaDLA failed to extract base info from file:%s", filePath),
                                infoLog, warnLog, errorLog, "warn", "run");
                        continue;
                    }

                    // generate the delta book and save the scan date
                    String deltaBookPath = this.deltaDLAGenerator.generateDeltaBook(filePath,
                            currentElementExtractor.createTimestamp);

                    // identical, no delta
                    if (deltaBookPath == null) {
                        identicalFileList.add(filePath);
                        logMessage(
                                String.format("Update Meta tables scan_date and Skip process the identical file: %s",
                                        filePath),
                                infoLog,
                                warnLog, errorLog, "warn", "run");
                        updateMetaData(failedMetaFileList, filePath, currentElementExtractor, baseInfo);
                        continue;
                    }

                    // no FileTrace record,
                    if (deltaBookPath.equals("NoFileTraceRecord")) {
                        noTraceFileList.add(filePath);
                        logMessage(String.format(
                                "Skip process the unexpected filePath %s, which has no fileTrace record.", filePath),
                                infoLog, warnLog, errorLog, "warn", "run");
                        continue;
                    }

                    PipeCounter pipeCounter = null;
                    DLADataParser dlaDataParser = null;

                    if (deltaBookPath.isEmpty()) {
                        // If failed to generate the delta Book, parse the filePath directly
                        logMessage(String.format(
                                "The deltaBookPath is empty, go to parse the file %s directly in Full mode.",
                                filePath), infoLog, warnLog, errorLog, "info", "run");
                        dlaDataParser = new DLADataParser(currentElementExtractor, null,
                                null, null);
                        pipeCounter = dlaDataParser.populateDlaFromIdml(false, false);
                        logMessage(
                                String.format("Pipeline PopulateDeltaDLA finish paring the file in full mode.",
                                        filePath),
                                infoLog, warnLog, errorLog, "info", "run");
                    } else {
                        // Else parse the delta book with currentFile's baseInfo
                        logMessage(String.format("Go to parse the delta book %s with currentFile's base info.",
                                deltaBookPath), infoLog, warnLog, errorLog, "info", "run");
                        ElementExtractor deltaElementExtractor = new ElementExtractor(deltaBookPath);
                        // check the deltaElementExtractor is not empty
                        if (!deltaElementExtractor.isEmpty) { // In delta book if relationship has changed but CI hasn't
                                                              // change then we get the data but
                            // Use current file's base info to parse the delta data, especially the scanDate
                            ElementExtractor previousFileElementExtractor = commonUtils.setPreviousFileInfo(filePath,
                                    currentElementExtractor, infoLog, warnLog, errorLog);
                            if (previousFileElementExtractor != null && !previousFileElementExtractor.isEmpty) {
                                dlaDataParser = new DLADataParser(
                                        deltaElementExtractor, previousFileElementExtractor,
                                        baseInfo.get("cpcPrefixId"),
                                        baseInfo.get("zosPrefixId"),
                                        baseInfo.get("smfId"), baseInfo.get("lparName"), baseInfo.get("scanDate"),
                                        commonUtils.getPreviousFileCpcPrefixId(),
                                        commonUtils.getPreviousFileZosPrefixId());
                                pipeCounter = dlaDataParser.populateDlaFromIdml(true, false);
                                logMessage(String.format(
                                        "Pipeline PopulateDeltaDLA finish paring the file %s with base info in delta mode.",
                                        deltaBookPath), infoLog, warnLog, errorLog, "info", "run");
                            } else {
                                logMessage(String.format(
                                        "previousFileElementExtractor is Null or Empty in Delta mode, filepath is:  %s",
                                        filePath), infoLog, warnLog, errorLog, "warn", "run");
                            }

                        } else {
                            logMessage(String
                                    .format("deltaElementExtractor is Empty in Delta mode, filepath is:  %s", filePath),
                                    infoLog, warnLog, errorLog, "warn", "run");
                        }

                    }

                    if (!deltaBookPath.isEmpty()) {
                        updateMetaData(failedMetaFileList, filePath, currentElementExtractor, baseInfo);

                    }
                    updateTotalCounterAndTrace(failedFileList, filePath, currentElementExtractor, baseInfo,
                            pipeCounter, dlaDataParser, i);
                }
                logMetaDataProcess(failedMetaFileList);
                String respMsg = buildRespMsg(identicalFileList, noTraceFileList, failedFileList);
                // Throw all errors collected in the errorStore at the end of the PopulateDLA
                // process
                errorStore.throwIfAny();
                Session session = JPAManager.getJPAManager().getEntityManager().unwrap(Session.class);
                session.clear();
                logMessage("Clear JPA session cache.", infoLog, warnLog, errorLog, "info", "run");
                System.gc();
                return respMsg;

            } catch (Exception e) {
                logMessage(String.format("An unexpected error occurred during pipeline processing.", e),
                        infoLog, warnLog, errorLog, "error", "run");
                if (e instanceof ServiceException) {
                    // Throw ServiceException directly without replacing it with a generic message
                    throw e;
                }
                String errCode = ErrorCode.PluginError.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "PopulateDeltaDLA", parameter);
                logMessage(msg, infoLog, warnLog, errorLog, "error", "run");
                throw new ServiceException(errCode,
                        msg + "\nError Log: " + errorLog + "\nException message: " + e.toString(), REPONAME, CLASSNAME,
                        "run");

            }
        } else {
            String logMessage = "The parameter type is not json format!";
            logMessage(paramFormatErrorMsg + logMessage, infoLog, warnLog, errorLog, "error", "run");
            throw new ServiceException(paramFormatErrorCode,
                    paramFormatErrorMsg + "\nException message: " + logMessage, REPONAME, CLASSNAME, "run");
        }
    }

    private void updateMetaData(List<String> failedMetaFileList, String filePath,
            ElementExtractor currentElementExtractor, HashMap<String, String> baseInfo) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "updateMetaData", "Running updateMetaData");
        logMessage(String
                .format("Metadata update process during PopulateDeltaDLA pipeline.\\n" + //
                        " , go to parse the file %s directly.", filePath),
                infoLog, warnLog, errorLog, "info", "run");
        DLADataParser dlaDataParser = new DLADataParser(currentElementExtractor, null, baseInfo.get("cpcPrefixId"),
                baseInfo.get("zosPrefixId"),
                baseInfo.get("smfId"), baseInfo.get("lparName"), baseInfo.get("scanDate"), null, null);
        PipeCounter metaPipeCounter = dlaDataParser.populateDlaFromIdml(false, true);
        logMessage(String.format(
                "Finished Metadata update process during PopulateDeltaDLA pipeline, filepath is:  %s",
                filePath), infoLog, warnLog, errorLog, "info", "run");
        UpdateMetaTotalCounter(metaPipeCounter, failedMetaFileList, filePath);
    }

    private String response(String respMsg) {
        String noLogMessage = "No logs available";

        return String.format(
                "%nInfo Log: %n%s%n%nWarn Log: %n%s%n%nError Log: %n%s%n%nDelta Mode Completion Log: %n%s%n",
                getLogMessage(infoLog, noLogMessage),
                getLogMessage(warnLog, noLogMessage),
                getLogMessage(errorLog, noLogMessage),
                Objects.toString(respMsg, noLogMessage));
    }

    private String getLogMessage(Object log, String defaultMessage) {
        // If log is null or empty, return the default message
        if (log == null || log.toString().trim().isEmpty()) {
            return defaultMessage;
        }
        return log.toString();
    }

    private void updateTotalCounterAndTrace(List<String> failedFileList, String filePath,
            ElementExtractor currentElementExtractor, HashMap<String, String> baseInfo, PipeCounter pipeCounter,
            DLADataParser dlaDataParser, int i) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "updateTotalCounterAndTrace", "Running updateTotalCounterAndTrace");
        if (pipeCounter == null) {
            failedFileList.add(filePath); // failed to extract the IDML book
            return;
        } else {
            this.totalCounter.mergeCounter(filePath, pipeCounter);
            updateDlaTraceParser(currentElementExtractor, baseInfo, pipeCounter, dlaDataParser);
        }
        clearSession(i);
    }

    private void clearSession(int i) {
        if (i % CACHE_SIZE == (CACHE_SIZE - 1)) {
            Session session = JPAManager.getJPAManager().getEntityManager().unwrap(Session.class);
            session.clear();
            logMessage("Clear JPA session cache reaching cache size 10.", infoLog, warnLog, errorLog,
                    "info", "run");
        }
    }

    private String buildRespMsg(List<String> identicalFileList, List<String> noTraceFileList,
            List<String> failedFileList) {
        logMessage(this.totalCounter.getStrContent(), infoLog, warnLog, errorLog, "info", "run");

        StringBuilder respMsg = new StringBuilder("Finish PopulateDeltaDLA pipeline.\n ");
        respMsg.append(String.format(
                "Successfully generated delta and/or parsed %d files, saving %d objects data into the DB.\n ",
                this.totalCounter.countFile(), this.totalCounter.getTotal()));

        if (!failedFileList.isEmpty()) {
            String failedFilesMsg = String.format("Failed to extract %d files: %s.\n ", failedFileList.size(),
                    String.join(", ", failedFileList));
            logMessage(failedFilesMsg, infoLog, warnLog, errorLog, "warn", "run");
            respMsg.append(failedFilesMsg);
        }

        if (!totalCounter.getFailedDlaIds().isEmpty()) {
            String unexpectedObjectsMsg = String.format("There are %d unexpected objects, please check the details.\n ",
                    totalCounter.getFailedDlaIds().size());
            logMessage(unexpectedObjectsMsg, infoLog, warnLog, errorLog, "warn", "run");
            respMsg.append(unexpectedObjectsMsg);
        }

        if (!identicalFileList.isEmpty()) {
            String identicalFilesMsg = String.format("Skipped %d identical files: %s. \n ", identicalFileList.size(),
                    String.join(", ", identicalFileList));
            logMessage(identicalFilesMsg, infoLog, warnLog, errorLog, "warn", "run");
            respMsg.append(identicalFilesMsg);
        }

        if (!noTraceFileList.isEmpty()) {
            String noTraceMsg;
            if (noTraceFileList.size() == 1) {
                noTraceMsg = String.format("Skipped the unexpected filePath: %s because it has no fileTrace record. \n",
                        noTraceFileList.get(0));
            } else {
                noTraceMsg = String.format(
                        "Skipped %d unexpected filePaths: %s because they have no fileTrace record. \n",
                        noTraceFileList.size(), String.join(", ", noTraceFileList));
            }
            logMessage(noTraceMsg, infoLog, warnLog, errorLog, "warn", "run");
            respMsg.append(noTraceMsg);
        }

        logMessage(respMsg.toString(), infoLog, warnLog, errorLog, "info", "run");
        return respMsg.toString();
    }

    private void logMetaDataProcess(List<String> failedMetaFileList) {
        logMessage(this.totalMetaCounter.getStrContent(), infoLog, warnLog, errorLog, "info", "run");

        StringBuilder respMsg = new StringBuilder(String.format(
                "Finished all Metadata update process, parsed %d files and saved %d objects data into DB.\n ",
                this.totalMetaCounter.countFile(), this.totalMetaCounter.getTotal()));

        if (!failedMetaFileList.isEmpty()) {
            String failedFilesMsg = String.format("Failed to extract %d files: %s.\n ", failedMetaFileList.size(),
                    String.join(", ", failedMetaFileList));
            logMessage(failedFilesMsg, infoLog, warnLog, errorLog, "warn", "run");
            respMsg.append(failedFilesMsg);
        }

        if (!totalMetaCounter.getFailedDlaIds().isEmpty()) {
            String unexpectedObjectsMsg = String.format("There are %d unexpected objects, please check the details.\n ",
                    totalMetaCounter.getFailedDlaIds().size());
            logMessage(unexpectedObjectsMsg, infoLog, warnLog, errorLog, "warn", "run");
            respMsg.append(unexpectedObjectsMsg);
        }

        logMessage(respMsg.toString(), infoLog, warnLog, errorLog, "info", "run");
    }

    private void updateDlaTraceParser(ElementExtractor currentElementExtractor, HashMap<String, String> baseInfo,
            PipeCounter pipeCounter, DLADataParser dlaDataParser) {
        // go to update all object's data trace
        DLATraceParser dlaTraceParser = new DLATraceParser(
                currentElementExtractor, baseInfo.get("cpcPrefixId"), baseInfo.get("zosPrefixId"),
                baseInfo.get("smfId"), baseInfo.get("lparName"), pipeCounter.getFailedDlaIds());
        dlaTraceParser.updateScanDateFromElementExtractor();
        dlaTraceParser.updateScanDateForZSubsystem(dlaDataParser.getZSubSystemList());

        // Save Failed Dla Id's count to file_trace table
        FileTrace fileTrace = new FileTrace();
        if (!pipeCounter.getFailedDlaIds().isEmpty()) {
            fileTrace.setFailedDlaIdsCount(Long.valueOf(pipeCounter.getFailedDlaIds().size()));
            fileTraceRepository.save(fileTrace);
        }

    }

    private void UpdateMetaTotalCounter(PipeCounter metaPipeCounter, List<String> failedMetaFileList, String filePath) {
        if (metaPipeCounter == null) {
            failedMetaFileList.add(filePath); // fail to extract the IDML book
        } else {
            this.totalMetaCounter.mergeCounter(filePath, metaPipeCounter);
        }
    }

    public void logMessage(String logMessage, StringBuilder infoLog, StringBuilder warnLog, StringBuilder errorLog,
            String logLevel, String methodName) {
        switch (logLevel.toLowerCase()) {
            case "warn" -> {
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, methodName, logMessage);
                warnLog.append(logMessage).append("\n");
            }
            case "info" -> {
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, methodName, logMessage);
                infoLog.append(logMessage).append("\n");
            }
            case "error" -> {
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, methodName, logMessage);
                errorLog.append(logMessage).append("\n");
            }
            default -> {
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, methodName, logMessage); // Default to info if no valid log
                                                                                     // level is provided
                infoLog.append(logMessage).append("\n");
            }
        }
    }

}