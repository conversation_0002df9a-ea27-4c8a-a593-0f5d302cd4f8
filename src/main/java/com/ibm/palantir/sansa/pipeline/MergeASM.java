package com.ibm.palantir.sansa.pipeline;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.sansa.extractor.TopologyExtractor;
import com.ibm.palantir.sansa.loader.TopologyLoader;
import com.ibm.palantir.sansa.transfromation.TopologyTransformation;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

public class MergeASM extends AbstractPipeline {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();

    private static final String CLASSNAME = MergeASM.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private String pipeline(JsonObject config, String tenantID) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "pipeline", "Running pipeline");
        if(!config.has("option") || !config.get("option").isJsonObject()){
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "pipeline", "Missing option in persist config");
            String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM", "");
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "pipeline");
        }

        JsonObject optionConfig = config.get("option").getAsJsonObject();

        if (!optionConfig.has("topologyUrl") || !optionConfig.has("mergeUrl")
                || !optionConfig.has("authorizeUrl") || !optionConfig.has("user")) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "pipeline", "Missing topologyUrl or mergeUrl or authorizeUrl or user in Plugin config");
            String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM", "");
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "pipeline");
        }


        String topologyUrl = optionConfig.get("topologyUrl").getAsString();
        String authorizeUrl = optionConfig.get("authorizeUrl").getAsString();
        String mergeUrl = optionConfig.get("mergeUrl").getAsString();
        String user = optionConfig.get("user").getAsString();
        String author;

        TopologyExtractor topologyExtractor = new TopologyExtractor();
        TopologyTransformation topologyTransformation = new TopologyTransformation();
        TopologyLoader topologyLoader = new TopologyLoader();

        if (optionConfig.has("api_key")) {
            author = topologyExtractor.getToken(authorizeUrl, user, optionConfig.get("api_key").getAsString());
        } else if (optionConfig.has("password")) {
            author = topologyExtractor.getBasic(user, optionConfig.get("password").getAsString());
        } else {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "pipeline", "Missing api_key or password in persist config");
            String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM", "");
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "pipeline");
        }
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json");
        headers.put("Content-Type", "application/json");
        headers.put("X-TenantID", tenantID);
        headers.put("Authorization", author);

        JsonArray array = topologyExtractor.getIDList(topologyUrl, headers);

        topologyLoader.postRule(mergeUrl, headers);
        for (JsonElement element : array) {
            String value = topologyTransformation.convertJSON(element.getAsJsonObject());
            String id = element.getAsJsonObject().get("_id").getAsString();
            topologyLoader.postResource(topologyUrl, headers, id, value);
        }

        return "Merge data to add mergeTokens in zAPM.";
    }


    public String run(PipelineConf pipelineConf) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", "Running MergeASM");
        String parameter = pipelineConf.getParameter();
        String persistID = pipelineConf.getPersist().get(0).toString();
        ConfigManager configManager = ConfigManager.getConfigManager();
        JsonObject jsonObject = configManager.searchConfig("persist", persistID);

        if (jsonObject == null) {
            String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM");
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", "Error: {}", msg);
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "run");
        }
        JsonElement element = JsonParser.parseString(parameter);
        String result;
        if (element.isJsonPrimitive()) {
            String tenantID = element.getAsString();
            result = pipeline(jsonObject, tenantID);
        } else if (element.isJsonObject() && element.getAsJsonObject().has("tenantID")) {
            JsonObject parameterObject = element.getAsJsonObject();
            String tenantID = parameterObject.get("tenantID").getAsString();
            result = pipeline(jsonObject, tenantID);
        } else {
            String errCode = ErrorCode.ProcessError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM");
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", "Error: {}", msg);
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "run");
        }
        return result;
    }
}
