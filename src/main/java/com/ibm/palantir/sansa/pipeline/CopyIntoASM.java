/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.pipeline;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.AddressSpace;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.BindAddress;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSDB2Conn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSFile;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSPlex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTable;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTableOverride;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSTransaction;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2BufferPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2DataSharingGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Database;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Subsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Table;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2TableSpace;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Fqdn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSDatabase;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSSubsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSTransaction;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IpAddress;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IpInterface;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.LPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQAliasQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQAuthInfo;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQBufferPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQClientConnectionChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQClusterReceiverChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQClusterSenderChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQLocalQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQModelQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQReceiverChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQRemoteQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQSenderChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQServerConnectionChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQSubsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ProcessPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Relationship;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Sysplex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.TcpPort;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.UdpPort;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZOS;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSeriesComputer;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSubSystem;
import com.ibm.palantir.catelyn.jpa.repository.dla.AddressSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.BindAddressRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSDB2ConnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSFileRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSPlexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSRegionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableOverrideRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2BufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DataSharingGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2SubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.FqdnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSDatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IpAddressRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IpInterfaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.LPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAliasQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAuthInfoRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQBufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClientConnectionChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClusterReceiverChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClusterSenderChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQLocalQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQModelQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQReceiverChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQRemoteQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSenderChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQServerConnectionChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ProcessPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.SysplexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.TcpPortRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.UdpPortRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZOSRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSeriesComputerRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSubSystemRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.sansa.loader.FileObserverLoader;
import com.ibm.palantir.sansa.transfromation.FileObserverTransformation;

public class CopyIntoASM extends AbstractPipeline {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = CopyIntoASM.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private static String relationshipName = "_edgeType";
    private static String relationshipSource = "_fromUniqueId";
    private static String relationshipTarget = "_toUniqueId";
    private static String fillString = "";
    // Map<String, JsonArray> relationshipMap;

    private FileObserverTransformation fileObserverTransformation;
    private ZSeriesComputerRepository zSeriesComputerRepository;
    private LPARRepository lparRepository;
    private ZOSRepository zOSRepository;
    private SysplexRepository sysplexRepository;
    private ZSubSystemRepository zSubSystemRepository;
    private CICSRegionRepository cicsRegionRepository;
    private CICSSystemInitTableRepository cicsSystemInitTableRepository;
    private CICSSystemInitTableOverrideRepository cicsSystemInitTableOverrideRepository;
    private CICSProgramRepository cicsProgramRepository;
    private CICSTransactionRepository cicsTransactionRepository;
    private CICSFileRepository cicsFileRepository;
    private DB2SubsystemRepository db2SubsystemRepository;
    private DB2DataSharingGroupRepository db2DataSharingGroupRepository;
    private DB2DatabaseRepository db2DatabaseRepository;
    private DB2TableSpaceRepository db2TableSpaceRepository;
    private DB2TableRepository db2TableRepository;
    private DB2BufferPoolRepository db2BufferPoolRepository;
    private CICSDB2ConnRepository cicsdb2ConnRepository;
    private CICSPlexRepository cicsPlexRepository;
    private RelationshipRepository relationshipRepository;
    private AddressSpaceRepository addressSpaceRepository;
    private BindAddressRepository bindAddressRepository;
    private FqdnRepository fqdnRepository;
    private IpAddressRepository ipAddressRepository;
    private IpInterfaceRepository ipInterfaceRepository;
    private ProcessPoolRepository processPoolRepository;
    private TcpPortRepository tcpPortRepository;
    private UdpPortRepository udpPortRepository;
    // private IdmlOperationTimeRepository idmlOperationTimeRepository;
    private MQSubsystemRepository mqSubsystemRepository;
    private MQAliasQueueRepository mqAliasQueueRepository;
    private MQAuthInfoRepository mqAuthInfoRepository;
    private MQBufferPoolRepository mqBufferPoolRepository;
    private MQClientConnectionChannelRepository mqClientConnectionChannelRepository;
    private MQClusterReceiverChannelRepository mqClusterReceiverChannelRepository;
    private MQClusterSenderChannelRepository mqClusterSenderChannelRepository;
    private MQLocalQueueRepository mqLocalQueueRepository;
    private MQModelQueueRepository mqModelQueueRepository;
    private MQReceiverChannelRepository mqReceiverChannelRepository;
    private MQRemoteQueueRepository mqRemoteQueueRepository;
    private MQSenderChannelRepository mqSenderChannelRepository;
    private MQServerConnectionChannelRepository mqServerConnectionChannelRepository;
    private IMSSubsystemRepository imsSubsystemRepository;
    private IMSDatabaseRepository imsDatabaseRepository;
    private IMSProgramRepository imsProgramRepository;
    private IMSTransactionRepository imsTransactionRepository;

    private Gson gson = new Gson();

    private Map<String, JsonObject> zOSHashMap = new HashMap<>();
    private HashMap<String, String> entityZOS = new HashMap<>();

    private void transformZSeriesComputer() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSeriesComputer", "Running transformZSeriesComputer");
        List<ZSeriesComputer> zSeriesComputerList = zSeriesComputerRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (ZSeriesComputer zSeriesComputer : zSeriesComputerList) {
            // JsonObject jsonObject = new JsonObject();
            // jsonObject.addProperty("_operation", "InsertReplace");
            // jsonObject.addProperty("uniqueId", zSeriesComputer.getDlaId());
            // jsonObject.addProperty("observedTime", System.currentTimeMillis());
            // jsonObject.addProperty("name", zSeriesComputer.getDlaId());
            // JsonArray jsonArray = new JsonArray();
            // jsonArray.add(zSeriesComputer.getDlaId());
            // jsonObject.add("matchTokens", jsonArray);
            // jsonObject.add("tags", jsonArray);
            // jsonArray = new JsonArray();
            // jsonArray.add("ZSeriesComputerSystem");
            // jsonObject.add("entityTypes", jsonArray);
            JsonElement properties = JsonParser.parseString(gson.toJson(zSeriesComputer));
            JsonObject jsonObject = setJsonObject(zSeriesComputer.getDlaId(), "ZSeriesComputerSystem", properties);

            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "ZSeriesComputerSystem");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSeriesComputer", "finish transformZSeriesComputer");
    }

    private void transformLpar() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformLpar", "Running transformLpar");
        List<LPAR> zoslparList = lparRepository.findAll();
        JsonArray resultArray = new JsonArray();
        List<Relationship> relationships = relationshipRepository
                .getRelationshipListBySourceAndTargetLike("%-CICSRegion", "%-LPAR");
        HashMap<String, String> LparSys = new HashMap<>();
        for (Relationship relationship : relationships) {
            String CICSRegion = relationship.getSource();
            String LparID = relationship.getTarget();
            if (!LparSys.containsKey(LparID)) {
                String[] info = CICSRegion.split("-");
                if (info.length > 2) {
                    String matchTokens = LparID.replace("-LPAR", "") + "." + info[1];
                    LparSys.put(LparID, matchTokens);
                }
            }
        }
        for (LPAR lpar : zoslparList) {
            // JsonObject jsonObject = new JsonObject();
            // jsonObject.addProperty("_operation", "InsertReplace");
            // jsonObject.addProperty("uniqueId", lpar.getDlaId());
            // jsonObject.addProperty("observedTime", System.currentTimeMillis());
            // jsonObject.addProperty("name", lpar.getDlaId());
            // JsonArray jsonArray = new JsonArray();
            // jsonArray.add(lpar.getDlaId());
            // jsonObject.add("tags", jsonArray);
            // jsonArray = new JsonArray();
            // jsonArray.add(lpar.getDlaId());
            // if(LparSys.containsKey(lpar.getDlaId())) {
            // jsonArray.add(LparSys.get(lpar.getDlaId()));
            // }
            // jsonObject.add("matchTokens", jsonArray);
            // jsonArray = new JsonArray();
            // jsonArray.add("LPAR");
            // jsonObject.add("entityTypes", jsonArray);
            JsonElement properties = JsonParser.parseString(gson.toJson(lpar));
            JsonObject jsonObject = setJsonObject(lpar.getDlaId(), "LPAR", properties);
            if (LparSys.containsKey(lpar.getDlaId())) {
                jsonObject.get("matchTokens").getAsJsonArray().add(LparSys.get(lpar.getDlaId()));
            }

            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "LPAR");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformLpar", "finish transformLpar");
    }

    private void transformSysplex() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformSysplex", "Running transformSysplex");
        List<Sysplex> sysplexList = sysplexRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (Sysplex sysplex : sysplexList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(sysplex));
            JsonObject jsonObject = setJsonObject(sysplex.getDlaId(), "Sysplex", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "Sysplex");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformSysplex", "finish transformSysplex");
    }

    private void transformZOS() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZOS", "Running transformZOS");
        List<ZOS> zosList = zOSRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (ZOS zos : zosList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(zos));
            JsonObject jsonObject = setJsonObject(zos.getDlaId(), "ZOS", properties);
            resultArray.add(jsonObject);
        }
        setZOSInfo(zosList);
        fileObserverTransformation.exportFile(resultArray, "ZOS");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZOS", "finish transformZOS");
    }

    private void setZOSInfo(List<ZOS> zosList) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "setZOSInfo", "Running setZOSInfo");
        for (ZOS zos : zosList) {
            String systemName = zos.getName();
            String smf_sysplex = zos.getLabel();
            String[] entity = smf_sysplex.split("-");
            String id = zos.getDlaId();
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("smfID", entity[0]);
            jsonObject.addProperty("sysplexName", entity[1]);
            jsonObject.addProperty("systemName", systemName);
            zOSHashMap.put(id, jsonObject);
        }
        List<Relationship> relationships = relationshipRepository.getRelationshipListByTargetLike("%-ZOS");
        for (Relationship relationship : relationships) {
            String entity = relationship.getSource();
            String zOS = relationship.getTarget();
            if ((entity.endsWith("CICSRegion") || entity.endsWith("DB2Subsystem") || entity.endsWith("MQSubsystem")
                    || entity.endsWith("IMSSubsystem"))
                    && !entityZOS.containsKey(entity) && zOSHashMap.containsKey(zOS)) {
                entityZOS.put(entity, zOS);
            }
        }
    }

    private JsonObject addExtraProperties(String DLAId, JsonObject jsonObject) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "addExtraProperties", "Running addExtraProperties");
        if (zOSHashMap.isEmpty()) {
            List<ZOS> zosList = zOSRepository.findAll();
            setZOSInfo(zosList);
        }
        JsonObject fillObject = new JsonObject();
        fillObject.addProperty("smfID", fillString);
        fillObject.addProperty("sysplexName", fillString);
        fillObject.addProperty("systemName", fillString);
        if (entityZOS.containsKey(DLAId)) {
            jsonObject.add("extra_properties", zOSHashMap.get(entityZOS.get(DLAId)).deepCopy());
        } else {
            jsonObject.add("extra_properties", fillObject.deepCopy());
        }
        return jsonObject;
    }

    private void transformZSubSystem() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSubSystem", "Running transformZSubSystem");
        List<ZSubSystem> zSubSystemList = zSubSystemRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (ZSubSystem zSubSystem : zSubSystemList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(zSubSystem));
            JsonObject jsonObject = setJsonObject(zSubSystem.getDlaId(), "ZSubSystem", properties);
            // String[] info = zSubSystem.getDlaId().split("-");
            // if(info.length > 2) {
            // String matchTokens = info[0] + "." + info[1] + "." + info[2];
            // jsonObject.get("matchTokens").getAsJsonArray().add(matchTokens);
            // }
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "ZSubSystem");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSubSystem", "finish transformZSubSystem");
    }

    // ---------------------------------------CICS
    // group-----------------------------------------
    private void transformCICSRegion() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSRegion", "Running transformCICSRegion");
        List<CICSRegion> cicsRegionList = cicsRegionRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (CICSRegion cicsRegion : cicsRegionList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(cicsRegion));
            JsonObject jsonObject = setJsonObject(cicsRegion.getDlaId(), "CICSRegion", properties);
            jsonObject = addExtraProperties(cicsRegion.getDlaId(), jsonObject);
            // String[] info = cicsRegion.getDlaId().split("-");
            // if(info.length > 2) {
            // String matchTokens = info[0] + "." + info[1];
            // jsonObject.get("matchTokens").getAsJsonArray().add(matchTokens);
            // }
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "CICSRegion");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSRegion", "finish transformCICSRegion");
    }

    private void transformCICSSit() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSSit", "Running transformCICSSit");
        List<CICSSystemInitTable> cicsSitList = cicsSystemInitTableRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (CICSSystemInitTable cicsSit : cicsSitList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(cicsSit));
            JsonObject jsonObject = setJsonObject(cicsSit.getDlaId(), "CICSSit", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "CICSSit");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSSit", "finish transformCICSSit");
    }

    private void transformCICSSitOverride() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSSitOverride", "Running transformCICSSitOverride");
        List<CICSSystemInitTableOverride> cicsSitOverridList = cicsSystemInitTableOverrideRepository
                .findAll();
        JsonArray resultArray = new JsonArray();
        for (CICSSystemInitTableOverride cicsSitOverride : cicsSitOverridList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(cicsSitOverride));
            JsonObject jsonObject = setJsonObject(cicsSitOverride.getDlaId(), "CICSSitOverrides", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "CICSSitOverrides");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSSitOverride", "finish transformCICSSitOverride");
    }

    private void transformCICSProgram() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSProgram", "Running transformCICSProgram");
        List<CICSProgram> cicsProgramList = cicsProgramRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (CICSProgram cicsProgram : cicsProgramList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(cicsProgram));
            JsonObject jsonObject = setJsonObject(cicsProgram.getDlaId(), "CICSProgram", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "CICSProgram");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSProgram", "finish transformCICSProgram");
    }

    private void transformCICSTransaction() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSTransaction", "Running transformCICSTransaction");
        List<CICSTransaction> transactionList = cicsTransactionRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (CICSTransaction cicsTransaction : transactionList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(cicsTransaction));
            JsonObject jsonObject = setJsonObject(cicsTransaction.getDlaId(), "CICSTransaction", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "CICSTransaction");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSTransaction", "finish transformCICSTransaction");
    }

    private void transformCICSFile() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSFile", "Running transformCICSFile");
        List<CICSFile> fileList = cicsFileRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (CICSFile file : fileList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(file));
            JsonObject jsonObject = setJsonObject(file.getDlaId(), "CICSFile", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "CICSFile");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSFile", "finish transformCICSFile");
    }

    // ---------------------------------------DB2
    // group-----------------------------------------
    private void transformDb2DataSharingGroup() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2DataSharingGroup",
                "Running transformDb2DataSharingGroup");
        List<DB2DataSharingGroup> dataSharingGroupList = db2DataSharingGroupRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (DB2DataSharingGroup dataSharingGroup : dataSharingGroupList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(dataSharingGroup));
            JsonObject jsonObject = setJsonObject(dataSharingGroup.getDlaId(), "DB2DataSharingGroup", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "DB2DataSharingGroup");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2DataSharingGroup",
                "finish transformDb2DataSharingGroup");
    }

    private void transformDb2Subsystem() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Subsystem", "Running transformDb2Subsystem");
        // Add DB2DataSharingGroup name for DB2Subsystem
        List<Relationship> relationships = relationshipRepository
                .getRelationshipListBySourceAndTargetLike("%-DB2Subsystem", "%-DB2DataSharingGroup");
        HashMap<String, String> db2Relation = new HashMap<>();
        for (Relationship relationship : relationships) {
            String DB2Subsystem = relationship.getSource();
            String DB2DataSharingGroup = relationship.getTarget();
            if (!db2Relation.containsKey(DB2Subsystem)) {
                String[] info = DB2DataSharingGroup.split("-");
                if (info.length > 2) {
                    db2Relation.put(DB2Subsystem, info[0]);
                }
            }
        }

        List<DB2Subsystem> db2SubsystemList = db2SubsystemRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (DB2Subsystem db2Subsystem : db2SubsystemList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(db2Subsystem));
            JsonObject jsonObject = setJsonObject(db2Subsystem.getDlaId(), "DB2Subsystem", properties);
            jsonObject = addExtraProperties(db2Subsystem.getDlaId(), jsonObject);
            if (db2Relation.containsKey(db2Subsystem.getDlaId())) {
                jsonObject.get("extra_properties").getAsJsonObject().addProperty("DB2DataSharingGroup",
                        db2Relation.get(db2Subsystem.getDlaId()));
            } else {
                jsonObject.get("extra_properties").getAsJsonObject().addProperty("DB2DataSharingGroup", fillString);
            }
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "DB2Subsystem");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "trasnformDb2Subsystem", "finish trasnformDb2Subsystem");
    }

    private void transformDb2Database() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Database", "Running transformDb2Database");
        List<DB2Database> db2DatabaseList = db2DatabaseRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (DB2Database db2Database : db2DatabaseList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(db2Database));
            JsonObject jsonObject = setJsonObject(db2Database.getDlaId(), "Db2Database", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "Db2Database");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Database", "finish transformDb2Database");
    }

    private void transformDb2TableSpace() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2TableSpace", "Running transformDb2TableSpace");
        List<DB2TableSpace> db2TableSpaceList = db2TableSpaceRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (DB2TableSpace db2TableSpace : db2TableSpaceList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(db2TableSpace));
            JsonObject jsonObject = setJsonObject(db2TableSpace.getDlaId(), "Db2TableSpace", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "Db2TableSpace");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2TableSpace", "finish transformDb2TableSpace");
    }

    private void transformDb2BufferPool() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2BufferPool", "Running transformDb2BufferPool");
        List<DB2BufferPool> bufferPoolList = db2BufferPoolRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (DB2BufferPool bufferPool : bufferPoolList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(bufferPool));
            JsonObject jsonObject = setJsonObject(bufferPool.getDlaId(), "Db2BufferPool", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "Db2BufferPool");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2BufferPool", "finish transformDb2BufferPool");
    }

    // -------------------------------------MQ
    // Resources---------------------------------------
    private void transformMQSubsystem() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQSubsystem", "Running transformMQSubsystem");
        List<MQSubsystem> mqSubsystems = mqSubsystemRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQSubsystem mqSubsystem : mqSubsystems) {
            JsonElement properties = JsonParser.parseString(gson.toJson(mqSubsystem));
            JsonObject jsonObject = setJsonObject(mqSubsystem.getDlaId(), "MQSubsystem", properties);
            jsonObject = addExtraProperties(mqSubsystem.getDlaId(), jsonObject);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQSubsystem");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQSubsystem", "finish transformMQSubsystem");
    }

    private void transformMQAliasQueue() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQAliasQueue", "Running transformMQAliasQueue");
        List<MQAliasQueue> mqAliasQueues = mqAliasQueueRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQAliasQueue mqAliasQueue : mqAliasQueues) {
            JsonElement properties = JsonParser.parseString(gson.toJson(mqAliasQueue));
            JsonObject jsonObject = setJsonObject(mqAliasQueue.getDlaId(), "MQAliasQueue", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQAliasQueue");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQAliasQueue", "finish transformMQAliasQueue");
    }

    private void transformMQAuthInfo() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQAuthInfo", "Running transformMQAuthInfo");
        List<MQAuthInfo> mqAuthInfos = mqAuthInfoRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQAuthInfo mqAuthInfo : mqAuthInfos) {
            JsonElement properties = JsonParser.parseString(gson.toJson(mqAuthInfo));
            JsonObject jsonObject = setJsonObject(mqAuthInfo.getDlaId(), "MQAuthInfo", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQAuthInfo");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQAuthInfo", "finish transformMQAuthInfo");
    }

    private void transformMQBufferPool() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQBufferPool", "Running transformMQBufferPool");
        List<MQBufferPool> mqBufferPools = mqBufferPoolRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQBufferPool mqBufferPool : mqBufferPools) {
            JsonElement properties = JsonParser.parseString(gson.toJson(mqBufferPool));
            JsonObject jsonObject = setJsonObject(mqBufferPool.getDlaId(), "MQBufferPool", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQBufferPool");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQBufferPool", "finish transformMQBufferPool");
    }

    private void transformMQClientConnectionChannel() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQClientConnectionChannel",
                "Running transformMQClientConnectionChannel");
        List<MQClientConnectionChannel> mqClientConnectionChannels = mqClientConnectionChannelRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQClientConnectionChannel mqClientConnectionChannel : mqClientConnectionChannels) {
            JsonElement properties = JsonParser.parseString(gson.toJson(mqClientConnectionChannel));
            JsonObject jsonObject = setJsonObject(mqClientConnectionChannel.getDlaId(), "MQClientConnectionChannel",
                    properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQClientConnectionChannel");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQClientConnectionChannel",
                "finish transformMQClientConnectionChannel");
    }

    private void transformMQClusterReceiverChannel() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQClusterReceiverChannel",
                "Running transformMQClusterReceiverChannel");
        List<MQClusterReceiverChannel> mqClusterReceiverChannels = mqClusterReceiverChannelRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQClusterReceiverChannel mqClusterReceiverChannel : mqClusterReceiverChannels) {
            JsonElement properties = JsonParser.parseString(gson.toJson(mqClusterReceiverChannel));
            JsonObject jsonObject = setJsonObject(mqClusterReceiverChannel.getDlaId(), "MQClusterReceiverChannel",
                    properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQClusterReceiverChannel");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQClusterReceiverChannel",
                "finish transformMQClusterReceiverChannel");
    }

    private void transformMQClusterSenderChannel() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQClusterSenderChannel",
                "Running transformMQClusterSenderChannel");
        List<MQClusterSenderChannel> mqClusterSenderChannels = mqClusterSenderChannelRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQClusterSenderChannel mqClusterSenderChannel : mqClusterSenderChannels) {
            JsonElement properties = JsonParser.parseString(gson.toJson(mqClusterSenderChannel));
            JsonObject jsonObject = setJsonObject(mqClusterSenderChannel.getDlaId(), "MQClusterSenderChannel",
                    properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQClusterSenderChannel");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQClusterSenderChannel",
                "finish transformMQClusterSenderChannel");
    }

    private void transformMQLocalQueue() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQLocalQueue", "Running transformMQLocalQueue");
        List<MQLocalQueue> mqLocalQueues = mqLocalQueueRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQLocalQueue mqLocalQueue : mqLocalQueues) {
            JsonElement properties = JsonParser.parseString(gson.toJson(mqLocalQueue));
            JsonObject jsonObject = setJsonObject(mqLocalQueue.getDlaId(), "MQLocalQueue", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQLocalQueue");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQLocalQueue", "finish transformMQLocalQueue");
    }

    private void transformMQModelQueue() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQModelQueue", "Running transformMQModelQueue");
        List<MQModelQueue> modelQueues = mqModelQueueRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQModelQueue modelQueue : modelQueues) {
            JsonElement properties = JsonParser.parseString(gson.toJson(modelQueue));
            JsonObject jsonObject = setJsonObject(modelQueue.getDlaId(), "MQModelQueue", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQModelQueue");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQModelQueue", "finish transformMQModelQueue");
    }

    private void transformMQReceiverChannel() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQReceiverChannel", "Running transformMQReceiverChannel");
        List<MQReceiverChannel> mqReceiverChannels = mqReceiverChannelRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQReceiverChannel mqReceiverChannel : mqReceiverChannels) {
            JsonElement properties = JsonParser.parseString(gson.toJson(mqReceiverChannel));
            JsonObject jsonObject = setJsonObject(mqReceiverChannel.getDlaId(), "MQReceiverChannel", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQReceiverChannel");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQReceiverChannel", "finish transformMQReceiverChannel");
    }

    private void transformMQRemoteQueue() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQRemoteQueue", "Running transformMQRemoteQueue");
        List<MQRemoteQueue> mqRemoteQueues = mqRemoteQueueRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQRemoteQueue mqRemoteQueue : mqRemoteQueues) {
            JsonElement properties = JsonParser.parseString(gson.toJson(mqRemoteQueue));
            JsonObject jsonObject = setJsonObject(mqRemoteQueue.getDlaId(), "MQRemoteQueue", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQRemoteQueue");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQRemoteQueue", "finish transformMQRemoteQueue");
    }

    private void transformMQSenderChannel() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQSenderChannel", "Running transformMQSenderChannel");
        List<MQSenderChannel> mqSenderChannels = mqSenderChannelRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQSenderChannel mqSenderChannel : mqSenderChannels) {
            JsonElement properties = JsonParser.parseString(gson.toJson(mqSenderChannel));
            JsonObject jsonObject = setJsonObject(mqSenderChannel.getDlaId(), "MQSenderChannel", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQSenderChannel");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQSenderChannel", "finish transformMQSenderChannel");
    }

    private void transformMQServerConnectionChannel() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQServerConnectionChannel",
                "Running transformMQServerConnectionChannel");
        List<MQServerConnectionChannel> mqServerConnectionChannels = mqServerConnectionChannelRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (MQServerConnectionChannel mqServerConnectionChannel : mqServerConnectionChannels) {
            JsonElement properties = JsonParser.parseString(gson.toJson(mqServerConnectionChannel));
            JsonObject jsonObject = setJsonObject(mqServerConnectionChannel.getDlaId(), "MQServerConnectionChannel",
                    properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "MQServerConnectionChannel");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQServerConnectionChannel",
                "finish transformMQServerConnectionChannel");
    }

    // -------------------------------------IMS
    // Resources---------------------------------------
    private void transformIMSSubsystem() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSSubsystem", "Running transformIMSSubsystem");
        List<IMSSubsystem> imsSubsystems = imsSubsystemRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (IMSSubsystem imsSubsystem : imsSubsystems) {
            JsonElement properties = JsonParser.parseString(gson.toJson(imsSubsystem));
            JsonObject jsonObject = setJsonObject(imsSubsystem.getDlaId(), "IMSSubsystem", properties);
            jsonObject = addExtraProperties(imsSubsystem.getDlaId(), jsonObject);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "IMSSubsystem");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSSubsystem", "finish transformIMSSubsystem");
    }

    private void transformIMSDatabase() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSDatabase", "Running transformIMSDatabase");
        List<IMSDatabase> imsDatabaseList = imsDatabaseRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (IMSDatabase imsDatabase : imsDatabaseList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(imsDatabase));
            JsonObject jsonObject = setJsonObject(imsDatabase.getDlaId(), "IMSDatabase", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "IMSDatabase");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSDatabase", "finish transformIMSDatabase");
    }

    private void transformIMSProgram() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSProgram", "Running transformIMSProgram");
        List<IMSProgram> imsPrograms = imsProgramRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (IMSProgram imsProgram : imsPrograms) {
            JsonElement properties = JsonParser.parseString(gson.toJson(imsProgram));
            JsonObject jsonObject = setJsonObject(imsProgram.getDlaId(), "IMSProgram", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "IMSProgram");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSProgram", "finish transformIMSProgram");
    }

    private void transformIMSTransaction() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSTransaction", "Running transformIMSTransaction");
        List<IMSTransaction> imsTransactions = imsTransactionRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (IMSTransaction imsTransaction : imsTransactions) {
            JsonElement properties = JsonParser.parseString(gson.toJson(imsTransaction));
            JsonObject jsonObject = setJsonObject(imsTransaction.getDlaId(), "IMSTransaction", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "IMSTransaction");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSTransaction", "finish transformIMSTransaction");
    }

    // -------------------------------------Other
    // Resources------------------------------------
    private void transformCICSDB2Conn() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSDB2Conn", "Running transformCICSDB2Conn");
        List<CICSDB2Conn> cicsdb2ConnList = cicsdb2ConnRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (CICSDB2Conn cicsdb2Conn : cicsdb2ConnList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(cicsdb2Conn));
            JsonObject jsonObject = setJsonObject(cicsdb2Conn.getDlaId(), "CICSDB2Conn", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "CICSDB2Conn");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSDB2Conn", "finish transformCICSDB2Conn");
    }

    private void transformCICSPlex() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSPlex", "Running transformCICSPlex");
        List<CICSPlex> cicsPlexeList = cicsPlexRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (CICSPlex cicsPlex : cicsPlexeList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(cicsPlex));
            JsonObject jsonObject = setJsonObject(cicsPlex.getDlaId(), "CICSPlex", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "CICSPlex");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSPlex", "finish transformCICSPlex");
    }

    private void transformDb2Table() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Table", "Running transformDb2Table");
        List<DB2Table> tableList = db2TableRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (DB2Table db2Table : tableList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(db2Table));
            JsonObject jsonObject = setJsonObject(db2Table.getDlaId(), "Db2Table", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "Db2Table");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Table", "finish transformDb2Table");
    }

    private void transformAddressSpace() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformAddressSpace", "Running transformAddressSpace");
        List<AddressSpace> addressSpaceList = addressSpaceRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (AddressSpace addressSpace : addressSpaceList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(addressSpace));
            JsonObject jsonObject = setJsonObject(addressSpace.getDlaId(), "AddressSpace", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "AddressSpace");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformAddressSpace", "finish transformAddressSpace");
    }

    private void transformBindAddress() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformBindAddress", "Running transformBindAddress");
        List<BindAddress> bindAddressList = bindAddressRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (BindAddress bindAddress : bindAddressList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(bindAddress));
            JsonObject jsonObject = setJsonObject(bindAddress.getDlaId(), "BindAddress", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "BindAddress");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformBindAddress", "finish transformBindAddress");
    }

    private void transformFqdn() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformFqdn", "Running transformFqdn");
        List<Fqdn> fqdnList = fqdnRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (Fqdn fqdn : fqdnList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(fqdn));
            JsonObject jsonObject = setJsonObject(fqdn.getDlaId(), "Fqdn", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "Fqdn");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformFqdn", "finish transformFqdn");
    }

    private void transformIpAddress() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIpAddress", "Running transformIpAddress");
        List<IpAddress> ipAddressList = ipAddressRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (IpAddress ipAddress : ipAddressList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(ipAddress));
            JsonObject jsonObject = setJsonObject(ipAddress.getDlaId(), "IpAddress", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "IpAddress");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIpAddress", "finish transformIpAddress");
    }

    private void transformIpInterface() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIpInterface", "Running transformIpInterface");
        List<IpInterface> ipInterfaceList = ipInterfaceRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (IpInterface ipInterface : ipInterfaceList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(ipInterface));
            JsonObject jsonObject = setJsonObject(ipInterface.getDlaId(), "IpInterface", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "IpInterface");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIpInterface", "finish transformIpInterface");
    }

    private void transformProcessPool() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformProcessPool", "Running transformProcessPool");
        List<ProcessPool> processPoolList = processPoolRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (ProcessPool processPool : processPoolList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(processPool));
            JsonObject jsonObject = setJsonObject(processPool.getDlaId(), "ProcessPool", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "ProcessPool");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformProcessPool", "finish transformProcessPool");
    }

    private void transformTcpPort() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformTcpPort", "Running transformTcpPort");
        List<TcpPort> tcpPortList = tcpPortRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (TcpPort tcpPort : tcpPortList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(tcpPort));
            JsonObject jsonObject = setJsonObject(tcpPort.getDlaId(), "TcpPort", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "TcpPort");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformTcpPort", "finish transformTcpPort");
    }

    private void transformUdpPort() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformUdpPort", "Running transformUdpPort");
        List<UdpPort> udpPortList = udpPortRepository.findAll();
        JsonArray resultArray = new JsonArray();
        for (UdpPort udpPort : udpPortList) {
            JsonElement properties = JsonParser.parseString(gson.toJson(udpPort));
            JsonObject jsonObject = setJsonObject(udpPort.getDlaId(), "UdpPort", properties);
            resultArray.add(jsonObject);
        }
        fileObserverTransformation.exportFile(resultArray, "UdpPort");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformUdpPort", "finish transformUdpPort");
    }

    // private void transformIdmlOperationTime() {
    // List<IdmlOperationTime> operationTimeList =
    // idmlOperationTimeRepository.findAll();
    // for (IdmlOperationTime operationTime: operationTimeList) {
    // }
    // LOG.info("finish transformIdmlOperationTime");
    // }

    private void transformRelationships(JsonArray typeArray) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformRelationships", "Running transformRelationships");
        HashSet<String> types = new HashSet<>();
        for (JsonElement element : typeArray)
            types.add(element.getAsString());

        List<Relationship> relationshipList = relationshipRepository.findAll();
        JsonArray jsonArray = new JsonArray();
        for (Relationship relationship : relationshipList) {
            String source = relationship.getSource();
            String target = relationship.getTarget();
            String name = relationship.getName();
            String reverseName = relationship.getReverseName();

            String sourceType = source.substring(source.lastIndexOf("-") + 1);
            String targetType = target.substring(target.lastIndexOf("-") + 1);
            if (types.contains(sourceType) && types.contains(targetType)) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("_operation", "InsertReplace");
                jsonObject.addProperty(relationshipName, "zDiscovery_" + name);
                jsonObject.addProperty(relationshipSource, source);
                jsonObject.addProperty(relationshipTarget, target);
                jsonArray.add(jsonObject);
                if (reverseName != null && !reverseName.isEmpty()) {
                    jsonObject = new JsonObject();
                    jsonObject.addProperty("_operation", "InsertReplace");
                    jsonObject.addProperty(relationshipName, "zDiscovery_" + reverseName);
                    jsonObject.addProperty(relationshipSource, target);
                    jsonObject.addProperty(relationshipTarget, source);
                    jsonArray.add(jsonObject);
                }
            }
        }
        fileObserverTransformation.exportRelationFile(jsonArray);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformRelationship", "finish transformRelationship");
    }

    private void transformRelationships() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformRelationships", "Running transformRelationships");
        List<Relationship> relationshipList = relationshipRepository.findAll();
        JsonArray jsonArray = new JsonArray();
        for (Relationship relationship : relationshipList) {
            String source = relationship.getSource();
            String target = relationship.getTarget();
            String name = relationship.getName();
            String reverseName = relationship.getReverseName();
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("_operation", "InsertReplace");
            jsonObject.addProperty(relationshipName, "zDiscovery_" + name);
            jsonObject.addProperty(relationshipSource, source);
            jsonObject.addProperty(relationshipTarget, target);
            jsonArray.add(jsonObject);
            // if (relationshipMap.containsKey(source)) {
            // relationshipMap.get(source).add(jsonObject);
            // } else {
            // JsonArray jsonArray = new JsonArray();
            // jsonArray.add(jsonObject);
            // relationshipMap.put(source, jsonArray);
            // }
            // if (reverseName != null && !reverseName.isEmpty()) {
            // jsonObject = new JsonObject();
            // jsonObject.addProperty(relationshipName, "zDiscovery_" + reverseName);
            // jsonObject.addProperty(relationshipTarget, source);
            // if (relationshipMap.containsKey(target)) {
            // relationshipMap.get(target).add(jsonObject);
            // } else {
            // JsonArray jsonArray = new JsonArray();
            // jsonArray.add(jsonObject);
            // relationshipMap.put(target, jsonArray);
            // }
            // }

            if (reverseName != null && !reverseName.isEmpty()) {
                jsonObject = new JsonObject();
                jsonObject.addProperty("_operation", "InsertReplace");
                jsonObject.addProperty(relationshipName, "zDiscovery_" + reverseName);
                jsonObject.addProperty(relationshipSource, target);
                jsonObject.addProperty(relationshipTarget, source);
                jsonArray.add(jsonObject);
            }
        }
        fileObserverTransformation.exportRelationFile(jsonArray);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformRelationship", "finish transformRelationship");
    }

    private JsonObject setJsonObject(String DLAId, String Type, JsonElement properties) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "setJsonObject", "Running setJsonObject");
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("_operation", "InsertReplace");
        jsonObject.addProperty("uniqueId", DLAId);
        jsonObject.addProperty("observedTime", System.currentTimeMillis());
        jsonObject.addProperty("name", DLAId);
        JsonArray jsonArray = new JsonArray();
        jsonArray.add(DLAId);
        jsonObject.add("matchTokens", jsonArray);
        jsonArray = new JsonArray();
        jsonArray.add(DLAId);
        jsonObject.add("tags", jsonArray);
        jsonArray = new JsonArray();
        jsonArray.add(Type);
        jsonObject.add("entityTypes", jsonArray);
        jsonObject.add("discovery_properties", properties);
        jsonObject.add("_references", new JsonArray());
        return jsonObject;
    }

    private void setRepository() {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "setRepository", "Running setRepository");
        HashMap<String, Object> repos = JPAManager.getJPAManager().getRepos();

        this.zSeriesComputerRepository = (ZSeriesComputerRepository) repos.get("zSeriesComputer");
        this.lparRepository = (LPARRepository) repos.get("lpar");
        this.zOSRepository = (ZOSRepository) repos.get("zos");
        this.sysplexRepository = (SysplexRepository) repos.get("sysplex");
        this.zSubSystemRepository = (ZSubSystemRepository) repos.get("zSubSystem");
        this.cicsRegionRepository = (CICSRegionRepository) repos.get("cicsRegion");
        this.cicsSystemInitTableRepository = (CICSSystemInitTableRepository) repos.get("cicsSystemInitTable");
        this.cicsSystemInitTableOverrideRepository = (CICSSystemInitTableOverrideRepository) repos
                .get("cicsSystemInitTableOverride");
        this.cicsProgramRepository = (CICSProgramRepository) repos.get("cicsProgram");
        this.cicsTransactionRepository = (CICSTransactionRepository) repos.get("cicsTransaction");
        this.cicsFileRepository = (CICSFileRepository) repos.get("cicsFile");
        this.db2SubsystemRepository = (DB2SubsystemRepository) repos.get("db2Subsystem");
        this.db2DataSharingGroupRepository = (DB2DataSharingGroupRepository) repos.get("db2dataSharingGroup");
        this.db2DatabaseRepository = (DB2DatabaseRepository) repos.get("db2Database");
        this.db2TableSpaceRepository = (DB2TableSpaceRepository) repos.get("db2TableSpace");
        this.db2TableRepository = (DB2TableRepository) repos.get("db2Table");
        this.db2BufferPoolRepository = (DB2BufferPoolRepository) repos.get("db2BufferPool");
        this.cicsdb2ConnRepository = (CICSDB2ConnRepository) repos.get("cicsdb2Conn");
        this.cicsPlexRepository = (CICSPlexRepository) repos.get("cicsPlex");
        this.relationshipRepository = (RelationshipRepository) repos.get("relationship");
        this.addressSpaceRepository = (AddressSpaceRepository) repos.get("addressSpace");
        this.bindAddressRepository = (BindAddressRepository) repos.get("bindAddress");
        this.fqdnRepository = (FqdnRepository) repos.get("fqdn");
        this.ipAddressRepository = (IpAddressRepository) repos.get("ipAddress");
        this.ipInterfaceRepository = (IpInterfaceRepository) repos.get("ipInterface");
        this.processPoolRepository = (ProcessPoolRepository) repos.get("processPool");
        this.tcpPortRepository = (TcpPortRepository) repos.get("tcpPort");
        this.udpPortRepository = (UdpPortRepository) repos.get("udpPort");
        // this.idmlOperationTimeRepository = (IdmlOperationTimeRepository)
        // repos.get("idmlOperationTime");
        this.mqSubsystemRepository = (MQSubsystemRepository) repos.get("mqSubsystem");
        this.mqAliasQueueRepository = (MQAliasQueueRepository) repos.get("mqAliasQueue");
        this.mqAuthInfoRepository = (MQAuthInfoRepository) repos.get("mqAuthInfo");
        this.mqBufferPoolRepository = (MQBufferPoolRepository) repos.get("mqBufferPool");
        this.mqClientConnectionChannelRepository = (MQClientConnectionChannelRepository) repos
                .get("mqClientConnectionChannel");
        this.mqClusterReceiverChannelRepository = (MQClusterReceiverChannelRepository) repos
                .get("mqClusterReceiverChannel");
        this.mqClusterSenderChannelRepository = (MQClusterSenderChannelRepository) repos.get("mqClusterSenderChannel");
        this.mqLocalQueueRepository = (MQLocalQueueRepository) repos.get("mqLocalQueue");
        this.mqModelQueueRepository = (MQModelQueueRepository) repos.get("mqModelQueue");
        this.mqReceiverChannelRepository = (MQReceiverChannelRepository) repos.get("mqReceiverChannel");
        this.mqRemoteQueueRepository = (MQRemoteQueueRepository) repos.get("mqRemoteQueue");
        this.mqSenderChannelRepository = (MQSenderChannelRepository) repos.get("mqSenderChannel");
        this.mqServerConnectionChannelRepository = (MQServerConnectionChannelRepository) repos
                .get("mqServerConnectionChannel");
        this.imsSubsystemRepository = (IMSSubsystemRepository) repos.get("imsSubsystem");
        this.imsDatabaseRepository = (IMSDatabaseRepository) repos.get("imsDatabase");
        this.imsProgramRepository = (IMSProgramRepository) repos.get("imsProgram");
        this.imsTransactionRepository = (IMSTransactionRepository) repos.get("imsTransaction");
    }

    private JsonObject getOptionConfig(JsonObject config) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "getOptionConfig", "Running getOptionConfig");
        if (!config.has("option") || !config.get("option").isJsonObject()) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "setRepository", "Missing option in persist config");
            String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM", "");
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "getOptionConfig");
        }

        JsonObject optionConfig = config.get("option").getAsJsonObject();

        if (!optionConfig.has("topologyUrl") || !optionConfig.has("fileObserverUrl") || !optionConfig.has("user")
                || (!(optionConfig.has("api_key") && optionConfig.has("authorizeUrl"))
                        && !optionConfig.has("password"))) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "setRepository",
                    "Missing topologyUrl or fileObserverUrl or user or authorize option (api_key && authorizeUrl or password) in Plugin config");
            String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM", "");
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "getOptionConfig");
        }
        return optionConfig;
    }

    private String runAll(JsonObject config, String tenantID) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "runAll", "Running runAll");
        JsonObject optionConfig = getOptionConfig(config);

        String topologyUrl = optionConfig.get("topologyUrl").getAsString();
        String fileObserverUrl = optionConfig.get("fileObserverUrl").getAsString();
        String user = optionConfig.get("user").getAsString();
        fileObserverTransformation = new FileObserverTransformation();
        try {
            transformZSeriesComputer();
            transformLpar();
            transformSysplex();
            transformZOS();
            transformZSubSystem();
            transformCICSRegion();
            transformCICSSit();
            transformCICSSitOverride();
            transformCICSProgram();
            transformCICSTransaction();
            transformCICSFile();
            transformDb2DataSharingGroup();
            transformDb2Subsystem();
            transformDb2Database();
            transformDb2TableSpace();
            transformDb2BufferPool();
            transformDb2Table();

            transformCICSDB2Conn();
            transformCICSPlex();

            // don't parse the AddressSpace to clear cycle
            // transformAddressSpace();
            transformBindAddress();
            transformFqdn();
            transformIpAddress();
            transformIpInterface();
            transformProcessPool();
            transformTcpPort();
            transformUdpPort();

            transformMQSubsystem();
            transformMQAliasQueue();
            transformMQAuthInfo();
            transformMQBufferPool();
            transformMQClientConnectionChannel();
            transformMQClusterReceiverChannel();
            transformMQClusterSenderChannel();
            transformMQLocalQueue();
            transformMQModelQueue();
            transformMQRemoteQueue();
            transformMQReceiverChannel();
            transformMQSenderChannel();
            transformMQServerConnectionChannel();

            transformIMSSubsystem();
            transformIMSDatabase();
            transformIMSProgram();
            transformIMSTransaction();

            transformRelationships();

            FileObserverLoader fileObserverLoader = new FileObserverLoader();
            String author;
            if (optionConfig.has("api_key")) {
                String authorizeUrl = optionConfig.get("authorizeUrl").getAsString();
                author = fileObserverLoader.getToken(authorizeUrl, user, optionConfig.get("api_key").getAsString());
            } else if (optionConfig.has("password")) {
                author = fileObserverLoader.getBasic(user, optionConfig.get("password").getAsString());
            } else {
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "runAll", "Missing api_key or password in persist config");
                String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM", "");
                throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "runAll");
            }
            fileObserverLoader.loadData(topologyUrl, fileObserverUrl, tenantID,
                    fileObserverTransformation.getFileList(), author);

        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "runAll", e.toString());
            String errCode = ErrorCode.PluginError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM", "");
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "runAll");
        }

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "runAll", "copy data from PostgresQL into WAIOps successfully.");
        return "copy data from PostgresQL into WAIOps successfully.";
    }

    public String runInclude(JsonObject config, String tenantID, JsonArray include) throws Exception {

        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "transformZSeriesComputer", "Running transformZSeriesComputer");
        JsonObject optionConfig = getOptionConfig(config);

        String topologyUrl = optionConfig.get("topologyUrl").getAsString();
        String fileObserverUrl = optionConfig.get("fileObserverUrl").getAsString();
        String authorizeUrl = optionConfig.get("authorizeUrl").getAsString();
        String user = optionConfig.get("user").getAsString();

        fileObserverTransformation = new FileObserverTransformation();

        try {

            for (JsonElement jsonObject : include) {
                String includeType = jsonObject.getAsString();
                switch (includeType) {
                    case "ZSeriesComputerSystem":
                        transformZSeriesComputer();
                        break;
                    case "LPAR":
                        transformLpar();
                        break;
                    case "Sysplex":
                        transformSysplex();
                        break;
                    case "ZOS":
                        transformZOS();
                        break;
                    case "ZSubSystem":
                        transformZSubSystem();
                        break;
                    case "CICSRegion":
                        transformCICSRegion();
                        break;
                    case "CICSSit":
                        transformCICSSit();
                        break;
                    case "CICSSitOverrides":
                        transformCICSSitOverride();
                        break;
                    case "CICSProgram":
                        transformCICSProgram();
                        break;
                    case "CICSTransaction":
                        transformCICSTransaction();
                        break;
                    case "CICSFile":
                        transformCICSFile();
                        break;
                    case "DB2DataSharingGroup":
                        transformDb2DataSharingGroup();
                        break;
                    case "DB2Subsystem":
                        transformDb2Subsystem();
                        break;
                    case "Db2Database":
                        transformDb2Database();
                        break;
                    case "Db2TableSpace":
                        transformDb2TableSpace();
                        break;
                    case "Db2BufferPool":
                        transformDb2BufferPool();
                        break;
                    case "Db2Table":
                        transformDb2Table();
                        break;
                    case "CICSDB2Conn":
                        transformCICSDB2Conn();
                        break;
                    case "CICSPlex":
                        transformCICSPlex();
                        break;
                    case "AddressSpace":
                        transformAddressSpace();
                        break;
                    case "BindAddress":
                        transformBindAddress();
                        break;
                    case "Fqdn":
                        transformFqdn();
                        break;
                    case "IpAddress":
                        transformIpAddress();
                        break;
                    case "IpInterface":
                        transformIpInterface();
                        break;
                    case "ProcessPool":
                        transformProcessPool();
                        break;
                    case "TcpPort":
                        transformTcpPort();
                        break;
                    case "UdpPort":
                        transformUdpPort();
                        break;
                    case "MQSubsystem":
                        transformMQSubsystem();
                        break;
                    case "MQAliasQueue":
                        transformMQAliasQueue();
                        break;
                    case "MQAuthInfo":
                        transformMQAuthInfo();
                        break;
                    case "MQBufferPool":
                        transformMQBufferPool();
                        break;
                    case "MQClientConnectionChannel":
                        transformMQClientConnectionChannel();
                        break;
                    case "MQClusterReceiverChannel":
                        transformMQClusterReceiverChannel();
                        break;
                    case "MQClusterSenderChannel":
                        transformMQClusterSenderChannel();
                        break;
                    case "MQLocalQueue":
                        transformMQLocalQueue();
                        break;
                    case "MQModelQueue":
                        transformMQModelQueue();
                        break;
                    case "MQReceiverChannel":
                        transformMQReceiverChannel();
                        break;
                    case "MQRemoteQueue":
                        transformMQRemoteQueue();
                        break;
                    case "MQSenderChannel":
                        transformMQSenderChannel();
                        break;
                    case "MQServerConnectionChannel":
                        transformMQServerConnectionChannel();
                        break;
                    case "IMSSubsystem":
                        transformIMSSubsystem();
                        break;
                    case "IMSDatabase":
                        transformIMSDatabase();
                        break;
                    case "IMSProgram":
                        transformIMSProgram();
                        break;
                    case "IMSTransaction":
                        transformIMSTransaction();
                        break;
                    default:
                        break;
                }

            }

            transformRelationships(include);
            FileObserverLoader fileObserverLoader = new FileObserverLoader();
            String author;
            if (optionConfig.has("api_key")) {
                author = fileObserverLoader.getToken(authorizeUrl, user, optionConfig.get("api_key").getAsString());
            } else if (optionConfig.has("password")) {
                author = fileObserverLoader.getBasic(user, optionConfig.get("password").getAsString());
            } else {
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "runInclude",
                        "Missing api_key or password in persist config");
                String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM", "");
                throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "runInclude");
            }
            fileObserverLoader.loadData(topologyUrl, fileObserverUrl, tenantID,
                    fileObserverTransformation.getFileList(), author);

        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "runInclude", e.toString());
            String errCode = ErrorCode.PluginError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM", "");
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "runInclude");
        }

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "runInclude", "copy data from PostgresQL into WAIOps successfully");
        return "copy data from PostgresQL into WAIOps successfully.";
    }

    public String run(PipelineConf pipelineConf) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", "Running CopyIntoASMt");
        String parameter = pipelineConf.getParameter();
        String persistID = pipelineConf.getPersist().get(0).toString();

        ConfigManager configManager = ConfigManager.getConfigManager();
        JsonObject jsonObject = configManager.searchConfig("persist", persistID);

        if (jsonObject == null) {
            String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM");
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", "Error: {}", msg);
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "runInclude");
        }

        JsonElement element = JsonParser.parseString(parameter);
        setRepository();
        String result;
        if (element.isJsonPrimitive()) {
            String tenantID = element.getAsString();
            result = runAll(jsonObject, tenantID);
        } else if (element.isJsonObject() && element.getAsJsonObject().has("tenantID")) {
            JsonObject parameterObject = element.getAsJsonObject();
            String tenantID = parameterObject.get("tenantID").getAsString();
            if (parameterObject.has("include") && parameterObject.get("include").isJsonArray()) {
                result = runInclude(jsonObject, tenantID, parameterObject.get("include").getAsJsonArray());
            } else {
                result = runAll(jsonObject, tenantID);
            }
        } else {
            String errCode = ErrorCode.ProcessError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoASM");
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", "Error: {}", msg);
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "run");
        }
        return result;
    }
}
