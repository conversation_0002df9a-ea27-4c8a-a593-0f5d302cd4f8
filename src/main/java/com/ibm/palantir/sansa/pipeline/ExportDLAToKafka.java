/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.pipeline;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;

import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.AddressSpace;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.BindAddress;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSDB2Conn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSFile;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSPlex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTable;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTableOverride;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSTransaction;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2BufferPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2DataSharingGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Database;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Subsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Table;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2TableSpace;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Fqdn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSDatabase;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSSubsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSTransaction;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IdmlOperationTime;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IpAddress;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IpInterface;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.LPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQAliasQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQAuthInfo;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQBufferPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQClientConnectionChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQClusterReceiverChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQClusterSenderChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQLocalQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQModelQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQReceiverChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQRemoteQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQSenderChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQServerConnectionChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQSubsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ProcessPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Relationship;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Sysplex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.TcpPort;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.UdpPort;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZOS;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSeriesComputer;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSubSystem;
import com.ibm.palantir.catelyn.jpa.repository.dla.AddressSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.BindAddressRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSDB2ConnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSFileRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSPlexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSRegionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableOverrideRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2BufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DataSharingGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2SubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.FqdnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSDatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IdmlOperationTimeRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IpAddressRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IpInterfaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.LPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAliasQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAuthInfoRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQBufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClientConnectionChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClusterReceiverChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClusterSenderChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQLocalQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQModelQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQReceiverChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQRemoteQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSenderChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQServerConnectionChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ProcessPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.SysplexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.TcpPortRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.UdpPortRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZOSRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSeriesComputerRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSubSystemRepository;
import com.ibm.palantir.catelyn.loader.KafkaLoader;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.catelyn.util.GsonUtils;

public class ExportDLAToKafka extends AbstractPipeline {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();

    private static final String CLASSNAME = ExportDLAToKafka.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private ZSeriesComputerRepository zSeriesComputerRepository;
    private LPARRepository lparRepository;
    private ZOSRepository zOSRepository;
    private SysplexRepository sysplexRepository;
    private ZSubSystemRepository zSubSystemRepository;
    private CICSRegionRepository cicsRegionRepository;
    private CICSSystemInitTableRepository cicsSystemInitTableRepository;
    private CICSSystemInitTableOverrideRepository cicsSystemInitTableOverrideRepository;
    private CICSProgramRepository cicsProgramRepository;
    private CICSTransactionRepository cicsTransactionRepository;
    private CICSFileRepository cicsFileRepository;
    private DB2SubsystemRepository db2SubsystemRepository;
    private DB2DataSharingGroupRepository db2DataSharingGroupRepository;
    private DB2DatabaseRepository db2DatabaseRepository;
    private DB2TableSpaceRepository db2TableSpaceRepository;
    private DB2TableRepository db2TableRepository;
    private DB2BufferPoolRepository db2BufferPoolRepository;
    private CICSDB2ConnRepository cicsdb2ConnRepository;
    private CICSPlexRepository cicsPlexRepository;
    private RelationshipRepository relationshipRepository;
    private AddressSpaceRepository addressSpaceRepository;
    private BindAddressRepository bindAddressRepository;
    private FqdnRepository fqdnRepository;
    private IpAddressRepository ipAddressRepository;
    private IpInterfaceRepository ipInterfaceRepository;
    private ProcessPoolRepository processPoolRepository;
    private TcpPortRepository tcpPortRepository;
    private UdpPortRepository udpPortRepository;
    private IdmlOperationTimeRepository idmlOperationTimeRepository;
    private MQSubsystemRepository mqSubsystemRepository;
    private MQAliasQueueRepository mqAliasQueueRepository;
    private MQAuthInfoRepository mqAuthInfoRepository;
    private MQBufferPoolRepository mqBufferPoolRepository;
    private MQClientConnectionChannelRepository mqClientConnectionChannelRepository;
    private MQClusterReceiverChannelRepository mqClusterReceiverChannelRepository;
    private MQClusterSenderChannelRepository mqClusterSenderChannelRepository;
    private MQLocalQueueRepository mqLocalQueueRepository;
    private MQModelQueueRepository mqModelQueueRepository;
    private MQReceiverChannelRepository mqReceiverChannelRepository;
    private MQRemoteQueueRepository mqRemoteQueueRepository;
    private MQSenderChannelRepository mqSenderChannelRepository;
    private MQServerConnectionChannelRepository mqServerConnectionChannelRepository;
    private IMSSubsystemRepository imsSubsystemRepository;
    private IMSDatabaseRepository imsDatabaseRepository;
    private IMSProgramRepository imsProgramRepository;
    private IMSTransactionRepository imsTransactionRepository;

    private KafkaLoader kafkaLoader;

    private void exportZSeriesComputer() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportZSeriesComputer", "Running exportZSeriesComputer");
        List<ZSeriesComputer> zSeriesComputerList = zSeriesComputerRepository.findAll();
        for (ZSeriesComputer zSeriesComputer : zSeriesComputerList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(zSeriesComputer));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportZSeriesComputer", "finish exportZSeriesComputer to Kafka");
    }

    private void exportLpar() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportLpar", "Running exportLpar");
        List<LPAR> lparList = lparRepository.findAll();
        for (LPAR lpar : lparList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(lpar));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportLpar", "finish exportLpar to Kafka");
    }

    private void exportSysplex() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportSysplex", "Running exportSysplex");
        List<Sysplex> sysplexList = sysplexRepository.findAll();
        for (Sysplex sysplex : sysplexList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(sysplex));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportSysplex", "finish exportSysplex to Kafka");
    }

    private void exportZOS() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportZOS", "Running exportZOS");
        List<ZOS> zosList = zOSRepository.findAll();
        for (ZOS zos : zosList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(zos));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportZOS", "finish exportZOS to Kafka");
    }

    private void exportZSubSystem() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportZSubSystem", "Running exportZSubSystem");
        List<ZSubSystem> zSubSystemList = zSubSystemRepository.findAll();
        for (ZSubSystem zSubSystem : zSubSystemList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(zSubSystem));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportZSubSystem", "finish exportZSubSystem to Kafka");
    }

    // ---------------------------------------CICS
    // group-----------------------------------------
    private void exportCICSRegion() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSRegion", "Running exportCICSRegion");
        List<CICSRegion> cicsRegionList = cicsRegionRepository.findAll();
        for (CICSRegion cicsRegion : cicsRegionList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(cicsRegion));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSRegion", "finish exportCICSRegion to Kafka");
    }

    private void exportCICSSit() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSSit", "Running exportCICSSit");
        List<CICSSystemInitTable> cicsSitList = cicsSystemInitTableRepository.findAll();
        for (CICSSystemInitTable cicsSit : cicsSitList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(cicsSit));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSSit", "finish exportCICSSit to Kafka");
    }

    private void exportCICSSitOverride() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSSitOverride", "Running exportCICSSitOverride");
        List<CICSSystemInitTableOverride> cicsSitOverridList = cicsSystemInitTableOverrideRepository
                .findAll();
        for (CICSSystemInitTableOverride cicsSitOverride : cicsSitOverridList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(cicsSitOverride));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSSitOverride", "finish exportCICSSitOverride to Kafka");
    }

    private void exportCICSProgram() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSProgram", "Running exportCICSProgram");
        List<CICSProgram> cicsProgramList = cicsProgramRepository.findAll();
        for (CICSProgram cicsProgram : cicsProgramList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(cicsProgram));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSProgram", "finish exportCICSProgram to Kafka");
    }

    private void exportCICSTransaction() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSTransaction", "Running exportCICSTransaction");
        List<CICSTransaction> transactionList = cicsTransactionRepository.findAll();
        for (CICSTransaction cicsTransaction : transactionList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(cicsTransaction));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSTransaction", "finish exportCICSTransaction to Kafka");
    }

    private void exportCICSFile() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSFile", "Running exportCICSFile");
        List<CICSFile> fileList = cicsFileRepository.findAll();
        for (CICSFile cicsFile : fileList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(cicsFile));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSFile", "finish exportCICSFile to Kafka");
    }

    // ---------------------------------------DB2
    // group-----------------------------------------
    private void exportDb2DataSharingGroup() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportDb2DataSharingGroup", "Running exportDb2DataSharingGroup");
        List<DB2DataSharingGroup> dataSharingGroupList = db2DataSharingGroupRepository.findAll();
        for (DB2DataSharingGroup dataSharingGroup : dataSharingGroupList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(dataSharingGroup));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportDb2DataSharingGroup",
                "finish exportDb2DataSharingGroup to Kafka");
    }

    private void exportDb2Subsystem() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportDb2Subsystem", "Running exportDb2Subsystem");
        List<DB2Subsystem> db2SubsystemList = db2SubsystemRepository.findAll();
        for (DB2Subsystem db2Subsystem : db2SubsystemList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(db2Subsystem));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportDb2Subsystem", "finish exportDb2Subsystem to Kafka");
    }

    private void exportDb2Database() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportDb2Database", "Running exportDb2Database");
        List<DB2Database> db2DatabaseList = db2DatabaseRepository.findAll();
        for (DB2Database db2Database : db2DatabaseList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(db2Database));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportDb2Database", "finish exportDb2Database to Kafka");
    }

    private void exportDb2TableSpace() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportDb2TableSpace", "Running exportDb2TableSpace");
        List<DB2TableSpace> db2TableSpaceList = db2TableSpaceRepository.findAll();
        for (DB2TableSpace db2TableSpace : db2TableSpaceList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(db2TableSpace));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportDb2TableSpace", "finish exportDb2TableSpace to Kafka");
    }

    private void exportDb2BufferPool() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportDb2BufferPool", "Running exportDb2BufferPool");
        List<DB2BufferPool> bufferPoolList = db2BufferPoolRepository.findAll();
        for (DB2BufferPool bufferPool : bufferPoolList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(bufferPool));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportDb2BufferPool", "finish exportDb2BufferPool to Kafka");
    }

    // -------------------------------------MQ
    // Resources---------------------------------------
    private void exportMQSubsystem() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQSubsystem", "Running exportMQSubsystem");
        List<MQSubsystem> mqSubsystems = mqSubsystemRepository.findAll();
        for (MQSubsystem mqSubsystem : mqSubsystems) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(mqSubsystem));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQSubsystem", "finish exportMQSubsystem to Kafka");
    }

    private void exportMQAliasQueue() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQAliasQueue", "Running exportMQAliasQueue");
        List<MQAliasQueue> mqAliasQueues = mqAliasQueueRepository.findAll();
        for (MQAliasQueue mqAliasQueue : mqAliasQueues) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(mqAliasQueue));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQAliasQueue", "finish exportMQAliasQueue to Kafka");
    }

    private void exportMQAuthInfo() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQAuthInfo", "Running exportMQAuthInfo");
        List<MQAuthInfo> mqAuthInfos = mqAuthInfoRepository.findAll();
        for (MQAuthInfo mqAuthInfo : mqAuthInfos) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(mqAuthInfo));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQAuthInfo", "finish exportMQAuthInfo to Kafka");
    }

    private void exportMQBufferPool() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQBufferPool", "Running exportMQBufferPool");
        List<MQBufferPool> mqBufferPools = mqBufferPoolRepository.findAll();
        for (MQBufferPool mqBufferPool : mqBufferPools) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(mqBufferPool));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQBufferPool", "finish exportMQBufferPool to Kafka");
    }

    private void exportMQClientConnectionChannel() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQClientConnectionChannel",
                "Running exportMQClientConnectionChannel");
        List<MQClientConnectionChannel> mqClientConnectionChannels = mqClientConnectionChannelRepository.findAll();
        for (MQClientConnectionChannel mqClientConnectionChannel : mqClientConnectionChannels) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(mqClientConnectionChannel));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQClientConnectionChannel",
                "finish exportMQClientConnectionChannel to Kafka");
    }

    private void exportMQClusterReceiverChannel() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQClusterReceiverChannel",
                "Running exportMQClusterReceiverChannel");
        List<MQClusterReceiverChannel> mqClusterReceiverChannels = mqClusterReceiverChannelRepository.findAll();
        for (MQClusterReceiverChannel mqClusterReceiverChannel : mqClusterReceiverChannels) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(mqClusterReceiverChannel));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQClusterReceiverChannel",
                "finish exportMQClusterReceiverChannel to Kafka");
    }

    private void exportMQClusterSenderChannel() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQClusterSenderChannel",
                "Running exportMQClusterSenderChannel");
        List<MQClusterSenderChannel> mqClusterSenderChannels = mqClusterSenderChannelRepository.findAll();
        for (MQClusterSenderChannel mqClusterSenderChannel : mqClusterSenderChannels) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(mqClusterSenderChannel));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQClusterSenderChannel",
                "finish exportMQClusterSenderChannel to Kafka");
    }

    private void exportMQLocalQueue() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQLocalQueue", "Running exportMQLocalQueue");
        List<MQLocalQueue> mqLocalQueues = mqLocalQueueRepository.findAll();
        for (MQLocalQueue mqLocalQueue : mqLocalQueues) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(mqLocalQueue));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQLocalQueue", "finish exportMQLocalQueue to Kafka");
    }

    private void exportMQModelQueue() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQModelQueue", "Running exportMQModelQueue");
        List<MQModelQueue> modelQueues = mqModelQueueRepository.findAll();
        for (MQModelQueue modelQueue : modelQueues) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(modelQueue));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQModelQueue", "finish exportMQModelQueue to Kafka");
    }

    private void exportMQReceiverChannel() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQReceiverChannel", "Running exportMQReceiverChannel");
        List<MQReceiverChannel> mqReceiverChannels = mqReceiverChannelRepository.findAll();
        for (MQReceiverChannel mqReceiverChannel : mqReceiverChannels) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(mqReceiverChannel));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQReceiverChannel",
                "finish exportMQReceiverChannel to Kafka");
    }

    private void exportMQRemoteQueue() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQRemoteQueue", "Running exportMQRemoteQueue");
        List<MQRemoteQueue> mqRemoteQueues = mqRemoteQueueRepository.findAll();
        for (MQRemoteQueue mqRemoteQueue : mqRemoteQueues) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(mqRemoteQueue));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQRemoteQueue", "finish exportMQRemoteQueue to Kafka");
    }

    private void exportMQSenderChannel() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQSenderChannel", "Running exportMQSenderChannel");
        List<MQSenderChannel> mqSenderChannels = mqSenderChannelRepository.findAll();
        for (MQSenderChannel mqSenderChannel : mqSenderChannels) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(mqSenderChannel));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQSenderChannel", "finish exportMQSenderChannel to Kafka");
    }

    private void exportMQServerConnectionChannel() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQServerConnectionChannel",
                "Running exportMQServerConnectionChannel");
        List<MQServerConnectionChannel> mqServerConnectionChannels = mqServerConnectionChannelRepository.findAll();
        for (MQServerConnectionChannel mqServerConnectionChannel : mqServerConnectionChannels) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(mqServerConnectionChannel));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportMQServerConnectionChannel",
                "finish exportMQServerConnectionChannel to Kafka");
    }

    // -------------------------------------IMS
    // Resources---------------------------------------
    private void exportIMSSubsystem() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIMSSubsystem", "Running exportIMSSubsystem");
        List<IMSSubsystem> imsSubsystems = imsSubsystemRepository.findAll();
        for (IMSSubsystem imsSubsystem : imsSubsystems) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(imsSubsystem));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIMSSubsystem", "finish exportIMSSubsystem to Kafka");
    }

    private void exportIMSDatabase() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIMSDatabase", "Running exportIMSDatabase");
        List<IMSDatabase> imsDatabaseList = imsDatabaseRepository.findAll();
        for (IMSDatabase imsDatabase : imsDatabaseList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(imsDatabase));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIMSDatabase", "finish exportIMSDatabase to Kafka");
    }

    private void exportIMSProgram() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIMSProgram", "Running exportIMSProgram");
        List<IMSProgram> imsPrograms = imsProgramRepository.findAll();
        for (IMSProgram imsProgram : imsPrograms) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(imsProgram));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIMSProgram", "finish exportIMSProgram to Kafka");
    }

    private void exportIMSTransaction() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIMSTransaction", "Running exportIMSTransaction");
        List<IMSTransaction> imsTransactions = imsTransactionRepository.findAll();
        for (IMSTransaction imsTransaction : imsTransactions) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(imsTransaction));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIMSTransaction", "finish exportIMSTransaction to Kafka");
    }

    // -------------------------------------Other
    // Resources------------------------------------
    private void exportCICSDB2Conn() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSDB2Conn", "Running exportCICSDB2Conn");
        List<CICSDB2Conn> cicsdb2ConnList = cicsdb2ConnRepository.findAll();
        for (CICSDB2Conn cicsdb2Conn : cicsdb2ConnList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(cicsdb2Conn));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSDB2Conn", "finish exportCICSDB2Conn to Kafka");
    }

    private void exportCICSPlex() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSPlex", "Running exportCICSPlex");
        List<CICSPlex> cicsPlexeList = cicsPlexRepository.findAll();
        for (CICSPlex cicsPlex : cicsPlexeList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(cicsPlex));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportCICSPlex", "finish exportCICSPlex to Kafka");
    }

    private void exportDb2Table() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportDb2Table", "Running exportDb2Table");
        List<DB2Table> tableList = db2TableRepository.findAll();
        for (DB2Table db2Table : tableList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(db2Table));
            ;
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportDb2Table", "finish exportDb2Table to Kafka");
    }

    private void exportRelationships() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportRelationships", "Running exportRelationships");
        List<Relationship> relationshipList = relationshipRepository.findAll();
        Integer count = 0;
        for (Relationship relationship : relationshipList) {
            count++;
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(relationship));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportRelationships",
                String.format("finish exportRelationship to Kafka, totally %d relationships.", count));
    }

    private void exportAddressSpace() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportAddressSpace", "Running exportAddressSpace");
        List<AddressSpace> addressSpaceList = addressSpaceRepository.findAll();
        for (AddressSpace addressSpace : addressSpaceList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(addressSpace));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportAddressSpace", "finish exportAddressSpace to Kafka");
    }

    private void exportBindAddress() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportBindAddress", "Running exportBindAddress");
        List<BindAddress> bindAddressList = bindAddressRepository.findAll();
        for (BindAddress bindAddress : bindAddressList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(bindAddress));
            ;
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportBindAddress", "finish exportBindAddress to Kafka");
    }

    private void exportFqdn() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportFqdn", "Running exportFqdn");
        List<Fqdn> fqdnList = fqdnRepository.findAll();
        for (Fqdn fqdn : fqdnList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(fqdn));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportFqdn", "finish exportFqdn to Kafka");
    }

    private void exportIpAddress() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIpAddress", "Running exportIpAddress");
        List<IpAddress> ipAddressList = ipAddressRepository.findAll();
        for (IpAddress ipAddress : ipAddressList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(ipAddress));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIpAddress", "finish exportIpAddress to Kafka");
    }

    private void exportIpInterface() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIpInterface", "Running exportIpInterface");
        List<IpInterface> ipInterfaceList = ipInterfaceRepository.findAll();
        for (IpInterface ipInterface : ipInterfaceList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(ipInterface));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIpInterface", "finish exportIpInterface to Kafka");
    }

    private void exportProcessPool() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportProcessPool", "Running exportProcessPool");
        List<ProcessPool> processPoolList = processPoolRepository.findAll();
        for (ProcessPool processPool : processPoolList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(processPool));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportProcessPool", "finish exportProcessPool to Kafka");
    }

    private void exportTcpPort() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportTcpPort", "Running exportTcpPort");
        List<TcpPort> tcpPortList = tcpPortRepository.findAll();
        for (TcpPort tcpPort : tcpPortList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(tcpPort));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportTcpPort", "finish exportTcpPort to Kafka");
    }

    private void exportUdpPort() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportUdpPort", "Running exportUdpPort");
        List<UdpPort> udpPortList = udpPortRepository.findAll();
        for (UdpPort udpPort : udpPortList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(udpPort));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportUdpPort", "finish exportUdpPort to Kafka");
    }

    private void exportIdmlOperationTime() throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIdmlOperationTime", "Running exportIdmlOperationTime");
        List<IdmlOperationTime> operationTimeList = idmlOperationTimeRepository.findAll();
        for (IdmlOperationTime operationTime : operationTimeList) {
            kafkaLoader.sendMessage(GsonUtils.toJsonStringWithNull(operationTime));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "exportIdmlOperationTime",
                "finish exportIdmlOperationTime to Kafka");
    }

    public String excute(PipelineConf pipelineConf) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "excute", "Running excute");
        HashMap<String, Object> repos = JPAManager.getJPAManager().getRepos();

        this.zSeriesComputerRepository = (ZSeriesComputerRepository) repos.get("zSeriesComputer");
        this.lparRepository = (LPARRepository) repos.get("lpar");
        this.zOSRepository = (ZOSRepository) repos.get("zos");
        this.sysplexRepository = (SysplexRepository) repos.get("sysplex");
        this.zSubSystemRepository = (ZSubSystemRepository) repos.get("zSubSystem");
        this.cicsRegionRepository = (CICSRegionRepository) repos.get("cicsRegion");
        this.cicsSystemInitTableRepository = (CICSSystemInitTableRepository) repos.get("cicsSystemInitTable");
        this.cicsSystemInitTableOverrideRepository = (CICSSystemInitTableOverrideRepository) repos
                .get("cicsSystemInitTableOverride");
        this.cicsProgramRepository = (CICSProgramRepository) repos.get("cicsProgram");
        this.cicsTransactionRepository = (CICSTransactionRepository) repos.get("cicsTransaction");
        this.cicsFileRepository = (CICSFileRepository) repos.get("cicsFile");
        this.db2SubsystemRepository = (DB2SubsystemRepository) repos.get("db2Subsystem");
        this.db2DataSharingGroupRepository = (DB2DataSharingGroupRepository) repos.get("db2dataSharingGroup");
        this.db2DatabaseRepository = (DB2DatabaseRepository) repos.get("db2Database");
        this.db2TableSpaceRepository = (DB2TableSpaceRepository) repos.get("db2TableSpace");
        this.db2TableRepository = (DB2TableRepository) repos.get("db2Table");
        this.db2BufferPoolRepository = (DB2BufferPoolRepository) repos.get("db2BufferPool");
        this.cicsdb2ConnRepository = (CICSDB2ConnRepository) repos.get("cicsdb2Conn");
        this.cicsPlexRepository = (CICSPlexRepository) repos.get("cicsPlex");
        this.relationshipRepository = (RelationshipRepository) repos.get("relationship");
        this.addressSpaceRepository = (AddressSpaceRepository) repos.get("addressSpace");
        this.bindAddressRepository = (BindAddressRepository) repos.get("bindAddress");
        this.fqdnRepository = (FqdnRepository) repos.get("fqdn");
        this.ipAddressRepository = (IpAddressRepository) repos.get("ipAddress");
        this.ipInterfaceRepository = (IpInterfaceRepository) repos.get("ipInterface");
        this.processPoolRepository = (ProcessPoolRepository) repos.get("processPool");
        this.tcpPortRepository = (TcpPortRepository) repos.get("tcpPort");
        this.udpPortRepository = (UdpPortRepository) repos.get("udpPort");
        this.idmlOperationTimeRepository = (IdmlOperationTimeRepository) repos.get("idmlOperationTime");
        this.mqSubsystemRepository = (MQSubsystemRepository) repos.get("mqSubsystem");
        this.mqAliasQueueRepository = (MQAliasQueueRepository) repos.get("mqAliasQueue");
        this.mqAuthInfoRepository = (MQAuthInfoRepository) repos.get("mqAuthInfo");
        this.mqBufferPoolRepository = (MQBufferPoolRepository) repos.get("mqBufferPool");
        this.mqClientConnectionChannelRepository = (MQClientConnectionChannelRepository) repos
                .get("mqClientConnectionChannel");
        this.mqClusterReceiverChannelRepository = (MQClusterReceiverChannelRepository) repos
                .get("mqClusterReceiverChannel");
        this.mqClusterSenderChannelRepository = (MQClusterSenderChannelRepository) repos.get("mqClusterSenderChannel");
        this.mqLocalQueueRepository = (MQLocalQueueRepository) repos.get("mqLocalQueue");
        this.mqModelQueueRepository = (MQModelQueueRepository) repos.get("mqModelQueue");
        this.mqReceiverChannelRepository = (MQReceiverChannelRepository) repos.get("mqReceiverChannel");
        this.mqRemoteQueueRepository = (MQRemoteQueueRepository) repos.get("mqRemoteQueue");
        this.mqSenderChannelRepository = (MQSenderChannelRepository) repos.get("mqSenderChannel");
        this.mqServerConnectionChannelRepository = (MQServerConnectionChannelRepository) repos
                .get("mqServerConnectionChannel");
        this.imsSubsystemRepository = (IMSSubsystemRepository) repos.get("imsSubsystem");
        this.imsDatabaseRepository = (IMSDatabaseRepository) repos.get("imsDatabase");
        this.imsProgramRepository = (IMSProgramRepository) repos.get("imsProgram");
        this.imsTransactionRepository = (IMSTransactionRepository) repos.get("imsTransaction");

        String persistID = pipelineConf.getPersist().get(0).toString();

        try {
            this.kafkaLoader = new KafkaLoader(persistID);

            exportZSeriesComputer();
            exportLpar();
            exportSysplex();
            exportZOS();
            exportZSubSystem();
            exportCICSRegion();
            exportCICSSit();
            exportCICSSitOverride();
            exportCICSProgram();
            exportCICSTransaction();
            exportCICSFile();
            exportDb2DataSharingGroup();
            exportDb2Subsystem();
            exportDb2Database();
            exportDb2TableSpace();
            exportDb2BufferPool();
            exportDb2Table();

            exportCICSDB2Conn();
            exportCICSPlex();

            exportAddressSpace();
            exportBindAddress();
            exportFqdn();
            exportIpAddress();
            exportIpInterface();
            exportProcessPool();
            exportTcpPort();
            exportUdpPort();

            exportMQSubsystem();
            exportMQAliasQueue();
            exportMQAuthInfo();
            exportMQBufferPool();
            exportMQClientConnectionChannel();
            exportMQClusterReceiverChannel();
            exportMQClusterSenderChannel();
            exportMQLocalQueue();
            exportMQModelQueue();
            exportMQRemoteQueue();
            exportMQReceiverChannel();
            exportMQSenderChannel();
            exportMQServerConnectionChannel();

            exportIMSSubsystem();
            exportIMSDatabase();
            exportIMSProgram();
            exportIMSTransaction();

            exportIdmlOperationTime();

            exportRelationships();

            kafkaLoader.flush();
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "execute", "Raise error during ExportDLAToKafka: {}",
                    e.toString());
            String errCode = ErrorCode.PipelineError.getCodeStr();
            String errMsg = MessageFormat.format(MsgTemp.get(errCode), "ExportDLAToKafka", ".");
            throw new ServiceException(errCode, errMsg, REPONAME, CLASSNAME, "execute");
        } finally {
            kafkaLoader.close();
        }

        return "Pipeline ExportDLAToKafka runs ok.";
    }

    public String run(PipelineConf pipelineConf) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", "Running ExportDLAToKafka");
        return excute(pipelineConf);
    }
}
