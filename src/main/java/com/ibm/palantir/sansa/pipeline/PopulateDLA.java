/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.pipeline;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

import org.hibernate.Session;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.entity.manage.FileTrace;
import com.ibm.palantir.catelyn.jpa.repository.manage.FileTraceRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.sansa.exception.ServiceExceptionStore;
import com.ibm.palantir.sansa.extractor.ElementExtractor;
import com.ibm.palantir.sansa.parser.DLADataParser;
import com.ibm.palantir.sansa.parser.DLATraceParser;
import com.ibm.palantir.sansa.utils.CommonUtils;
import com.ibm.palantir.sansa.utils.PipeCounter;

public class PopulateDLA extends AbstractPipeline {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();

    private static final String CLASSNAME = PopulateDLA.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private int CACHE_SIZE = 10;

    private FileTraceRepository fileTraceRepository;
    private PipeCounter totalCounter;
    private CommonUtils commonUtils;
    private static final ServiceExceptionStore errorStore = ServiceExceptionStore.getInstance();

    public PipeCounter runMain(FileTrace fileTrace, String fileUrl, String ip, String account, String pwd)
            throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "runMain", "Running runMain with params - fileUrl: {}", fileUrl);
        // fileUrl: local fileName or remote filePath in FileServer for the XML
        // ip, account, pwd: infos for the remote file server
        // 1st, resolve the idml book
        ElementExtractor elementExtractor = new ElementExtractor(fileUrl, ip, account, pwd);

        // failed to create ElementExtractor
        if (elementExtractor.isEmpty) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "runMain",
                    "Pipeline PopulateDeltaDLA failed to create ElementExtractor from {}", fileUrl);
            return null;
        }

        // save the file's scan date before parsing process
        fileTrace.setScanDate(elementExtractor.createTimestamp);
        fileTraceRepository.save(fileTrace);

        DLADataParser dlaDataParser = new DLADataParser(elementExtractor, null,
                null, null);
        PipeCounter pipeCounter = dlaDataParser.populateDlaFromIdml(false, false);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "runMain", "Pipeline PopulateDLA finish paring {}", fileUrl);

        if (pipeCounter != null) {
            // go to update all object's data trace
            DLATraceParser dlaTraceParser = new DLATraceParser(
                    elementExtractor, dlaDataParser.getCpcPrefixId(), dlaDataParser.getZosPrefixId(),
                    dlaDataParser.getSmfId(), dlaDataParser.getLparName(), pipeCounter.getFailedDlaIds());
            dlaTraceParser.updateScanDateFromElementExtractor();
            dlaTraceParser.updateScanDateForZSubsystem(dlaDataParser.getZSubSystemList());

            // Save Filed Dla Id's count to file_trace table
            if (!pipeCounter.getFailedDlaIds().isEmpty()) {
                fileTrace.setFailedDlaIdsCount(Long.valueOf(pipeCounter.getFailedDlaIds().size()));
                fileTraceRepository.save(fileTrace);
            }
        }
        return pipeCounter;
    }

    public void cleanMemory() {
        this.totalCounter = null;
        this.fileTraceRepository = null;
        System.gc();
    }

    public String run(PipelineConf pipelineConf) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", "Running PopulateDLA");

        String parameter = pipelineConf.getParameter();
        // Clear any previously stored errors
        errorStore.clear();
        String paramFormatErrorCode = ErrorCode.PluginParamFormatErrorWithMsg.getCodeStr();

        if (parameter == null) {
            String errCode = ErrorCode.PluginNullParamError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "PopulateDLA");
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", "Error: {}", msg);
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "run");
        }

        JsonElement jsonElement = JsonParser.parseString(parameter);
        if (jsonElement.isJsonObject()) {
            JsonObject jsonObject = jsonElement.getAsJsonObject();

            String ip = "";
            String account = "";
            String pwd = "";
            if (jsonObject.has("ip") && jsonObject.has("account") && jsonObject.has("password")) {
                ip = jsonObject.get("ip").getAsString();
                account = jsonObject.get("account").getAsString();
                pwd = jsonObject.get("password").getAsString();
            }

            if (!jsonObject.has("fileList")) {
                String msg = MessageFormat.format(MsgTemp.get(paramFormatErrorCode), "Sansa", "PopulateDLA",
                        "There is no fileList key in parameter json object! \nParameter: " + parameter);
                LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "run", "Error: {}", msg);
                throw new ServiceException(paramFormatErrorCode, msg, REPONAME, CLASSNAME, "run");
            }

            try {
                // new totalCounter to count the whole fileList
                this.totalCounter = new PipeCounter();
                // init after checking the fileList parameter
                pipelineInit();

                JsonArray jsonArray = jsonObject.get("fileList").getAsJsonArray();
                List<String> failedFileList = new ArrayList<>();
                List<String> noTraceFileList = new ArrayList<>();
                for (int i = 0; i < jsonArray.size(); i++) {
                    String fileName = jsonArray.get(i).getAsString();

                    FileTrace fileTrace = fileTraceRepository.getTraceByFilePath(fileName);
                    if (fileTrace == null) {
                        String str = String.format("Skipped unexpected filePath '%s' which has no fileTrace record.",
                                fileName);
                        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "run", str);
                        noTraceFileList.add(fileName);
                        continue;
                    }

                    this.commonUtils = new CommonUtils();
                    PipeCounter pipeCounter = runMain(fileTrace, fileName, ip, account, pwd);

                    // fail to extract the IDML book
                    if (pipeCounter == null) {
                        failedFileList.add(fileName);
                        continue;
                    } else {
                        this.totalCounter.mergeCounter(fileName, pipeCounter);
                    }
                    if (i % CACHE_SIZE == (CACHE_SIZE - 1)) {
                        Session session = JPAManager.getJPAManager().getEntityManager().unwrap(Session.class);
                        session.clear();
                        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run",
                                "Clear JPA session cache reaching cache size 10.");
                    }
                }

                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", this.totalCounter.getStrContent());

                String respMsg = "PopulateDLA pipeline finished.\n ";
                respMsg += String.format("Successfully parsed %d files and saved %d objects data into the database.\n ",
                        this.totalCounter.countFile(), this.totalCounter.getTotal());
                if (!failedFileList.isEmpty()) {
                    String str = String.format("Failed to extract %d files: %s.\n ", failedFileList.size(),
                            String.join(", ", failedFileList));
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "run", str);
                    respMsg += str;
                }

                if (!totalCounter.getFailedDlaIds().isEmpty()) {
                    String str = String.format("The data contains %d unexpected objects. Please check the details.\n ",
                            totalCounter.getFailedDlaIds().size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", str);
                    respMsg += str;
                }

                if (!noTraceFileList.isEmpty()) {
                    String str = "";
                    if (noTraceFileList.size() == 1) {
                        str = String.format("Skipped unexpected filePath '%s' because it has no fileTrace record. \n",
                                noTraceFileList.get(0));
                    } else {
                        str = String.format(
                                "Skipped %d unexpected filePaths '%s' because they have no fileTrace record. \n",
                                noTraceFileList.size(), String.join(", ", noTraceFileList));
                    }
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "run", str);
                    respMsg += str;
                }
                Session session = JPAManager.getJPAManager().getEntityManager().unwrap(Session.class);
                errorStore.throwIfAny();
                errorStore.clear();
                session.clear();
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", "Clear JPA session cache.");
                cleanMemory();
                return respMsg;
            } catch (Exception e) {
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", e.toString());
                if(e instanceof ServiceException){
                     // Throw ServiceException directly without replacing it with a generic message
                    throw e;
                }
                String errCode = ErrorCode.PluginError.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "PopulateDLA", parameter);
                throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "run");
            }
        } else if (jsonElement.isJsonPrimitive()) {
            try {
                pipelineInit();

                FileTrace fileTrace = fileTraceRepository.getTraceByFilePath(parameter);
                if (fileTrace == null) {
                    String str = String.format("Skips the unexpected filePath %s, which has no fileTrace record.",
                            parameter);
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "run", str);
                    return str;
                }

                PipeCounter pipeCounter = runMain(fileTrace, parameter, "", "", "");
                Session session = JPAManager.getJPAManager().getEntityManager().unwrap(Session.class);
                errorStore.throwIfAny();
                errorStore.clear();
                session.clear();
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", "Clear JPA session cache.");
                if (pipeCounter == null) {
                    return "Fail to extract idml book file: " + parameter;
                } else {
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", pipeCounter.getStrContent());
                    return "Pipeline PopulateDLA finish parsing " + parameter;
                }
            } catch (ServiceException se){
                // Throw ServiceException directly without replacing it with a generic message
                throw se;
            } catch (Exception e) {
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", e.toString());
                String errCode = ErrorCode.PluginError.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "PopulateDLA", parameter);
                throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "run");
            }
        } else {
            String msg = MessageFormat.format(MsgTemp.get(paramFormatErrorCode), "Sansa", "PopulateDLA",
                                "The parameter type is not JSON or String! \nParameter: " + parameter);            
            LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "run", "Error: {}", msg);
            throw new ServiceException(paramFormatErrorCode, msg, REPONAME, CLASSNAME, "run");
        }
    }

    private void pipelineInit() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "pipelineInit", "Running pipelineInit");
        this.fileTraceRepository = (FileTraceRepository) JPAManager.getJPAManager().getRepos().get("fileTrace");
    }

}
