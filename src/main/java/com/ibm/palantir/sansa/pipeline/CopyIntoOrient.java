/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.pipeline;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;

import com.google.gson.JsonObject;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.AddressSpace;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.BindAddress;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSDB2Conn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSFile;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSPlex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTable;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTableOverride;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSTransaction;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2BufferPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2DataSharingGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Database;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2StoredProcedure;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Subsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Table;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2TableSpace;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Fqdn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSDatabase;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSSubsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSSysplexGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSTransaction;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IdmlOperationTime;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IpAddress;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IpInterface;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.LPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQAliasQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQAuthInfo;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQBufferPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQClientConnectionChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQClusterReceiverChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQClusterSenderChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQLocalQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQModelQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQQueueSharingGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQReceiverChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQRemoteQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQSenderChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQServerConnectionChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQSubsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ProcessPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Relationship;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Sysplex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.TcpPort;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.UdpPort;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZOS;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSeriesComputer;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSubSystem;
import com.ibm.palantir.catelyn.jpa.repository.dla.AddressSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.BindAddressRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSDB2ConnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSFileRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSPlexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSRegionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableOverrideRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2BufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DataSharingGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2StoredProcedureRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2SubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.FqdnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSDatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSSysplexGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IdmlOperationTimeRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IpAddressRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IpInterfaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.LPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAliasQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAuthInfoRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQBufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClientConnectionChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClusterReceiverChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClusterSenderChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQLocalQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQModelQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQQueueSharingGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQReceiverChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQRemoteQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSenderChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQServerConnectionChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ProcessPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.SysplexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.TcpPortRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.UdpPortRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZOSRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSeriesComputerRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSubSystemRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.sansa.transfromation.OrientRelationshipsBuilder;
import com.ibm.palantir.sansa.transfromation.OrientTransformation;
import com.orientechnologies.orient.core.db.ODatabaseSession;
import com.orientechnologies.orient.core.db.ODatabaseType;
import com.orientechnologies.orient.core.db.OrientDB;
import com.orientechnologies.orient.core.db.OrientDBConfig;

public class CopyIntoOrient extends AbstractPipeline {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = CopyIntoOrient.class.getSimpleName();
    private static final String REPONAME = "Sansa";
    private OrientTransformation orientTransformation;
    private OrientRelationshipsBuilder orientRelationshipsBuilder;
    private ZSeriesComputerRepository zSeriesComputerRepository;
    private LPARRepository lparRepository;
    private ZOSRepository zOSRepository;
    private SysplexRepository sysplexRepository;
    private ZSubSystemRepository zSubSystemRepository;
    private CICSRegionRepository cicsRegionRepository;
    private CICSSystemInitTableRepository cicsSystemInitTableRepository;
    private CICSSystemInitTableOverrideRepository cicsSystemInitTableOverrideRepository;
    private CICSProgramRepository cicsProgramRepository;
    private CICSTransactionRepository cicsTransactionRepository;
    private CICSFileRepository cicsFileRepository;
    private DB2SubsystemRepository db2SubsystemRepository;
    private DB2DataSharingGroupRepository db2DataSharingGroupRepository;
    private DB2DatabaseRepository db2DatabaseRepository;
    private DB2TableSpaceRepository db2TableSpaceRepository;
    private DB2TableRepository db2TableRepository;
    private DB2BufferPoolRepository db2BufferPoolRepository;
    private DB2StoredProcedureRepository db2StoredProcedureRepository;
    private CICSDB2ConnRepository cicsdb2ConnRepository;
    private CICSPlexRepository cicsPlexRepository;
    private RelationshipRepository relationshipRepository;
    private AddressSpaceRepository addressSpaceRepository;
    private BindAddressRepository bindAddressRepository;
    private FqdnRepository fqdnRepository;
    private IpAddressRepository ipAddressRepository;
    private IpInterfaceRepository ipInterfaceRepository;
    private ProcessPoolRepository processPoolRepository;
    private TcpPortRepository tcpPortRepository;
    private UdpPortRepository udpPortRepository;
    private IdmlOperationTimeRepository idmlOperationTimeRepository;
    private MQSubsystemRepository mqSubsystemRepository;
    private MQAliasQueueRepository mqAliasQueueRepository;
    private MQAuthInfoRepository mqAuthInfoRepository;
    private MQBufferPoolRepository mqBufferPoolRepository;
    private MQClientConnectionChannelRepository mqClientConnectionChannelRepository;
    private MQClusterReceiverChannelRepository mqClusterReceiverChannelRepository;
    private MQClusterSenderChannelRepository mqClusterSenderChannelRepository;
    private MQLocalQueueRepository mqLocalQueueRepository;
    private MQModelQueueRepository mqModelQueueRepository;
    private MQReceiverChannelRepository mqReceiverChannelRepository;
    private MQRemoteQueueRepository mqRemoteQueueRepository;
    private MQSenderChannelRepository mqSenderChannelRepository;
    private MQServerConnectionChannelRepository mqServerConnectionChannelRepository;
    private MQQueueSharingGroupRepository mqQueueSharingGroupRepository;
    private IMSSubsystemRepository imsSubsystemRepository;
    private IMSDatabaseRepository imsDatabaseRepository;
    private IMSProgramRepository imsProgramRepository;
    private IMSTransactionRepository imsTransactionRepository;
    private IMSSysplexGroupRepository imsSysplexGroupRepository;

    private void transformZSeriesComputer() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSeriesComputer", "Running transformZSeriesComputer");
        List<ZSeriesComputer> zSeriesComputerList = zSeriesComputerRepository.findAll();
        for (ZSeriesComputer zSeriesComputer : zSeriesComputerList) {
            if (!zSeriesComputer.getSoftDel()) { // only copy those active items
                orientTransformation.createOrUpdateZSeriesComputer(zSeriesComputer);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSeriesComputer", "finish transformZSeriesComputer");
    }

    private void transformLpar() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformLpar", "Running transformLpar");
        List<LPAR> zoslparList = lparRepository.findAll();
        for (LPAR lpar : zoslparList) {
            if (!lpar.getSoftDel()) {
                orientTransformation.createOrUpdateLpar(lpar);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSeriesComputer", "finish transformZSeriesComputer");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformLpar", "finish transformLpar");
    }

    private void transformSysplex() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformSysplex", "Running transformSysplex");
        List<Sysplex> sysplexList = sysplexRepository.findAll();
        for (Sysplex sysplex : sysplexList) {
            if (!sysplex.getSoftDel()) {
                orientTransformation.createOrUpdateSysplex(sysplex);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformSysplex", "finish transformSysplex");
    }

    private void transformZOS() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZOS", "Running transformZOS");
        List<ZOS> zosList = zOSRepository.findAll();
        for (ZOS zos : zosList) {
            if (!zos.getSoftDel()) {
                orientTransformation.createOrUpdateZos(zos);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZOS", "finish transformZOS");
    }

    private void transformZSubSystem() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSubSystem", "Running transformZSubSystem");
        List<ZSubSystem> zSubSystemList = zSubSystemRepository.findAll();
        for (ZSubSystem zSubSystem : zSubSystemList) {
            if (!zSubSystem.getSoftDel()) {
                orientTransformation.createOrUpdateZSubSystem(zSubSystem);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSubSystem", "finish transformZSubSystem");
    }

    // ---------------------------------------CICS
    // group-----------------------------------------
    private void transformCICSRegion() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSRegion", "Running transformCICSRegion");
        List<CICSRegion> cicsRegionList = cicsRegionRepository.findAll();
        for (CICSRegion cicsRegion : cicsRegionList) {
            if (!cicsRegion.getSoftDel()) {
                orientTransformation.createOrUpdateCICSRegion(cicsRegion);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSRegion", "finish transformCICSRegion");
    }

    private void transformCICSSit() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSSit", "Running transformCICSSit");
        List<CICSSystemInitTable> cicsSitList = cicsSystemInitTableRepository.findAll();
        for (CICSSystemInitTable cicsSit : cicsSitList) {
            if (!cicsSit.getSoftDel()) {
                orientTransformation.createOrUpdateCICSSit(cicsSit);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSSit", "finish transformCICSSit");
    }

    private void transformCICSSitOverride() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSSitOverride", "Running transformCICSSitOverride");
        List<CICSSystemInitTableOverride> cicsSitOverridList = cicsSystemInitTableOverrideRepository
                .findAll();
        for (CICSSystemInitTableOverride cicsSitOverride : cicsSitOverridList) {
            if (!cicsSitOverride.getSoftDel()) {
                orientTransformation.createOrUpdateCICSSitOverrides(cicsSitOverride);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSSitOverride", "finish transformCICSSitOverride");
    }

    private void transformCICSProgram() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSProgram", "Running transformCICSProgram");
        List<CICSProgram> cicsProgramList = cicsProgramRepository.findAll();
        for (CICSProgram cicsProgram : cicsProgramList) {
            if (!cicsProgram.getSoftDel()) {
                orientTransformation.createOrUpdateCICSProgram(cicsProgram);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSProgram", "finish transformCICSProgram");
    }

    private void transformCICSTransaction() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSTransaction", "Running transformCICSTransaction");
        List<CICSTransaction> transactionList = cicsTransactionRepository.findAll();
        for (CICSTransaction cicsTransaction : transactionList) {
            if (!cicsTransaction.getSoftDel()) {
                orientTransformation.createOrUpdateCICSTransaction(cicsTransaction);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSTransaction", "finish transformCICSTransaction");
    }

    private void transformCICSFile() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSFile", "Running transformCICSFile");
        List<CICSFile> fileList = cicsFileRepository.findAll();
        for (CICSFile file : fileList) {
            if (!file.getSoftDel()) {
                orientTransformation.createOrUpdateCICSFile(file);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSFile", "finish transformCICSFile");
    }

    // ---------------------------------------DB2
    // group-----------------------------------------
    private void transformDb2DataSharingGroup() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2DataSharingGroup",
                "Running transformDb2DataSharingGroup");
        List<DB2DataSharingGroup> dataSharingGroupList = db2DataSharingGroupRepository.findAll();
        for (DB2DataSharingGroup dataSharingGroup : dataSharingGroupList) {
            if (!dataSharingGroup.getSoftDel()) {
                orientTransformation.createOrUpdateDb2DataSharingGroup(dataSharingGroup);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2DataSharingGroup",
                "finish transformDb2DataSharingGroup");
    }

    private void transformDb2Subsystem() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Subsystem", "Running transformDb2Subsystem");
        List<DB2Subsystem> db2SubsystemList = db2SubsystemRepository.findAll();
        for (DB2Subsystem db2Subsystem : db2SubsystemList) {
            if (!db2Subsystem.getSoftDel()) {
                orientTransformation.createOrUpdateDb2Subsystem(db2Subsystem);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "trasnformDb2Subsystem", "finish trasnformDb2Subsystem");
    }

    private void transformDb2Database() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Database", "Running transformDb2Database");
        List<DB2Database> db2DatabaseList = db2DatabaseRepository.findAll();
        for (DB2Database db2Database : db2DatabaseList) {
            if (!db2Database.getSoftDel()) {
                orientTransformation.createOrUpdateDb2Database(db2Database);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Database", "finish transformDb2Database");
    }

    private void transformDb2TableSpace() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2TableSpace", "Running transformDb2TableSpace");
        List<DB2TableSpace> db2TableSpaceList = db2TableSpaceRepository.findAll();
        for (DB2TableSpace db2TableSpace : db2TableSpaceList) {
            if (!db2TableSpace.getSoftDel()) {
                orientTransformation.createOrUpdateDb2TableSpace(db2TableSpace);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2TableSpace", "finish transformDb2TableSpace");
    }

    private void transformDb2BufferPool() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2BufferPool", "Running transformDb2BufferPool");
        List<DB2BufferPool> bufferPoolList = db2BufferPoolRepository.findAll();
        for (DB2BufferPool bufferPool : bufferPoolList) {
            if (!bufferPool.getSoftDel()) {
                orientTransformation.createOrUpdateDb2BufferPool(bufferPool);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2BufferPool", "finish transformDb2BufferPool");
    }

    private void transformDb2StoredProcedure() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2StoredProcedure",
                "Running transformDb2StoredProcedure");
        List<DB2StoredProcedure> db2StoredProcedureList = db2StoredProcedureRepository.findAll();
        for (DB2StoredProcedure db2StoredProcedure : db2StoredProcedureList) {
            if (!db2StoredProcedure.getSoftDel()) {
                orientTransformation.createOrUpdateDb2StoredProcedure(db2StoredProcedure);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2StoredProcedure",
                "finish transformDb2StoredProcedure");
    }

    // -------------------------------------MQ
    // Resources---------------------------------------
    private void transformMQSubsystem() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQSubsystem", "Running transformMQSubsystem");
        List<MQSubsystem> mqSubsystems = mqSubsystemRepository.findAll();
        for (MQSubsystem mqSubsystem : mqSubsystems) {
            if (!mqSubsystem.getSoftDel()) {
                orientTransformation.createOrUpdateMQSubsystem(mqSubsystem);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQSubsystem", "finish transformMQSubsystem");
    }

    private void transformMQAliasQueue() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQAliasQueue", "Running transformMQAliasQueue");
        List<MQAliasQueue> mqAliasQueues = mqAliasQueueRepository.findAll();
        for (MQAliasQueue mqAliasQueue : mqAliasQueues) {
            if (!mqAliasQueue.getSoftDel()) {
                orientTransformation.createOrUpdateMQAliasQueue(mqAliasQueue);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQAliasQueue", "finish transformMQAliasQueue");
    }

    private void transformMQAuthInfo() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQAuthInfo", "Running transformMQAuthInfo");
        List<MQAuthInfo> mqAuthInfos = mqAuthInfoRepository.findAll();
        for (MQAuthInfo mqAuthInfo : mqAuthInfos) {
            if (!mqAuthInfo.getSoftDel()) {
                orientTransformation.createOrUpdateMQAuthInfo(mqAuthInfo);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQAuthInfo", "finish transformMQAuthInfo");
    }

    private void transformMQBufferPool() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQBufferPool", "Running transformMQBufferPool");
        List<MQBufferPool> mqBufferPools = mqBufferPoolRepository.findAll();
        for (MQBufferPool mqBufferPool : mqBufferPools) {
            if (!mqBufferPool.getSoftDel()) {
                orientTransformation.createOrUpdateMQBufferPool(mqBufferPool);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQBufferPool", "finish transformMQBufferPool");
    }

    private void transformMQClientConnectionChannel() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQClientConnectionChannel",
                "Running transformMQClientConnectionChannel");
        List<MQClientConnectionChannel> mqClientConnectionChannels = mqClientConnectionChannelRepository.findAll();
        for (MQClientConnectionChannel mqClientConnectionChannel : mqClientConnectionChannels) {
            if (!mqClientConnectionChannel.getSoftDel()) {
                orientTransformation.createOrUpdateMQClientConnectionChannel(mqClientConnectionChannel);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQClientConnectionChannel",
                "finish transformMQClientConnectionChannel");
    }

    private void transformMQClusterReceiverChannel() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQClusterReceiverChannel",
                "Running transformMQClusterReceiverChannel");
        List<MQClusterReceiverChannel> mqClusterReceiverChannels = mqClusterReceiverChannelRepository.findAll();
        for (MQClusterReceiverChannel mqClusterReceiverChannel : mqClusterReceiverChannels) {
            if (!mqClusterReceiverChannel.getSoftDel()) {
                orientTransformation.createOrUpdateMQClusterReceiverChannel(mqClusterReceiverChannel);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQClusterReceiverChannel",
                "finish transformMQClusterReceiverChannel");
    }

    private void transformMQClusterSenderChannel() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQClusterSenderChannel",
                "Running transformMQClusterSenderChannel");
        List<MQClusterSenderChannel> mqClusterSenderChannels = mqClusterSenderChannelRepository.findAll();
        for (MQClusterSenderChannel mqClusterSenderChannel : mqClusterSenderChannels) {
            if (!mqClusterSenderChannel.getSoftDel()) {
                orientTransformation.createOrUpdateMQClusterSenderChannel(mqClusterSenderChannel);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQClusterSenderChannel",
                "finish transformMQClusterSenderChannel");
    }

    private void transformMQLocalQueue() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQLocalQueue", "Running transformMQLocalQueue");
        List<MQLocalQueue> mqLocalQueues = mqLocalQueueRepository.findAll();
        for (MQLocalQueue mqLocalQueue : mqLocalQueues) {
            if (!mqLocalQueue.getSoftDel()) {
                orientTransformation.createOrUpdateMQLocalQueue(mqLocalQueue);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQLocalQueue", "finish transformMQLocalQueue");
    }

    private void transformMQModelQueue() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQModelQueue", "Running transformMQModelQueue");
        List<MQModelQueue> modelQueues = mqModelQueueRepository.findAll();
        for (MQModelQueue modelQueue : modelQueues) {
            if (!modelQueue.getSoftDel()) {
                orientTransformation.createOrUpdateMQModelQueue(modelQueue);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQModelQueue", "finish transformMQModelQueue");
    }

    private void transformMQReceiverChannel() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQReceiverChannel", "Running transformMQReceiverChannel");
        List<MQReceiverChannel> mqReceiverChannels = mqReceiverChannelRepository.findAll();
        for (MQReceiverChannel mqReceiverChannel : mqReceiverChannels) {
            if (!mqReceiverChannel.getSoftDel()) {
                orientTransformation.createOrUpdateMQReceiverChannel(mqReceiverChannel);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQReceiverChannel", "finish transformMQReceiverChannel");
    }

    private void transformMQRemoteQueue() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQRemoteQueue", "Running transformMQRemoteQueue");
        List<MQRemoteQueue> mqRemoteQueues = mqRemoteQueueRepository.findAll();
        for (MQRemoteQueue mqRemoteQueue : mqRemoteQueues) {
            if (!mqRemoteQueue.getSoftDel()) {
                orientTransformation.createOrUpdateMQRemoteQueue(mqRemoteQueue);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQRemoteQueue", "finish transformMQRemoteQueue");
    }

    private void transformMQSenderChannel() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQSenderChannel", "Running transformMQSenderChannel");
        List<MQSenderChannel> mqSenderChannels = mqSenderChannelRepository.findAll();
        for (MQSenderChannel mqSenderChannel : mqSenderChannels) {
            if (!mqSenderChannel.getSoftDel()) {
                orientTransformation.createOrUpdateMQSenderChannel(mqSenderChannel);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQSenderChannel", "finish transformMQSenderChannel");
    }

    private void transformMQServerConnectionChannel() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQServerConnectionChannel",
                "Running transformMQServerConnectionChannel");
        List<MQServerConnectionChannel> mqServerConnectionChannels = mqServerConnectionChannelRepository.findAll();
        for (MQServerConnectionChannel mqServerConnectionChannel : mqServerConnectionChannels) {
            if (!mqServerConnectionChannel.getSoftDel()) {
                orientTransformation.createOrUpdateMQServerConnectionChannel(mqServerConnectionChannel);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQServerConnectionChannel",
                "finish transformMQServerConnectionChannel");
    }

    private void transformMQQueueSharingGroup() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQQueueSharingGroup",
                "Running transformMQQueueSharingGroup");
        List<MQQueueSharingGroup> mqQueueSharingGroups = mqQueueSharingGroupRepository.findAll();
        for (MQQueueSharingGroup mqQueueSharingGroup : mqQueueSharingGroups) {
            if (!mqQueueSharingGroup.getSoftDel()) {
                orientTransformation.createOrUpdateMQQueueSharingGroup(mqQueueSharingGroup);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformMQQueueSharingGroup",
                "finish transformMQQueueSharingGroup");
    }

    // -------------------------------------IMS
    // Resources---------------------------------------
    private void transformIMSSubsystem() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSSubsystem", "Running transformIMSSubsystem");
        List<IMSSubsystem> imsSubsystems = imsSubsystemRepository.findAll();
        for (IMSSubsystem imsSubsystem : imsSubsystems) {
            if (!imsSubsystem.getSoftDel()) {
                orientTransformation.createOrUpdateIMSSubystem(imsSubsystem);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSSubsystem", "finish transformIMSSubsystem");
    }

    private void transformIMSDatabase() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSDatabase", "Running transformIMSDatabase");
        List<IMSDatabase> imsDatabaseList = imsDatabaseRepository.findAll();
        for (IMSDatabase imsDatabase : imsDatabaseList) {
            if (!imsDatabase.getSoftDel()) {
                orientTransformation.createOrUpdateIMSDatabase(imsDatabase);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSDatabase", "finish transformIMSDatabase");
    }

    private void transformIMSProgram() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSProgram", "Running transformIMSProgram");
        List<IMSProgram> imsPrograms = imsProgramRepository.findAll();
        for (IMSProgram imsProgram : imsPrograms) {
            if (!imsProgram.getSoftDel()) {
                orientTransformation.createOrUpdateIMSProgram(imsProgram);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSProgram", "finish transformIMSProgram");
    }

    private void transformIMSTransaction() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSTransaction", "Running transformIMSTransaction");
        List<IMSTransaction> imsTransactions = imsTransactionRepository.findAll();
        for (IMSTransaction imsTransaction : imsTransactions) {
            if (!imsTransaction.getSoftDel()) {
                orientTransformation.createOrUpdateIMSTransaction(imsTransaction);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSTransaction", "finish transformIMSTransaction");
    }

    private void transformIMSSysplexGroup() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSSysplexGroup", "Running transformIMSSysplexGroup");
        List<IMSSysplexGroup> imsSysplexGroups = imsSysplexGroupRepository.findAll();
        for (IMSSysplexGroup imsSysplexGroup : imsSysplexGroups) {
            if (!imsSysplexGroup.getSoftDel()) {
                orientTransformation.createOrUpdateIMSSysplexGroup(imsSysplexGroup);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIMSSysplexGroup", "finish transformIMSSysplexGroup");
    }

    // -------------------------------------Other
    // Resources------------------------------------
    private void transformCICSDB2Conn() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSDB2Conn", "Running transformCICSDB2Conn");
        List<CICSDB2Conn> cicsdb2ConnList = cicsdb2ConnRepository.findAll();
        for (CICSDB2Conn cicsdb2Conn : cicsdb2ConnList) {
            if (!cicsdb2Conn.getSoftDel()) {
                orientTransformation.getOrCreateDB2Conn(cicsdb2Conn);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSDB2Conn", "finish transformCICSDB2Conn");
    }

    private void transformCICSPlex() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSPlex", "Running transformCICSPlex");
        List<CICSPlex> cicsPlexeList = cicsPlexRepository.findAll();
        for (CICSPlex cicsPlex : cicsPlexeList) {
            if (!cicsPlex.getSoftDel()) {
                orientTransformation.createOrUpdateCICSPlex(cicsPlex);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSPlex", "finish transformCICSPlex");
    }

    private void transformDb2Table() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Table", "Running transformDb2Table");
        List<DB2Table> tableList = db2TableRepository.findAll();
        for (DB2Table db2Table : tableList) {
            if (!db2Table.getSoftDel()) {
                orientTransformation.createOrUpdateDb2Table(db2Table);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Table", "finish transformDb2Table");
    }

    private void transformAddressSpace() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformAddressSpace", "Running transformAddressSpace");
        List<AddressSpace> addressSpaceList = addressSpaceRepository.findAll();
        for (AddressSpace addressSpace : addressSpaceList) {
            if (!addressSpace.getSoftDel()) {
                orientTransformation.createOrUpdateAddressSpace(addressSpace);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformAddressSpace", "finish transformAddressSpace");
    }

    private void transformBindAddress() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformBindAddress", "Running transformBindAddress");
        List<BindAddress> bindAddressList = bindAddressRepository.findAll();
        for (BindAddress bindAddress : bindAddressList) {
            if (!bindAddress.getSoftDel()) {
                orientTransformation.createOrUpdateBindAddress(bindAddress);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformBindAddress", "finish transformBindAddress");
    }

    private void transformFqdn() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformFqdn", "Running transformFqdn");
        List<Fqdn> fqdnList = fqdnRepository.findAll();
        for (Fqdn fqdn : fqdnList) {
            if (!fqdn.getSoftDel()) {
                orientTransformation.createOrUpdateFqdn(fqdn);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformFqdn", "finish transformFqdn");
    }

    private void transformIpAddress() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIpAddress", "Running transformIpAddress");
        List<IpAddress> ipAddressList = ipAddressRepository.findAll();
        for (IpAddress ipAddress : ipAddressList) {
            if (!ipAddress.getSoftDel()) {
                orientTransformation.createOrUpdateIpAddress(ipAddress);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIpAddress", "finish transformIpAddress");
    }

    private void transformIpInterface() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIpInterface", "Running transformIpInterface");
        List<IpInterface> ipInterfaceList = ipInterfaceRepository.findAll();
        for (IpInterface ipInterface : ipInterfaceList) {
            if (!ipInterface.getSoftDel()) {
                orientTransformation.getOrCreateIpInterface(ipInterface);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIpInterface", "finish transformIpInterface");
    }

    private void transformProcessPool() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformProcessPool", "Running transformProcessPool");
        List<ProcessPool> processPoolList = processPoolRepository.findAll();
        for (ProcessPool processPool : processPoolList) {
            if (!processPool.getSoftDel()) {
                orientTransformation.createOrUpdateProcessPool(processPool);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformProcessPool", "finish transformProcessPool");
    }

    private void transformTcpPort() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformTcpPort", "Running transformTcpPort");
        List<TcpPort> tcpPortList = tcpPortRepository.findAll();
        for (TcpPort tcpPort : tcpPortList) {
            if (!tcpPort.getSoftDel()) {
                orientTransformation.createOrUpdateTcpPort(tcpPort);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformTcpPort", "finish transformTcpPort");
    }

    private void transformUdpPort() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformUdpPort", "Running transformUdpPort");
        List<UdpPort> udpPortList = udpPortRepository.findAll();
        for (UdpPort udpPort : udpPortList) {
            if (!udpPort.getSoftDel()) {
                orientTransformation.createOrUpdateUdpPort(udpPort);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformUdpPort", "finish transformUdpPort");
    }

    private void transformIdmlOperationTime() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIdmlOperationTime", "Running transformIdmlOperationTime");
        List<IdmlOperationTime> operationTimeList = idmlOperationTimeRepository.findAll();
        for (IdmlOperationTime operationTime : operationTimeList) {
            orientTransformation.createOrUpdateIdmlOperationTime(operationTime);
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformIdmlOperationTime", "finish transformIdmlOperationTime");
    }

    private Integer transformRelationships(OrientDB orient, String dbName, String username, String password) {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformRelationships", "Running transformRelationships");
        Integer errCount = 0;
        Integer count = 0;
        Integer limit = 5000;
        Integer offset = 0;
        Boolean goFlag = true;
        Boolean retryFlag = false;

        ODatabaseSession db = orient.open(dbName, username, password);
        orientRelationshipsBuilder = new OrientRelationshipsBuilder(db);

        do {
            List<Relationship> relationshipList = relationshipRepository.findActiveAllByLimitOffset(limit, offset);

            if (relationshipList.size() > 0) {

                for (Relationship relationship : relationshipList) {

                    if (retryFlag) {
                        db = orient.open(dbName, username, password);
                        orientRelationshipsBuilder = new OrientRelationshipsBuilder(db);
                        retryFlag = false;
                    }

                    count++;
                    try {
                        orientRelationshipsBuilder.buildOrCheckRelationship(relationship);
                    } catch (Exception e) {
                        LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "transformRelationships",
                                String.format("An error is raised in the %d record.", count));
                        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "transformRelationships",
                                String.format("record detail: name:%s, source:%s, target:%s, reverseName:%s",
                                        relationship.getName(), relationship.getSource(), relationship.getTarget(),
                                        relationship.getReverseName()));

                        // skip the "bad" record
                        errCount += 1;
                        db.close();
                        retryFlag = true;
                        continue;
                    }
                }

                offset += limit;
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformRelationships",
                        String.format("transformRelationship's process is %d.", count));
            } else {
                goFlag = false;
                db.close();
            }
        } while (goFlag);

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformRelationships",
                String.format("finish transformRelationship, totally %d active relationships.", count));
        return errCount;
    }

    public String runMain(HashMap<String, String> dbConfig) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "runMain", "Running runMain");
        HashMap<String, Object> repos = JPAManager.getJPAManager().getRepos();

        this.zSeriesComputerRepository = (ZSeriesComputerRepository) repos.get("zSeriesComputer");
        this.lparRepository = (LPARRepository) repos.get("lpar");
        this.zOSRepository = (ZOSRepository) repos.get("zos");
        this.sysplexRepository = (SysplexRepository) repos.get("sysplex");
        this.zSubSystemRepository = (ZSubSystemRepository) repos.get("zSubSystem");
        this.cicsRegionRepository = (CICSRegionRepository) repos.get("cicsRegion");
        this.cicsSystemInitTableRepository = (CICSSystemInitTableRepository) repos.get("cicsSystemInitTable");
        this.cicsSystemInitTableOverrideRepository = (CICSSystemInitTableOverrideRepository) repos
                .get("cicsSystemInitTableOverride");
        this.cicsProgramRepository = (CICSProgramRepository) repos.get("cicsProgram");
        this.cicsTransactionRepository = (CICSTransactionRepository) repos.get("cicsTransaction");
        this.cicsFileRepository = (CICSFileRepository) repos.get("cicsFile");
        this.db2SubsystemRepository = (DB2SubsystemRepository) repos.get("db2Subsystem");
        this.db2DataSharingGroupRepository = (DB2DataSharingGroupRepository) repos.get("db2dataSharingGroup");
        this.db2DatabaseRepository = (DB2DatabaseRepository) repos.get("db2Database");
        this.db2TableSpaceRepository = (DB2TableSpaceRepository) repos.get("db2TableSpace");
        this.db2TableRepository = (DB2TableRepository) repos.get("db2Table");
        this.db2BufferPoolRepository = (DB2BufferPoolRepository) repos.get("db2BufferPool");
        this.db2StoredProcedureRepository = (DB2StoredProcedureRepository) repos.get("db2StoredProcedure");
        this.cicsdb2ConnRepository = (CICSDB2ConnRepository) repos.get("cicsdb2Conn");
        this.cicsPlexRepository = (CICSPlexRepository) repos.get("cicsPlex");
        this.relationshipRepository = (RelationshipRepository) repos.get("relationship");
        this.addressSpaceRepository = (AddressSpaceRepository) repos.get("addressSpace");
        this.bindAddressRepository = (BindAddressRepository) repos.get("bindAddress");
        this.fqdnRepository = (FqdnRepository) repos.get("fqdn");
        this.ipAddressRepository = (IpAddressRepository) repos.get("ipAddress");
        this.ipInterfaceRepository = (IpInterfaceRepository) repos.get("ipInterface");
        this.processPoolRepository = (ProcessPoolRepository) repos.get("processPool");
        this.tcpPortRepository = (TcpPortRepository) repos.get("tcpPort");
        this.udpPortRepository = (UdpPortRepository) repos.get("udpPort");
        this.idmlOperationTimeRepository = (IdmlOperationTimeRepository) repos.get("idmlOperationTime");
        this.mqSubsystemRepository = (MQSubsystemRepository) repos.get("mqSubsystem");
        this.mqAliasQueueRepository = (MQAliasQueueRepository) repos.get("mqAliasQueue");
        this.mqAuthInfoRepository = (MQAuthInfoRepository) repos.get("mqAuthInfo");
        this.mqBufferPoolRepository = (MQBufferPoolRepository) repos.get("mqBufferPool");
        this.mqClientConnectionChannelRepository = (MQClientConnectionChannelRepository) repos
                .get("mqClientConnectionChannel");
        this.mqClusterReceiverChannelRepository = (MQClusterReceiverChannelRepository) repos
                .get("mqClusterReceiverChannel");
        this.mqClusterSenderChannelRepository = (MQClusterSenderChannelRepository) repos.get("mqClusterSenderChannel");
        this.mqLocalQueueRepository = (MQLocalQueueRepository) repos.get("mqLocalQueue");
        this.mqModelQueueRepository = (MQModelQueueRepository) repos.get("mqModelQueue");
        this.mqReceiverChannelRepository = (MQReceiverChannelRepository) repos.get("mqReceiverChannel");
        this.mqRemoteQueueRepository = (MQRemoteQueueRepository) repos.get("mqRemoteQueue");
        this.mqSenderChannelRepository = (MQSenderChannelRepository) repos.get("mqSenderChannel");
        this.mqServerConnectionChannelRepository = (MQServerConnectionChannelRepository) repos
                .get("mqServerConnectionChannel");
        this.mqQueueSharingGroupRepository = (MQQueueSharingGroupRepository) repos.get("mqQueueSharingGroup");
        this.imsSubsystemRepository = (IMSSubsystemRepository) repos.get("imsSubsystem");
        this.imsDatabaseRepository = (IMSDatabaseRepository) repos.get("imsDatabase");
        this.imsProgramRepository = (IMSProgramRepository) repos.get("imsProgram");
        this.imsTransactionRepository = (IMSTransactionRepository) repos.get("imsTransaction");
        this.imsSysplexGroupRepository = (IMSSysplexGroupRepository) repos.get("imsSysplexGroup");

        // connect to the OrientDB
        String url = dbConfig.get("url");
        String dbName = dbConfig.get("dbName");
        Boolean isNew = false;
        if (dbConfig.get("isNew") != null && dbConfig.get("isNew").equals("yes")) {
            isNew = true;
        }

        OrientDB orient = null;
        String username = "";
        String password = "";
        try {
            if (url.startsWith("embedded:") || url.startsWith("plocal:")) {
                /**
                 * !IMPORTANT: required hard code to access OrientDB through local pattern
                 */
                username = "admin";
                password = "admin";
                orient = new OrientDB(url, OrientDBConfig.defaultConfig());
            } else {
                username = dbConfig.get("username");
                password = dbConfig.get("password");
                orient = new OrientDB(url, username, password, OrientDBConfig.defaultConfig());
            }

            // 1) If the DB Instance is existing, and requires re-creating it, drop it and
            // then create an new Instance.
            // 2) If the DB Instance isn't existing, create it by default.
            if (orient.exists(dbName)) {
                if (isNew) {
                    orient.drop(dbName);
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "runMain", "drop the old instance: {} by required.",
                            dbName);
                    orient.create(dbName, ODatabaseType.PLOCAL);
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "runMain", "create new instance: {} by required.",
                            dbName);
                }
            } else {
                orient.create(dbName, ODatabaseType.PLOCAL);
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "runMain", "create instance: {} by default.", dbName);
            }
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "runMain", e.toString());
            String errCode = ErrorCode.DBConnectError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Orient", url);
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "runMain");
        }

        ODatabaseSession db = null;
        Integer errCount = 0;
        try {
            db = orient.open(dbName, username, password);
            this.orientTransformation = new OrientTransformation(db);
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "runMain", "create or check update for the schema.");
            this.orientTransformation.createOrUpdateDLASchema();

            transformZSeriesComputer();
            transformLpar();
            transformSysplex();
            transformZOS();
            transformZSubSystem();
            transformCICSRegion();
            transformCICSSit();
            transformCICSSitOverride();
            transformCICSProgram();
            transformCICSTransaction();
            transformCICSFile();
            transformDb2DataSharingGroup();
            transformDb2Subsystem();
            transformDb2Database();
            transformDb2TableSpace();
            transformDb2BufferPool();
            transformDb2StoredProcedure();

            transformDb2Table();
            transformCICSDB2Conn();
            transformCICSPlex();

            // don't parse the AddressSpace to clear cycle
            // transformAddressSpace();
            transformBindAddress();
            transformFqdn();
            transformIpAddress();
            transformIpInterface();
            transformProcessPool();
            transformTcpPort();
            transformUdpPort();

            transformMQSubsystem();
            transformMQAliasQueue();
            transformMQAuthInfo();
            transformMQBufferPool();
            transformMQClientConnectionChannel();
            transformMQClusterReceiverChannel();
            transformMQClusterSenderChannel();
            transformMQLocalQueue();
            transformMQModelQueue();
            transformMQRemoteQueue();
            transformMQReceiverChannel();
            transformMQSenderChannel();
            transformMQServerConnectionChannel();
            transformMQQueueSharingGroup();

            transformIMSSubsystem();
            transformIMSDatabase();
            transformIMSProgram();
            transformIMSTransaction();
            transformIMSSysplexGroup();

            transformIdmlOperationTime();

            db.commit();
            db.close();

            errCount = transformRelationships(orient, dbName, username, password);
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "runMain", e.toString());
            String errCode = ErrorCode.PluginError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoOrient", "");
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "runMain");
        }

        try {
            orient.close();
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "runMain", e.toString());
            String errCode = ErrorCode.DBCloseError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Orient");
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "runMain");
        }

        String resMsg = "CopyIntoOrient pipeline finished.";
        if (errCount > 0) {
            String errStr = String.format(
                    "But transformRelationships has %d abnormal records, please go to check the log.", errCount);
            resMsg += errStr;
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "runMain", resMsg);
        return resMsg;
    }

    public String run(PipelineConf pipelineConf) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", "Running CopyIntoOrient");
        // get the Config
        String persistID = pipelineConf.getPersist().get(0).toString();
        ConfigManager configManager = ConfigManager.getConfigManager();
        JsonObject jsonObject = configManager.searchConfig("persist", persistID);

        if (jsonObject == null) {
            String errCode = ErrorCode.PluginSearchConfigNull.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "CopyIntoOrient");
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", "Error: {}", msg);
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "run");
        }

        String url = jsonObject.get("url").getAsString();
        String[] urlList = url.split("/");
        String dbName = urlList[urlList.length - 1];
        url = url.substring(0, url.lastIndexOf("/"));
        String username = jsonObject.get("user").getAsString();
        String password = jsonObject.get("password").getAsString();
        String isNew = "";
        if (pipelineConf.getParameter() != null) {
            isNew = pipelineConf.getParameter();
        }

        HashMap<String, String> dbConfig = new HashMap();
        dbConfig.put("url", url);
        dbConfig.put("username", username);
        dbConfig.put("password", password);
        dbConfig.put("dbName", dbName);
        dbConfig.put("isNew", isNew);

        return runMain(dbConfig);
    }
}
