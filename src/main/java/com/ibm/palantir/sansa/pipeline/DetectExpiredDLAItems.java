/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.pipeline;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IdmlOperationTime;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Relationship;
import com.ibm.palantir.catelyn.jpa.repository.dla.AddressSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.BindAddressRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CFComputerSystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CFLPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSFileRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSRegionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableOverrideRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2BufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DataSharingGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2StoredProcedureRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2SubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.FqdnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSDatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSSysplexGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IdmlOperationTimeRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IpAddressRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IpInterfaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.LPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAliasQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAuthInfoRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQBufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClientConnectionChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClusterReceiverChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClusterSenderChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQLocalQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQModelQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQQueueSharingGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQReceiverChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQRemoteQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSenderChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQServerConnectionChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.PRSMLPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ProcessPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipServiceNowRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.SysplexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.TcpPortRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.UdpPortRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZOSRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSeriesComputerRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSubSystemRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.sansa.utils.TimestampConverter;

// This pipeline is used to detect those retired CI items
@Component
public class DetectExpiredDLAItems extends AbstractPipeline {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = DetectExpiredDLAItems.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private final TimestampConverter timestampConverter = new TimestampConverter();

    private ZSeriesComputerRepository zSeriesComputerRepository;
    private CFComputerSystemRepository cfComputerSystemRepository;
    private LPARRepository lparRepository;
    private CFLPARRepository cfLparRepository;
    private PRSMLPARRepository prsmlparRepository;
    private ZOSRepository zOSRepository;
    private SysplexRepository sysplexRepository;
    private ZSubSystemRepository zSubSystemRepository;
    private CICSRegionRepository cicsRegionRepository;
    private CICSSystemInitTableRepository cicsSystemInitTableRepository;
    private CICSSystemInitTableOverrideRepository cicsSystemInitTableOverrideRepository;
    private CICSProgramRepository cicsProgramRepository;
    private CICSTransactionRepository cicsTransactionRepository;
    private CICSFileRepository cicsFileRepository;
    private DB2DataSharingGroupRepository db2dataSharingGroupRepository;
    private DB2SubsystemRepository db2SubsystemRepository;
    private DB2DatabaseRepository db2DatabaseRepository;
    private DB2TableSpaceRepository db2TableSpaceRepository;
    private DB2BufferPoolRepository db2BufferPoolRepository;
    private DB2StoredProcedureRepository db2StoredProcedureRepository;
    private RelationshipRepository relationshipRepository;
    private AddressSpaceRepository addressSpaceRepository;
    private BindAddressRepository bindAddressRepository;
    private FqdnRepository fqdnRepository;
    private IpAddressRepository ipAddressRepository;
    private IpInterfaceRepository ipInterfaceRepository;
    private ProcessPoolRepository processPoolRepository;
    private TcpPortRepository tcpPortRepository;
    private UdpPortRepository udpPortRepository;
    private IdmlOperationTimeRepository idmlOperationTimeRepository;
    private MQAliasQueueRepository mqAliasQueueRepository;
    private MQAuthInfoRepository mqAuthInfoRepository;
    private MQBufferPoolRepository mqBufferPoolRepository;
    private MQClientConnectionChannelRepository mqClientConnectionChannelRepository;
    private MQClusterReceiverChannelRepository mqClusterReceiverChannelRepository;
    private MQClusterSenderChannelRepository mqClusterSenderChannelRepository;
    private MQLocalQueueRepository mqLocalQueueRepository;
    private MQModelQueueRepository mqModelQueueRepository;
    private MQReceiverChannelRepository mqReceiverChannelRepository;
    private MQRemoteQueueRepository mqRemoteQueueRepository;
    private MQSenderChannelRepository mqSenderChannelRepository;
    private MQServerConnectionChannelRepository mqServerConnectionChannelRepository;
    private MQSubsystemRepository mqSubsystemRepository;
    private IMSSubsystemRepository imsSubsystemRepository;
    private IMSDatabaseRepository imsDatabaseRepository;
    private IMSProgramRepository imsProgramRepository;
    private IMSTransactionRepository imsTransactionRepository;
    private IMSSysplexGroupRepository imsSysplexGroupRepository;
    private MQQueueSharingGroupRepository mqQueueSharingGroupRepository;
    private RelationshipServiceNowRepository relationshipServiceNowRepository;

    private Integer ciCount = 0; // count the deleted CI items
    private Integer rlCount = 0; // count the deleted relationship records
    private Integer rlsnCount = 0; // count the deleted relationshipServiceNow records
    private final List<String> deletedRLIds = new ArrayList<>();
    private final List<String> deletedRLSNIds = new ArrayList<>();

    @Override
    public String run(PipelineConf pipelineConf) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", "Running DetectExpiredDLAItems");
        String parameter = pipelineConf.getParameter();

        String paramFormatErrorCode = ErrorCode.PluginParamFormatError.getCodeStr();
        String paramFormatErrorMsg = MessageFormat.format(MsgTemp.get(paramFormatErrorCode), "Sansa",
                "DetectExpiredDLAItems", parameter);
        String paramNullParamErrorCode = ErrorCode.PluginNullParamError.getCodeStr();
        String paramNullParamErrorMsg = MessageFormat.format(MsgTemp.get(paramNullParamErrorCode), "Sansa",
                "DetectExpiredDLAItems");

        if (parameter == null) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", "Error: {}", paramNullParamErrorMsg);
            throw new ServiceException(paramNullParamErrorCode, paramNullParamErrorMsg, REPONAME, CLASSNAME, "run");
        }

        Integer interval;
        JsonElement jsonElement = JsonParser.parseString(parameter);
        if (jsonElement.isJsonObject()) {
            JsonObject jsonObject = jsonElement.getAsJsonObject();

            if (!jsonObject.has("interval")) {
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run",
                        "There is no interval key in the parameter json object");
                throw new ServiceException(paramNullParamErrorCode, paramNullParamErrorMsg, REPONAME, CLASSNAME, "run");
            }
            // get the parameter interval, whose Unit is days
            String intervalStr = jsonObject.get("interval").getAsString();
            if (!intervalStr.matches("\\d+")) {
                String invalidParamErrorCode = ErrorCode.PluginInvalidParamError.getCodeStr();
                String invalidParamErrorMsg = MessageFormat.format(MsgTemp.get(invalidParamErrorCode), "Sansa",
                        intervalStr, "DetectExpiredDLAItems");
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", "Get invalid parameter interval: {}", intervalStr);
                throw new ServiceException(invalidParamErrorCode, invalidParamErrorMsg, REPONAME, CLASSNAME, "run");
            } else {
                interval = Integer.valueOf(intervalStr);
            }
        } else {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", "The parameter format is invalid!");
            throw new ServiceException(paramFormatErrorCode, paramFormatErrorMsg, REPONAME, CLASSNAME, "run");
        }

        try {
            // initial all repositories
            pipelineInit();

            // get all IdmlOperationTime and then compute the overdue time for each
            List<IdmlOperationTime> iots = this.idmlOperationTimeRepository.findAll();

            for (IdmlOperationTime iot : iots) {
                String cpcPrefixId = iot.getPrefixId();
                String batchScanDateStr = iot.getCreateTimestamp();
                LocalDateTime retiredDateTime = getRetiredDateTime(batchScanDateStr, interval);
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run",
                        String.format("The expired time for cpcPrefixId %s is %s.", cpcPrefixId, retiredDateTime));
                // prefixId = cpcPrefixId
                detectZSeriesComputer(retiredDateTime, cpcPrefixId);
                // detectCFComputerSystem(retiredDateTime, cpcPrefixId);
                detectLPAR(retiredDateTime, cpcPrefixId);
                // detectCFLPAR(retiredDateTime, cpcPrefixId);
                // detectPRSMLPAR(retiredDateTime, cpcPrefixId);

                // Stage processing Relationship and RelationshipServiceNow

                deletedRLIds.clear();
                deletedRLSNIds.clear();

                // prefixId = zosPrefixId
                List<Relationship> zosRunsOnLPARList = relationshipRepository
                        .findZOSRunsOnLPARByTargetPrefix(cpcPrefixId);
                for (Relationship zrol : zosRunsOnLPARList) {
                    String zosPrefixId = zrol.getSourcePrefix();
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run",
                            String.format("Start to check those items with zosPrefixId %s by the expired time %s.",
                                    zosPrefixId, retiredDateTime));

                    detectZOS(retiredDateTime, zosPrefixId);
                    // NetInfo
                    // detectAddressSpace(retiredDateTime, zosPrefixId);
                    // detectBindAddress(retiredDateTime, zosPrefixId);
                    // detectFqdn(retiredDateTime, zosPrefixId);
                    // detectIpAddress(retiredDateTime, zosPrefixId);
                    // detectIpInterface(retiredDateTime, zosPrefixId);
                    // detectProcessPool(retiredDateTime, zosPrefixId);
                    // detectTcpPort(retiredDateTime, zosPrefixId);
                    // detectUdpPort(retiredDateTime, zosPrefixId);

                    // detectZSubsystem(retiredDateTime, zosPrefixId);
                    // CICS
                    detectCICSRegion(retiredDateTime, zosPrefixId);
                    // detectCICSSitOverride(retiredDateTime, zosPrefixId);
                    // detectCICSSit(retiredDateTime, zosPrefixId);
                    // detectCICSProgram(retiredDateTime, zosPrefixId);
                    detectCICSTransaction(retiredDateTime, zosPrefixId);
                    // detectCICSFile(retiredDateTime, zosPrefixId);
                    // DB2
                    detectDB2Subsystem(retiredDateTime, zosPrefixId);
                    detectDB2Database(retiredDateTime, zosPrefixId);
                    detectDB2StoredProcedure(retiredDateTime, zosPrefixId);
                    // detectDB2TableSpace(retiredDateTime, zosPrefixId);
                    // detectDB2BufferPool(retiredDateTime, zosPrefixId);

                    // MQ
                    detectMQSubsystem(retiredDateTime, zosPrefixId);
                    detectMQAliasQueue(retiredDateTime, zosPrefixId);
                    // detectMQAuthInfo(retiredDateTime, zosPrefixId);
                    // detectMQBufferPool(retiredDateTime, zosPrefixId);
                    // detectMQClientConnectionChannel(retiredDateTime, zosPrefixId);
                    // detectMQClusterReceiverChannel(retiredDateTime, zosPrefixId);
                    // detectMQClusterSenderChannel(retiredDateTime, zosPrefixId);
                    detectMQLocalQueue(retiredDateTime, zosPrefixId);
                    detectMQModelQueue(retiredDateTime, zosPrefixId);
                    detectMQRemoteQueue(retiredDateTime, zosPrefixId);
                    // detectMQReceiverChannel(retiredDateTime, zosPrefixId);
                    // detectMQSenderChannel(retiredDateTime, zosPrefixId);
                    // detectMQServerConnectionChannel(retiredDateTime, zosPrefixId);
                    // IMS
                    detectIMSSubsystem(retiredDateTime, zosPrefixId);
                    detectIMSDatabase(retiredDateTime, zosPrefixId);
                    detectIMSTransaction(retiredDateTime, zosPrefixId);
                    // detectIMSProgram(retiredDateTime, zosPrefixId);
                    // Processing Relationship and RelationshipServiceNow
                    // detectExpiredRelationships(retiredDateTime, cpcPrefixId);
                    detectExpiredServiceNowRelationships(retiredDateTime, cpcPrefixId);

                    deletedRLIds.clear();
                    deletedRLSNIds.clear();

                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run",
                            String.format("Finish checking those items with zosPrefixId %s by the expired time %s.",
                                    zosPrefixId, retiredDateTime));
                }
                // prefixId = ""
                // detectSysplex(retiredDateTime);

                // DSGs
                // detectDB2DataSharingGroup(retiredDateTime);
                // detectMQQueueSharingGroup(retiredDateTime);
                // detectIMSSysplexGroup(retiredDateTime);
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", String.format(
                        "Finish checking those items under the CPC whose PrefixId is %s by the expired time %s.",
                        cpcPrefixId, retiredDateTime));
            }

        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", e.toString());
            String errCode = ErrorCode.PluginError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "DetectExpiredDLAItems",
                    parameter);
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "run");
        }

        String resMsg = String.format(
                "Success to detect %d expired DLA items with %d Relationship records.",
                ciCount, rlsnCount);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", resMsg);
        return resMsg;
    }

    /**
     * Determines if an item should be considered retired based on its timestamp and
     * a retirement date threshold.
     *
     * @param id              The unique identifier of the item to check
     * @param timeStr         The timestamp of the item in ISO date-time format
     * @param retiredDateTime The threshold date-time; items with timestamps before
     *                        this are considered retired
     * @return true if the item is considered retired (its timestamp is before the
     *         retirement threshold), false otherwise
     */

    private void pipelineInit() {
        HashMap<String, Object> repos = JPAManager.getJPAManager().getRepos();

        this.zSeriesComputerRepository = (ZSeriesComputerRepository) repos.get("zSeriesComputer");
        this.cfComputerSystemRepository = (CFComputerSystemRepository) repos.get("cfComputerSystem");
        this.lparRepository = (LPARRepository) repos.get("lpar");
        this.cfLparRepository = (CFLPARRepository) repos.get("cfLpar");
        this.prsmlparRepository = (PRSMLPARRepository) repos.get("prsmLpar");
        this.zOSRepository = (ZOSRepository) repos.get("zos");
        this.sysplexRepository = (SysplexRepository) repos.get("sysplex");
        this.zSubSystemRepository = (ZSubSystemRepository) repos.get("zSubSystem");
        this.cicsRegionRepository = (CICSRegionRepository) repos.get("cicsRegion");
        this.cicsSystemInitTableRepository = (CICSSystemInitTableRepository) repos.get("cicsSystemInitTable");
        this.cicsSystemInitTableOverrideRepository = (CICSSystemInitTableOverrideRepository) repos
                .get("cicsSystemInitTableOverride");
        this.cicsProgramRepository = (CICSProgramRepository) repos.get("cicsProgram");
        this.cicsTransactionRepository = (CICSTransactionRepository) repos.get("cicsTransaction");
        this.cicsFileRepository = (CICSFileRepository) repos.get("cicsFile");
        this.db2dataSharingGroupRepository = (DB2DataSharingGroupRepository) repos.get("db2dataSharingGroup");
        this.db2SubsystemRepository = (DB2SubsystemRepository) repos.get("db2Subsystem");
        this.db2DatabaseRepository = (DB2DatabaseRepository) repos.get("db2Database");
        this.db2TableSpaceRepository = (DB2TableSpaceRepository) repos.get("db2TableSpace");
        this.db2BufferPoolRepository = (DB2BufferPoolRepository) repos.get("db2BufferPool");
        this.db2StoredProcedureRepository = (DB2StoredProcedureRepository) repos.get("db2StoredProcedure");
        this.relationshipRepository = (RelationshipRepository) repos.get("relationship");
        this.addressSpaceRepository = (AddressSpaceRepository) repos.get("addressSpace");
        this.bindAddressRepository = (BindAddressRepository) repos.get("bindAddress");
        this.fqdnRepository = (FqdnRepository) repos.get("fqdn");
        this.ipAddressRepository = (IpAddressRepository) repos.get("ipAddress");
        this.ipInterfaceRepository = (IpInterfaceRepository) repos.get("ipInterface");
        this.processPoolRepository = (ProcessPoolRepository) repos.get("processPool");
        this.tcpPortRepository = (TcpPortRepository) repos.get("tcpPort");
        this.udpPortRepository = (UdpPortRepository) repos.get("udpPort");
        this.idmlOperationTimeRepository = (IdmlOperationTimeRepository) repos.get("idmlOperationTime");
        this.mqAliasQueueRepository = (MQAliasQueueRepository) repos.get("mqAliasQueue");
        this.mqAuthInfoRepository = (MQAuthInfoRepository) repos.get("mqAuthInfo");
        this.mqBufferPoolRepository = (MQBufferPoolRepository) repos.get("mqBufferPool");
        this.mqClientConnectionChannelRepository = (MQClientConnectionChannelRepository) repos
                .get("mqClientConnectionChannel");
        this.mqClusterReceiverChannelRepository = (MQClusterReceiverChannelRepository) repos
                .get("mqClusterReceiverChannel");
        this.mqClusterSenderChannelRepository = (MQClusterSenderChannelRepository) repos.get("mqClusterSenderChannel");
        this.mqLocalQueueRepository = (MQLocalQueueRepository) repos.get("mqLocalQueue");
        this.mqModelQueueRepository = (MQModelQueueRepository) repos.get("mqModelQueue");
        this.mqReceiverChannelRepository = (MQReceiverChannelRepository) repos.get("mqReceiverChannel");
        this.mqRemoteQueueRepository = (MQRemoteQueueRepository) repos.get("mqRemoteQueue");
        this.mqSenderChannelRepository = (MQSenderChannelRepository) repos.get("mqSenderChannel");
        this.mqServerConnectionChannelRepository = (MQServerConnectionChannelRepository) repos
                .get("mqServerConnectionChannel");
        this.mqSubsystemRepository = (MQSubsystemRepository) repos.get("mqSubsystem");
        this.imsSubsystemRepository = (IMSSubsystemRepository) repos.get("imsSubsystem");
        this.imsDatabaseRepository = (IMSDatabaseRepository) repos.get("imsDatabase");
        this.imsProgramRepository = (IMSProgramRepository) repos.get("imsProgram");
        this.imsTransactionRepository = (IMSTransactionRepository) repos.get("imsTransaction");
        this.imsSysplexGroupRepository = (IMSSysplexGroupRepository) repos.get("imsSysplexGroup");
        this.mqQueueSharingGroupRepository = (MQQueueSharingGroupRepository) repos.get("mqQueueSharingGroup");
        this.relationshipServiceNowRepository = (RelationshipServiceNowRepository) repos.get("relationshipServiceNow");
    }

    // *****************************************************
    // Detect expired relationships
    // *****************************************************

    // @Transactional
    // private void detectExpiredRelationships(LocalDateTime retiredDateTime, String
    // cpcPrefixId) {
    // try {
    // // Update soft_del flag for all expired relationships
    // int expiredRelationshipCount =
    // relationshipRepository.markExpiredRelationshipItems(retiredDateTime,
    // cpcPrefixId);

    // rlCount += expiredRelationshipCount;
    // LOG.info(String.format("Detect that %d Relationship items have expired.",
    // expiredRelationshipCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired relationships", e);
    // throw e;
    // }
    // }

    @Transactional
    private void detectExpiredServiceNowRelationships(LocalDateTime retiredDateTime, String cpcPrefixId) {
        try {
            // Update soft_del flag for all expired relationships
            int expiredRelationshipServiceNowCount = relationshipServiceNowRepository
                    .markExpiredRelationshipServiceNowItems(retiredDateTime, cpcPrefixId);

            rlsnCount += expiredRelationshipServiceNowCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectExpiredServiceNowRelationships",
                    String.format("Detect that %d RelationshipServiceNow items have expired.",
                            expiredRelationshipServiceNowCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectExpiredServiceNowRelationships",
                    "Error while detecting expired ServiceNow relationships {}", e.toString());
            throw e;
        }
    }

    // ***********************************************************
    // Detection based on the cpcPrefixId and retiredDateTime
    // ***********************************************************
    private void detectZSeriesComputer(LocalDateTime retiredDateTime, String cpcPrefixId) {
        try {
            int expiredZSeriesComputerCount = zSeriesComputerRepository.markExpiredZSeriesComputerItems(retiredDateTime,
                    cpcPrefixId);
            ciCount += expiredZSeriesComputerCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectZSeriesComputer",
                    String.format("Detect that %d ZSeriesComputer items have expired.", expiredZSeriesComputerCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectZSeriesComputer",
                    "Error while detecting expired ZSeriesComputer items {}", e.toString());
            throw e;
        }
    }

    // private void detectCFComputerSystem(LocalDateTime retiredDateTime, String
    // cpcPrefixId) {
    // try {
    // int expiredCFComputerSystemCount =
    // cfComputerSystemRepository.markExpiredCFComputerSystemItems(
    // retiredDateTime,
    // cpcPrefixId);
    // ciCount += expiredCFComputerSystemCount;
    // LOG.info(
    // String.format("Detect that %d CFComputerSystem items have expired.",
    // expiredCFComputerSystemCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired CFComputerSystem items", e);
    // throw e;
    // }
    // }

    private void detectLPAR(LocalDateTime retiredDateTime, String cpcPrefixId) {
        try {
            int expiredLPARCount = lparRepository.markExpiredLPARItems(retiredDateTime, cpcPrefixId);
            ciCount += expiredLPARCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectLPAR",
                    String.format("Detect that %d LPAR items have expired.", expiredLPARCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectLPAR", "Error while detecting expired LPAR items {}",
                    e.toString());
            throw e;
        }
    }

    // private void detectCFLPAR(LocalDateTime retiredDateTime, String cpcPrefixId)
    // {
    // try {
    // int expiredCFLPARCount =
    // cfLparRepository.markExpiredCFLPARItems(retiredDateTime, cpcPrefixId);
    // ciCount += expiredCFLPARCount;
    // LOG.info(String.format("Detect that %d CFLPAR items have expired.",
    // expiredCFLPARCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired CFLPAR items", e);
    // throw e;
    // }
    // }

    // private void detectPRSMLPAR(LocalDateTime retiredDateTime, String
    // cpcPrefixId) {
    // try {
    // int expiredPRSMLPARCount =
    // prsmlparRepository.markExpiredPRSMLPARItems(retiredDateTime, cpcPrefixId);
    // ciCount += expiredPRSMLPARCount;
    // LOG.info(String.format("Detect that %d PRSMLPAR items have expired.",
    // expiredPRSMLPARCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired PRSMLPAR items", e);
    // throw e;
    // }
    // }

    // ***********************************************************
    // Detection based solely on the retiredDateTime
    // ***********************************************************
    // private void detectSysplex(LocalDateTime retiredDateTime) {
    // try {
    // int expiredSysplexCount =
    // sysplexRepository.markExpiredSysplexItems(retiredDateTime);
    // ciCount += expiredSysplexCount;
    // LOG.info(String.format("Detect that %d Sysplex items have expired.",
    // expiredSysplexCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired Sysplex items", e);
    // throw e;
    // }
    // }

    // ***********************************************************
    // Detection for DSGs
    // ***********************************************************

    // private void detectDB2DataSharingGroup(LocalDateTime retiredDateTime) {
    // try {
    // int expiredDB2DataSharingGroupCount =
    // db2dataSharingGroupRepository.markExpiredDB2DataSharingGroupItems(
    // retiredDateTime);
    // ciCount += expiredDB2DataSharingGroupCount;
    // LOG.info(String.format("Detect that %d DB2DataSharingGroup items have
    // expired.",
    // expiredDB2DataSharingGroupCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired DB2DataSharingGroup items", e);
    // throw e;
    // }

    // }

    // private void detectMQQueueSharingGroup(LocalDateTime retiredDateTime) {
    // try {
    // int expiredMQQueueSharingGroupCount =
    // mqQueueSharingGroupRepository.markExpiredMQQueueSharingGroupItems(
    // retiredDateTime);
    // ciCount += expiredMQQueueSharingGroupCount;
    // LOG.info(String.format("Detect that %d MQQueueSharingGroup items have
    // expired.",
    // expiredMQQueueSharingGroupCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired MQQueueSharingGroup items", e);
    // throw e;
    // }
    // }

    // private void detectIMSSysplexGroup(LocalDateTime retiredDateTime) {
    // try {
    // int expiredIMSSysplexGroupCount =
    // imsSysplexGroupRepository.markExpiredSysplexItems(retiredDateTime);
    // ciCount += expiredIMSSysplexGroupCount;
    // LOG.info(String.format("Detect that %d IMSSysplexGroup items have expired.",
    // expiredIMSSysplexGroupCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired IMSSysplexGroup items", e);
    // throw e;
    // }
    // }

    // ***********************************************************
    // Detection based on zosPrefixId and retiredDateTime
    // ***********************************************************

    private void detectZOS(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredZOSCount = zOSRepository.markExpiredZOSItems(retiredDateTime, zosPrefixId);
            ciCount += expiredZOSCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectZOS",
                    String.format("Detect that %d ZOS items have expired.", expiredZOSCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectZOS", "Error while detecting expired ZOS items {}",
                    e.toString());
            throw e;
        }
    }

    // private void detectAddressSpace(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredAddressSpaceCount =
    // addressSpaceRepository.markExpiredAddressSpaceItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredAddressSpaceCount;
    // LOG.info(String.format("Detect that %d AddressSpace items have expired.",
    // expiredAddressSpaceCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired AddressSpace items", e);
    // throw e;
    // }
    // }

    // private void detectBindAddress(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredBindAddressCount =
    // bindAddressRepository.markExpiredBindAddressItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredBindAddressCount;
    // LOG.info(String.format("Detect that %d BindAddress items have expired.",
    // expiredBindAddressCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired BindAddress items", e);
    // throw e;
    // }
    // }

    // private void detectFqdn(LocalDateTime retiredDateTime, String zosPrefixId) {
    // try {
    // int expiredFqdnCount = fqdnRepository.markExpiredFqdnItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredFqdnCount;
    // LOG.info(String.format("Detect that %d Fqdn items have expired.",
    // expiredFqdnCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired Fqdn items", e);
    // throw e;
    // }
    // }

    // private void detectIpAddress(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredIpAddressCount =
    // ipAddressRepository.markExpiredIpAddressItems(retiredDateTime, zosPrefixId);
    // ciCount += expiredIpAddressCount;
    // LOG.info(String.format("Detect that %d IpAddress items have expired.",
    // expiredIpAddressCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired IpAddress items", e);
    // throw e;
    // }
    // }

    // private void detectIpInterface(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredIpInterfaceCount =
    // ipInterfaceRepository.markExpiredIpInterfaceItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredIpInterfaceCount;
    // LOG.info(String.format("Detect that %d IpInterface items have expired.",
    // expiredIpInterfaceCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired IpInterface items", e);
    // throw e;
    // }
    // }

    // private void detectProcessPool(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredProcessPoolCount =
    // processPoolRepository.markExpiredProcessPoolItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredProcessPoolCount;
    // LOG.info(String.format("Detect that %d ProcessPool items have expired.",
    // expiredProcessPoolCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired ProcessPool items", e);
    // throw e;
    // }
    // }

    // private void detectTcpPort(LocalDateTime retiredDateTime, String zosPrefixId)
    // {
    // try {
    // int expiredTcpPortCount =
    // tcpPortRepository.markExpiredTcpPortItems(retiredDateTime, zosPrefixId);
    // ciCount += expiredTcpPortCount;
    // LOG.info(String.format("Detect that %d TcpPort items have expired.",
    // expiredTcpPortCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired TcpPort items", e);
    // throw e;
    // }
    // }

    // private void detectUdpPort(LocalDateTime retiredDateTime, String zosPrefixId)
    // {
    // try {
    // int expiredUdpPortCount =
    // udpPortRepository.markExpiredUdpPortItems(retiredDateTime, zosPrefixId);
    // ciCount += expiredUdpPortCount;
    // LOG.info(String.format("Detect that %d UdpPort items have expired.",
    // expiredUdpPortCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired UdpPort items", e);
    // throw e;
    // }
    // }

    // private void detectZSubsystem(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredZSubSystemCount =
    // zSubSystemRepository.markExpiredZSubSystemItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredZSubSystemCount;
    // LOG.info(String.format("Detect that %d ZSubSystem items have expired.",
    // expiredZSubSystemCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired ZSubSystem items", e);
    // throw e;
    // }
    // }

    private void detectCICSRegion(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredCICSRegionCount = cicsRegionRepository.markExpiredCICSRegionItems(retiredDateTime, zosPrefixId);
            ciCount += expiredCICSRegionCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectCICSRegion",
                    String.format("Detect that %d CICSRegion items have expired.", expiredCICSRegionCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectCICSRegion",
                    "Error while detecting expired CICSRegion items {}", e.toString());
            throw e;
        }
    }

    // private void detectCICSSit(LocalDateTime retiredDateTime, String zosPrefixId)
    // {
    // try {
    // int expiredCICSSystemInitTableCount =
    // cicsSystemInitTableRepository.markExpiredCICSSystemInitTableItems(
    // retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredCICSSystemInitTableCount;
    // LOG.info(String.format("Detect that %d CICSSystemInitTable items have
    // expired.",
    // expiredCICSSystemInitTableCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired CICSSystemInitTable items", e);
    // throw e;
    // }
    // }

    // private void detectCICSSitOverride(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredCICSSystemInitTableOverrideCount =
    // cicsSystemInitTableOverrideRepository
    // .markExpiredCICSSystemInitTableOverrideItems(
    // retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredCICSSystemInitTableOverrideCount;
    // LOG.info(String.format("Detect that %d CICSSystemInitTableOverride items have
    // expired.",
    // expiredCICSSystemInitTableOverrideCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired CICSSystemInitTableOverride items",
    // e);
    // throw e;
    // }
    // }

    // private void detectCICSProgram(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredCICSProgramCount =
    // cicsProgramRepository.markExpiredCICSProgramItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredCICSProgramCount;
    // LOG.info(String.format("Detect that %d CICSProgram items have expired.",
    // expiredCICSProgramCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired CICSProgram items", e);
    // throw e;
    // }
    // }

    private void detectCICSTransaction(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredCICSTransactionCount = cicsTransactionRepository.markExpiredCICSTransactionItems(retiredDateTime,
                    zosPrefixId);
            ciCount += expiredCICSTransactionCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectCICSTransaction",
                    String.format("Detect that %d CICSTransaction items have expired.", expiredCICSTransactionCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectCICSTransaction",
                    "Error while detecting expired CICSTransaction items {}", e.toString());
            throw e;
        }
    }

    // private void detectCICSFile(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredCICSFileCount =
    // cicsFileRepository.markExpiredCICSFileItems(retiredDateTime, zosPrefixId);
    // ciCount += expiredCICSFileCount;
    // LOG.info(String.format("Detect that %d CICSFile items have expired.",
    // expiredCICSFileCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired CICSFile items", e);
    // throw e;
    // }
    // }

    private void detectDB2Subsystem(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredDB2SubsystemCount = db2SubsystemRepository.markExpiredDB2SubsystemItems(retiredDateTime,
                    zosPrefixId);
            ciCount += expiredDB2SubsystemCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectDB2Subsystem",
                    String.format("Detect that %d DB2Subsystem items have expired.", expiredDB2SubsystemCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectDB2Subsystem",
                    "Error while detecting expired DB2Subsystem items {}", e.toString());
            throw e;
        }
    }

    private void detectDB2Database(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredDB2DatabaseCount = db2DatabaseRepository.markExpiredDB2DatabaseItems(retiredDateTime,
                    zosPrefixId);
            ciCount += expiredDB2DatabaseCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectDB2Database",
                    String.format("Detect that %d DB2Database items have expired.", expiredDB2DatabaseCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectDB2Database",
                    "Error while detecting expired DB2Database items {}", e.toString());
            throw e;
        }
    }

    // private void detectDB2TableSpace(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredDB2TableSpaceCount =
    // db2TableSpaceRepository.markExpiredDB2TableSpaceItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredDB2TableSpaceCount;
    // LOG.info(String.format("Detect that %d DB2TableSpace items have expired.",
    // expiredDB2TableSpaceCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired DB2TableSpace items", e);
    // throw e;
    // }
    // }

    // private void detectDB2BufferPool(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredDB2BufferPoolCount =
    // db2BufferPoolRepository.markExpiredDB2BufferPoolItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredDB2BufferPoolCount;
    // LOG.info(String.format("Detect that %d DB2BufferPool items have expired.",
    // expiredDB2BufferPoolCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired DB2BufferPool items", e);
    // throw e;
    // }
    // }

    private void detectDB2StoredProcedure(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredDB2StoredProcedureCount = db2StoredProcedureRepository.markExpiredDB2StoredProcedureItems(
                    retiredDateTime,
                    zosPrefixId);
            ciCount += expiredDB2StoredProcedureCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectDB2StoredProcedure", String
                    .format("Detect that %d DB2StoredProcedure items have expired.", expiredDB2StoredProcedureCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectDB2StoredProcedure",
                    "Error while detecting expired DB2StoredProcedure items {}", e.toString());
            throw e;
        }
    }

    private void detectMQSubsystem(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredMQSubsystemCount = mqSubsystemRepository.markExpiredMQSubsystemItems(retiredDateTime,
                    zosPrefixId);
            ciCount += expiredMQSubsystemCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectMQSubsystem",
                    String.format("Detect that %d MQSubsystem items have expired.", expiredMQSubsystemCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectMQSubsystem",
                    "Error while detecting expired MQSubsystem items {}", e.toString());
            throw e;
        }
    }

    private void detectMQAliasQueue(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredMQAliasQueueCount = mqAliasQueueRepository.markExpiredMQAliasQueueItems(retiredDateTime,
                    zosPrefixId);
            ciCount += expiredMQAliasQueueCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectMQAliasQueue",
                    String.format("Detect that %d MQAliasQueue items have expired.", expiredMQAliasQueueCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectMQAliasQueue",
                    "Error while detecting expired MQAliasQueue items {}", e.toString());
            throw e;
        }
    }

    // private void detectMQAuthInfo(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredMQAuthInfoCount =
    // mqAuthInfoRepository.markExpiredMQAuthInfoItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredMQAuthInfoCount;
    // LOG.info(String.format("Detect that %d MQAuthInfo items have expired.",
    // expiredMQAuthInfoCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired MQAuthInfo items", e);
    // throw e;
    // }
    // }

    // private void detectMQBufferPool(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredMQBufferPoolCount =
    // mqBufferPoolRepository.markExpiredMQBufferPoolItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredMQBufferPoolCount;
    // LOG.info(String.format("Detect that %d MQBufferPool items have expired.",
    // expiredMQBufferPoolCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired MQBufferPool items", e);
    // throw e;
    // }
    // }

    // private void detectMQClientConnectionChannel(LocalDateTime retiredDateTime,
    // String zosPrefixId) {
    // try {
    // int expiredMQClientConnectionChannelCount =
    // mqClientConnectionChannelRepository
    // .markExpiredMQClientConnectionChannelItems(retiredDateTime, zosPrefixId);
    // ciCount += expiredMQClientConnectionChannelCount;
    // LOG.info(String.format("Detect that %d MQClientConnectionChannel items have
    // expired.",
    // expiredMQClientConnectionChannelCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired MQClientConnectionChannel items",
    // e);
    // throw e;
    // }
    // }

    // private void detectMQClusterReceiverChannel(LocalDateTime retiredDateTime,
    // String zosPrefixId) {
    // try {
    // int expiredMQClusterReceiverChannelCount = mqClusterReceiverChannelRepository
    // .markExpiredMQClusterReceiverChannelItems(retiredDateTime, zosPrefixId);
    // ciCount += expiredMQClusterReceiverChannelCount;
    // LOG.info(String.format("Detect that %d MQClusterReceiverChannel items have
    // expired.",
    // expiredMQClusterReceiverChannelCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired MQClusterReceiverChannel items", e);
    // throw e;
    // }
    // }

    // private void detectMQClusterSenderChannel(LocalDateTime retiredDateTime,
    // String zosPrefixId) {
    // try {
    // int expiredMQClusterSenderChannelCount = mqClusterSenderChannelRepository
    // .markExpiredMQClusterSenderChannelItems(retiredDateTime, zosPrefixId);
    // ciCount += expiredMQClusterSenderChannelCount;
    // LOG.info(String.format("Detect that %d MQClusterSenderChannel items have
    // expired.",
    // expiredMQClusterSenderChannelCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired MQClusterSenderChannel items", e);
    // throw e;
    // }
    // }

    private void detectMQLocalQueue(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredMQLocalQueueCount = mqLocalQueueRepository.markExpiredMQLocalQueueItems(retiredDateTime,
                    zosPrefixId);
            ciCount += expiredMQLocalQueueCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectMQLocalQueue",
                    String.format("Detect that %d MQLocalQueue items have expired.", expiredMQLocalQueueCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectMQLocalQueue",
                    "Error while detecting expired MQLocalQueue items {}", e.toString());
            throw e;
        }
    }

    private void detectMQModelQueue(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredMQModelQueueCount = mqModelQueueRepository.markExpiredMQModelQueueItems(retiredDateTime,
                    zosPrefixId);
            ciCount += expiredMQModelQueueCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectMQModelQueue",
                    String.format("Detect that %d MQModelQueue items have expired.", expiredMQModelQueueCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectMQModelQueue",
                    "Error while detecting expired MQModelQueue items {}", e.toString());
            throw e;
        }
    }

    private void detectMQRemoteQueue(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredMQRemoteQueueCount = mqRemoteQueueRepository.markExpiredMQRemoteQueueItems(retiredDateTime,
                    zosPrefixId);
            ciCount += expiredMQRemoteQueueCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectMQRemoteQueue",
                    String.format("Detect that %d detectMQRemoteQueue items have expired.", expiredMQRemoteQueueCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectMQRemoteQueue",
                    "Error while detecting expired detectMQRemoteQueue items {}", e.toString());
            throw e;
        }
    }

    // private void detectMQReceiverChannel(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredMQReceiverChannelCount =
    // mqReceiverChannelRepository.markExpiredMQReceiverChannelItems(
    // retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredMQReceiverChannelCount;
    // LOG.info(String.format("Detect that %d MQReceiverChannel items have
    // expired.",
    // expiredMQReceiverChannelCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired MQReceiverChannel items", e);
    // throw e;
    // }
    // }

    // private void detectMQSenderChannel(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredMQSenderChannelCount =
    // mqSenderChannelRepository.markExpiredMQSenderChannelItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredMQSenderChannelCount;
    // LOG.info(String.format("Detect that %d MQSenderChannel items have expired.",
    // expiredMQSenderChannelCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired MQSenderChannel items", e);
    // throw e;
    // }
    // }

    // private void detectMQServerConnectionChannel(LocalDateTime retiredDateTime,
    // String zosPrefixId) {
    // try {
    // int expiredMQServerConnectionChannelCount =
    // mqServerConnectionChannelRepository
    // .markExpiredMQServerConnectionChannelItems(retiredDateTime, zosPrefixId);
    // ciCount += expiredMQServerConnectionChannelCount;
    // LOG.info(String.format("Detect that %d MQServerConnectionChannel items have
    // expired.",
    // expiredMQServerConnectionChannelCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired MQServerConnectionChannel items",
    // e);
    // throw e;
    // }
    // }

    private void detectIMSSubsystem(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredIMSSubsystemCount = imsSubsystemRepository.markExpiredIMSSubsystemItems(retiredDateTime,
                    zosPrefixId);
            ciCount += expiredIMSSubsystemCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectIMSSubsystem",
                    String.format("Detect that %d IMSSubsystem items have expired.", expiredIMSSubsystemCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectIMSSubsystem",
                    "Error while detecting expired IMSSubsystem items {}", e.toString());
            throw e;
        }
    }

    private void detectIMSDatabase(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredIMSDatabaseCount = imsDatabaseRepository.markExpiredIMSDatabaseItems(retiredDateTime,
                    zosPrefixId);
            ciCount += expiredIMSDatabaseCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectIMSDatabase",
                    String.format("Detect that %d IMSDatabase items have expired.", expiredIMSDatabaseCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectIMSDatabase",
                    "Error while detecting expired IMSDatabase items {}", e.toString());
            throw e;
        }
    }

    private void detectIMSTransaction(LocalDateTime retiredDateTime, String zosPrefixId) {
        try {
            int expiredIMSTransactionCount = imsTransactionRepository.markExpiredIMSTransactionItems(retiredDateTime,
                    zosPrefixId);
            ciCount += expiredIMSTransactionCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "detectIMSTransaction",
                    String.format("Detect that %d IMSTransaction items have expired.", expiredIMSTransactionCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "detectIMSTransaction",
                    "Error while detecting expired IMSTransaction items {}", e.toString());
            throw e;
        }
    }

    // private void detectIMSProgram(LocalDateTime retiredDateTime, String
    // zosPrefixId) {
    // try {
    // int expiredIMSProgramCount =
    // imsProgramRepository.markExpiredIMSProgramItems(retiredDateTime,
    // zosPrefixId);
    // ciCount += expiredIMSProgramCount;
    // LOG.info(String.format("Detect that %d IMSProgram items have expired.",
    // expiredIMSProgramCount));
    // } catch (Exception e) {
    // LOG.error("Error while detecting expired IMSProgram items", e);
    // throw e;
    // }
    // }

    // ****************************
    // Helper functions
    // ****************************

    /**
     * @param batchScanDate, IDML book's create timestamp, example: ' idml:create
     *                       timestamp="2024-04-23T06:47:15Z" '
     * @param interval:      the interval days between two batch discovering, units
     *                       Days
     *                       return a computed create timestamp, having the same
     *                       format with the
     *                       batchScanDate.
     **/
    private LocalDateTime getRetiredDateTime(String batchScanDate, Integer interval) {
        // Parse the ISO date string to LocalDateTime
        LocalDateTime batchDateTime = timestampConverter.getLocalDateTimeFromISODateTimeStr(batchScanDate);

        // Subtract the interval (days) directly from the LocalDateTime
        return batchDateTime.minusDays(interval);
    }
}
