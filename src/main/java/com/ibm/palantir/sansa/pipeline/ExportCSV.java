/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.pipeline;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;

import com.google.gson.JsonObject;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSDB2Conn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSFile;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSPlex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTable;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTableOverride;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSTransaction;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2BufferPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2DataSharingGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Database;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Subsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Table;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2TableSpace;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.LPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Relationship;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Sysplex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZOS;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSeriesComputer;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSubSystem;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSDB2ConnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSFileRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSPlexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSRegionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableOverrideRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2BufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DataSharingGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2SubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.LPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.SysplexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZOSRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSeriesComputerRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSubSystemRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.sansa.utils.CSVUtil;

public class ExportCSV extends AbstractPipeline {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();

    private static final String CLASSNAME = ExportCSV.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private ZSeriesComputerRepository zSeriesComputerRepository;
    private LPARRepository lparRepository;
    private ZOSRepository zOSRepository;
    private SysplexRepository sysplexRepository;
    private ZSubSystemRepository zSubSystemRepository;
    private CICSRegionRepository cicsRegionRepository;
    private CICSSystemInitTableRepository cicsSystemInitTableRepository;
    private CICSSystemInitTableOverrideRepository cicsSystemInitTableOverrideRepository;
    private CICSProgramRepository cicsProgramRepository;
    private CICSTransactionRepository cicsTransactionRepository;
    private CICSFileRepository cicsFileRepository;
    private DB2SubsystemRepository db2SubsystemRepository;
    private DB2DataSharingGroupRepository db2DataSharingGroupRepository;
    private DB2DatabaseRepository db2DatabaseRepository;
    private DB2TableSpaceRepository db2TableSpaceRepository;
    private DB2TableRepository db2TableRepository;
    private DB2BufferPoolRepository db2BufferPoolRepository;
    private CICSDB2ConnRepository cicsdb2ConnRepository;
    private CICSPlexRepository cicsPlexRepository;
    private RelationshipRepository relationshipRepository;

    private void transformZSeriesComputer(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSeriesComputer", "Running transformZSeriesComputer");
        List<ZSeriesComputer> zSeriesComputerList = zSeriesComputerRepository.findAll();
        CSVUtil<ZSeriesComputer> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, zSeriesComputerList, ZSeriesComputer.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSeriesComputer", "finish transformZSeriesComputer");
    }

    private void transformLpar(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformLpar", "Running transformLpar");
        List<LPAR> zoslparList = lparRepository.findAll();
        CSVUtil<LPAR> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, zoslparList, LPAR.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZOSLpar", "finish transformZOSLpar");
    }

    private void transformSysplex(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformSysplex", "Running transformSysplex");
        List<Sysplex> sysplexList = sysplexRepository.findAll();
        CSVUtil<Sysplex> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, sysplexList, Sysplex.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformSysplex", "finish transformSysplex");
    }

    private void transformZOS(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZOS", "Running transformZOS");
        List<ZOS> zosList = zOSRepository.findAll();
        CSVUtil<ZOS> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, zosList, ZOS.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZOS", "finish transformZOS");
    }

    private void transformZSubSystem(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSubSystem", "Running transformZSubSystem");
        List<ZSubSystem> zSubSystemList = zSubSystemRepository.findAll();
        CSVUtil<ZSubSystem> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, zSubSystemList, ZSubSystem.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformZSubSystem", "finish transformZSubSystem");
    }

    // ---------------------------------------CICS
    // group-----------------------------------------
    private void transformCICSRegion(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSRegion", "Running transformCICSRegion");
        List<CICSRegion> cicsRegionList = cicsRegionRepository.findAll();
        CSVUtil<CICSRegion> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, cicsRegionList, CICSRegion.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSRegion", "finish transformCICSRegion");
    }

    private void transformCICSSit(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSSit", "Running transformCICSSit");
        List<CICSSystemInitTable> cicsSitList = cicsSystemInitTableRepository.findAll();
        CSVUtil<CICSSystemInitTable> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, cicsSitList, CICSSystemInitTable.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSSit", "finish transformCICSSit");
    }

    private void transformCICSSitOverride(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSSitOverride", "Running transformCICSSitOverride");
        List<CICSSystemInitTableOverride> cicsSitOverridList = cicsSystemInitTableOverrideRepository
                .findAll();
        CSVUtil<CICSSystemInitTableOverride> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, cicsSitOverridList, CICSSystemInitTableOverride.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSSitOverride", "finish transformCICSSitOverride");
    }

    private void transformCICSProgram(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSProgram", "Running transformCICSProgram");
        List<CICSProgram> cicsProgramList = cicsProgramRepository.findAll();
        CSVUtil<CICSProgram> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, cicsProgramList, CICSProgram.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSProgram", "finish transformCICSProgram");
    }

    private void transformCICSTransaction(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSTransaction", "Running transformCICSTransaction");
        List<CICSTransaction> transactionList = cicsTransactionRepository.findAll();
        CSVUtil<CICSTransaction> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, transactionList, CICSTransaction.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSTransaction", "finish transformCICSTransaction");
    }

    private void transformCICSFile(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSFile", "Running transformCICSFile");
        List<CICSFile> fileList = cicsFileRepository.findAll();
        CSVUtil<CICSFile> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, fileList, CICSFile.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSFile", "finish transformCICSFile");
    }

    // ---------------------------------------DB2
    // group-----------------------------------------
    private void transformDb2DataSharingGroup(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2DataSharingGroup",
                "Running transformDb2DataSharingGroup");
        List<DB2DataSharingGroup> dataSharingGroupList = db2DataSharingGroupRepository.findAll();
        CSVUtil<DB2DataSharingGroup> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, dataSharingGroupList, DB2DataSharingGroup.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2DataSharingGroup",
                "finish transformDb2DataSharingGroup");
    }

    private void transformDb2Subsystem(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Subsystem", "Running transformDb2Subsystem");
        List<DB2Subsystem> db2SubsystemList = db2SubsystemRepository.findAll();
        CSVUtil<DB2Subsystem> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, db2SubsystemList, DB2Subsystem.class);

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Subsystem", "finish transformDb2Subsystem");
    }

    private void transformDb2Database(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Database", "Running transformDb2Database");
        List<DB2Database> db2DatabaseList = db2DatabaseRepository.findAll();
        CSVUtil<DB2Database> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, db2DatabaseList, DB2Database.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Database", "finish transformDb2Database");
    }

    private void transformDb2TableSpace(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2TableSpace", "Running transformDb2TableSpace");
        List<DB2TableSpace> db2TableSpaceList = db2TableSpaceRepository.findAll();
        CSVUtil<DB2TableSpace> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, db2TableSpaceList, DB2TableSpace.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2TableSpace", "finish transformDb2TableSpace");
    }

    private void transformDb2BufferPool(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2BufferPool", "Running transformDb2BufferPool");
        List<DB2BufferPool> bufferPoolList = db2BufferPoolRepository.findAll();
        CSVUtil<DB2BufferPool> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, bufferPoolList, DB2BufferPool.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2BufferPool", "finish transformDb2BufferPool");
    }

    // -------------------------------------Other
    // Resources------------------------------------
    private void transformCICSDB2Conn(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSDB2Conn", "Running transformCICSDB2Conn");
        List<CICSDB2Conn> cicsdb2ConnList = cicsdb2ConnRepository.findAll();
        CSVUtil<CICSDB2Conn> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, cicsdb2ConnList, CICSDB2Conn.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSDB2Conn", "finish transformCICSDB2Conn");
    }

    private void transformCICSPlex(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSPlex", "Running transformCICSPlex");
        List<CICSPlex> cicsPlexeList = cicsPlexRepository.findAll();
        CSVUtil<CICSPlex> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, cicsPlexeList, CICSPlex.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformCICSPlex", "finish transformCICSPlex");
    }

    private void transformDb2Table(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Table", "Running transformDb2Table");
        List<DB2Table> tableList = db2TableRepository.findAll();
        CSVUtil<DB2Table> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, tableList, DB2Table.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformDb2Table", "finish transformDb2Table");
    }

    private void transformRelationships(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformRelationships", "Running transformRelationships");
        List<Relationship> relationshipList = relationshipRepository.findAll();
        CSVUtil<Relationship> csvUtil = new CSVUtil<>();
        csvUtil.tableExportCSV(path, relationshipList, Relationship.class);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "transformRelationship", "finish transformRelationship");
    }

    public String export(String path) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "export", "Running export");
        HashMap<String, Object> repos = JPAManager.getJPAManager().getRepos();

        this.zSeriesComputerRepository = (ZSeriesComputerRepository) repos.get("zSeriesComputer");
        this.lparRepository = (LPARRepository) repos.get("zoslpar");
        this.zOSRepository = (ZOSRepository) repos.get("zos");
        this.sysplexRepository = (SysplexRepository) repos.get("sysplex");
        this.zSubSystemRepository = (ZSubSystemRepository) repos.get("zSubSystem");
        this.cicsRegionRepository = (CICSRegionRepository) repos.get("cicsRegion");
        this.cicsSystemInitTableRepository = (CICSSystemInitTableRepository) repos.get("cicsSystemInitTable");
        this.cicsSystemInitTableOverrideRepository = (CICSSystemInitTableOverrideRepository) repos
                .get("cicsSystemInitTableOverride");
        this.cicsProgramRepository = (CICSProgramRepository) repos.get("cicsProgram");
        this.cicsTransactionRepository = (CICSTransactionRepository) repos.get("cicsTransaction");
        this.cicsFileRepository = (CICSFileRepository) repos.get("cicsFile");
        this.db2SubsystemRepository = (DB2SubsystemRepository) repos.get("db2Subsystem");
        this.db2DataSharingGroupRepository = (DB2DataSharingGroupRepository) repos.get("db2dataSharingGroup");
        this.db2DatabaseRepository = (DB2DatabaseRepository) repos.get("db2Database");
        this.db2TableSpaceRepository = (DB2TableSpaceRepository) repos.get("db2TableSpace");
        this.db2TableRepository = (DB2TableRepository) repos.get("db2Table");
        this.db2BufferPoolRepository = (DB2BufferPoolRepository) repos.get("db2BufferPool");
        this.cicsdb2ConnRepository = (CICSDB2ConnRepository) repos.get("cicsdb2Conn");
        this.cicsPlexRepository = (CICSPlexRepository) repos.get("cicsPlex");
        this.relationshipRepository = (RelationshipRepository) repos.get("relationship");

        transformZSeriesComputer(path);
        transformLpar(path);
        transformSysplex(path);
        transformZOS(path);
        transformZSubSystem(path);
        transformCICSRegion(path);
        transformCICSSit(path);
        transformCICSSitOverride(path);
        transformCICSProgram(path);
        transformCICSTransaction(path);
        transformCICSFile(path);
        transformDb2DataSharingGroup(path);
        transformDb2Subsystem(path);
        transformDb2Database(path);
        transformDb2TableSpace(path);
        transformDb2BufferPool(path);
        transformDb2Table(path);

        transformCICSDB2Conn(path);
        transformCICSPlex(path);

        transformRelationships(path);

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "export", "export data from PostgresQL into csv file.");
        return "finish Export CSV successfully.";
    }

    public String run(PipelineConf pipelineConf) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", "Running ExportCSV");
        // connect to the OrientDB

        String persistID = pipelineConf.getPersist().get(0).toString();
        ConfigManager configManager = ConfigManager.getConfigManager();
        JsonObject jsonObject = configManager.searchConfig("persist", persistID);
        if (!jsonObject.get("type").getAsString().equals("File")) {
            String errCode = ErrorCode.PersistTypeError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "File", "ExportCSV");
            throw new ServiceException(errCode, msg, REPONAME, CLASSNAME, "run");
        }
        String path = jsonObject.get("url").getAsString();
        return export(path);
    }
}
