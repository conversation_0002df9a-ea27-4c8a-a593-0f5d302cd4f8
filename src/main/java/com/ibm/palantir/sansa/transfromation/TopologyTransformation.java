package com.ibm.palantir.sansa.transfromation;

import com.bazaarvoice.jolt.Chainr;
import com.bazaarvoice.jolt.JsonUtils;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.io.File;
import java.util.List;

public class TopologyTransformation {

    public static final String specpath = "./spec/";


    public String convertJSON(JsonElement jsonObject) {
        JsonObject object = jsonObject.getAsJsonObject();
        object.addProperty("name", object.get("name").getAsString().toLowerCase());
        String filename = specpath + "ZAPM.json";
        File file = new File(filename);
        if(file.exists() && file.isFile()) {
            List<Object> chainrSpecJSON = JsonUtils.filepathToList(filename);
            Chainr chainr = Chainr.fromSpec(chainrSpecJSON);
            Object transformedOutput = chainr.transform(JsonUtils.jsonToObject(object.toString()));
            Gson gson = new Gson();
            return gson.toJson(transformedOutput);
        }
        else{
            return jsonObject.toString();
        }
    }
}
