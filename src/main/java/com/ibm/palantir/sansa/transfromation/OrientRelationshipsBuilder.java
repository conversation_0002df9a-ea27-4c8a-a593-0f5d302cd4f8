/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.transfromation;

import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Relationship;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.utils.StringUtils;
import com.orientechnologies.orient.core.db.ODatabaseSession;
import com.orientechnologies.orient.core.record.OEdge;
import com.orientechnologies.orient.core.record.OVertex;
import com.orientechnologies.orient.core.sql.executor.OResult;
import com.orientechnologies.orient.core.sql.executor.OResultSet;

public class OrientRelationshipsBuilder {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = OrientRelationshipsBuilder.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private StringUtils stringUtils = new StringUtils();
    private ODatabaseSession db;

    public OrientRelationshipsBuilder(ODatabaseSession db) {
        this.db = db;
    }

    private OVertex getNodeById(String classType, String id) {
        OVertex node = null;
        OResultSet rs;
        if (classType == null || classType.equals("")) {
            rs = this.db.query("SELECT FROM V WHERE id = ?", id);
        } else {
            rs = this.db.query("SELECT FROM ? WHERE id = ?", classType, id);
        }
        while (rs.hasNext()) {
            OResult row = rs.next();
            if (row.isVertex()) {
                node = row.getVertex().get();
            }
        }
        rs.close();

        return node;
    }

    private Boolean isExistEdgeByProperty(String edgeClass, String source_target) {
        OEdge edge = null;
        OResultSet rs = this.db.query("SELECT FROM ? WHERE source_target = ?", edgeClass, source_target);
        while (rs.hasNext()) {
            OResult row = rs.next();
            if (row.isEdge()) {
                edge = row.getEdge().get();
            }
        }
        rs.close();

        return edge == null ? false : true;
    }

    // by Relationship
    public void buildOrCheckRelationship(Relationship relationship) {
        String sourceId = relationship.getSourceId();
        String sourceType = stringUtils.getClassTypeByDlaId(relationship.getSource());
        OVertex source = getNodeById(sourceType, sourceId);

        String targetId = relationship.getTargetId();
        String targetType = stringUtils.getClassTypeByDlaId(relationship.getTarget());
        OVertex target = getNodeById(targetType, targetId);

        if (source != null && target != null) {

            // remove AddressSpace relation
            if (sourceId.contains("AddressSpace") || targetId.contains("AddressSpace")) {
                return;
            }

            // reverseName have higher priority
            if (relationship.getReverseName() != null && !relationship.getReverseName().equals("")) {
                /**
                 * use reverseName and reverse the source and target
                 */
                buildOrCheckRelationship(target, source, relationship.getReverseName());
            } else {
                buildOrCheckRelationship(source, target, relationship.getName());
            }
        } else {
            String r = String.format(" for %s %s %s", sourceId, relationship.getName(), targetId);
            if (source == null) {
                LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "buildOrCheckRelationship", "getEmptyNode SourceId {}", r);
            } else {
                LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "buildOrCheckRelationship", "getEmptyNode TargetId {}", r);
            }
        }
    }

    // by target OVertex
    public void buildOrCheckRelationship(OVertex source, OVertex target, String relationshipName) {
        /**
         * !IMPORTANT
         * "contains" is keyword in OrientDB, so replace it with "contain"
         */
        if (relationshipName.equals("contains")) {
            relationshipName = "contain";
        }
        if (source != null && target != null) {
            String sourceId = source.getProperty("id");
            String targetId = target.getProperty("id");
            String source_target = String.format("%s_%s", sourceId,targetId);
            Boolean isExist = isExistEdgeByProperty(relationshipName, source_target);

            // create relationship if not exist
            if (isExist) {
                LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "buildOrCheckRelationship", "relationship: <{} {} {}> already exists.", sourceId, relationshipName, targetId);
            } else {
                OEdge relationship = this.db.newEdge(source, target, relationshipName);
                relationship.setProperty("source_target", source_target);
                relationship.save();
            }
        }
    }
}
