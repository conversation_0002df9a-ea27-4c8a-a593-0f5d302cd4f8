/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.transfromation;

import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.*;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;

import com.ibm.palantir.sansa.utils.StringUtils;
import com.orientechnologies.orient.core.db.ODatabaseSession;
import com.orientechnologies.orient.core.metadata.schema.OClass;
import com.orientechnologies.orient.core.metadata.schema.OType;
import com.orientechnologies.orient.core.record.OVertex;
import com.orientechnologies.orient.core.sql.executor.OResult;
import com.orientechnologies.orient.core.sql.executor.OResultSet;

public class OrientTransformation {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = OrientTransformation.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private StringUtils stringUtils = new StringUtils();
    private ODatabaseSession db;

    public OrientTransformation(ODatabaseSession db) {
        this.db = db;
    }

    public void createOrUpdateDLASchema() {
        String[] nodes = new String[]{
                "ZSeriesComputerSystem", "LPAR", "ZOS", "Sysplex", "ZSubSystem", "CICSRegion", "CICSSit", "CICSSitOverrides",
                "Program", "CICSTransaction", "CICSFile", "DB2Subsystem", "DB2DataSharingGroup", "Db2Database", "Db2StoredProcedure",
                "Db2TableSpace", "Db2Table", "Db2BufferPool", "MQSubsystem", "IMSSubsystem", "DB2Conn", "CICSPlex",
                "RemoteCICSRegion", "AddressSpace", "BindAddress", "Fqdn", "IpAddress", "IpInterface", "ProcessPool",
                "TcpPort", "UdpPort", "IdmlOperationTime", "MQSubsystem", "MQAliasQueue", "MQAuthInfo", "MQBufferPool",
                "MQClientConnectionChannel", "MQClusterReceiverChannel", "MQClusterSenderChannel", "MQLocalQueue",
                "MQModelQueue", "MQReceiverChannel", "MQRemoteQueue", "MQSenderChannel", "MQServerConnectionChannel",
                "IMSSubsystem", "IMSDatabase", "IMSTransaction", "IMSSysplexGroup", "MQQueueSharingGroup"
        };
        for (String node : nodes) {
            OClass nodeClass = db.getClass(node);
            if (nodeClass == null) {
                nodeClass = db.createVertexClass(node);
            }
            if (nodeClass.getProperty("id") == null) {
                nodeClass.createProperty("id", OType.STRING);
                nodeClass.createProperty("dlaId", OType.STRING);
                nodeClass.createProperty("prefixId", OType.STRING);
                nodeClass.createProperty("name", OType.STRING);
                nodeClass.createProperty("displayName", OType.STRING);
                nodeClass.createProperty("scanDate", OType.STRING);
                nodeClass.createIndex(node + "_idx", OClass.INDEX_TYPE.NOTUNIQUE, "id");
                nodeClass.createIndex(node + "_dlaidx", OClass.INDEX_TYPE.NOTUNIQUE, "dlaId");
                nodeClass.createIndex(node + "_namex", OClass.INDEX_TYPE.NOTUNIQUE, "name");
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "createOrUpdateDLASchema", "OVertex schema have been checked.");

        /**
         * !IMPORTANT
         * "contains" is keyword in OrientDB, so replace it with "contain"
         */
        String[] relationships = new String[]{
                "contain", "programCall", "uses", "runs", "hasMember", "federates", "transactionalDependency", "connects",
                "bindsTo", "runsOn", "assignedTo", "accessedVia", "bindsAsPrimary", "memberOf", "manages", "authorizes"
        };
        for (String relationship : relationships) {
            OClass edgeClass = db.getClass(relationship);
            if (edgeClass == null) {
                edgeClass = db.createEdgeClass(relationship);
            }
            if (edgeClass.getProperty("source_target") == null) {
                edgeClass.createProperty("source_target", OType.STRING);
                edgeClass.createIndex(relationship + "_idx", OClass.INDEX_TYPE.UNIQUE, "source_target");
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "createOrUpdateDLASchema", "OEdge schema have been checked.");
    }

//    private OVertex getNodeById(String classType, String id) {
//
//        OVertex node = null;
//        OResultSet rs;
//        if (classType == null || classType.equals("")) {
//            rs = this.db.query("SELECT FROM V WHERE id = ?", id);
//        } else {
//            rs = this.db.query("SELECT FROM ? WHERE id = ?", classType, id);
//        }
//        while (rs.hasNext()) {
//            OResult row = rs.next();
//            if (row.isVertex()) {
//                node = row.getVertex().get();
//            }
//        }
//        rs.close();
//
//        return node;
//    }

    private OVertex getNodeById(String classType, String id) {

        OVertex node = null;
        OResultSet rs;
        if (classType == null || classType.equals("")) {
            rs = this.db.query("SELECT FROM V WHERE id", id);
        } else {
            rs = this.db.query("SELECT FROM ? WHERE id = ?", classType, id);
        }
        while (rs.hasNext()) {
            OResult row = rs.next();
            if (row.isVertex()) {
                node = row.getVertex().get();
            }
        }
        rs.close();

        return node;
    }

    private OVertex getNodeByName(String classType, String name) {
        OVertex node = null;
        OResultSet rs;
        if (classType == null || classType.equals("")) {
            rs = this.db.query("SELECT FROM V WHERE name = ?", name);
        } else {
            rs = this.db.query("SELECT FROM ? WHERE name = ?", classType, name);
        }
        while (rs.hasNext()) {
            OResult row = rs.next();
            if (row.isVertex()) {
                node = row.getVertex().get();
            }
        }
        rs.close();

        return node;
    }


    public OVertex createOrUpdateZSeriesComputer(ZSeriesComputer zSeriesComputer) {
        String classType = stringUtils.getClassTypeByDlaId(zSeriesComputer.getDlaId());
        OVertex result = getNodeById(classType, zSeriesComputer.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", zSeriesComputer.getId());
            result.setProperty("dlaId", zSeriesComputer.getDlaId());
            result.setProperty("prefixId", zSeriesComputer.getPrefixId());
            result.setProperty("source", zSeriesComputer.getDataSource());
        }

        result.setProperty("scanDate", zSeriesComputer.getScanDate());
        result.setProperty("label", zSeriesComputer.getLabel());
        result.setProperty("name", zSeriesComputer.getName());
        result.setProperty("make", zSeriesComputer.getMake());
        result.setProperty("manufacturer", zSeriesComputer.getManufacturer());
        result.setProperty("model", zSeriesComputer.getModel());
        result.setProperty("modelId", zSeriesComputer.getModelId());
        result.setProperty("processingCapacity", zSeriesComputer.getProcessingCapacity());
        result.setProperty("processCapacityUnits", zSeriesComputer.getProcessCapacityUnits());
        result.setProperty("serialNumber", zSeriesComputer.getSerialNumber());
        result.setProperty("memorySize", zSeriesComputer.getMemorySize());
        result.setProperty("numCPUs", zSeriesComputer.getNumCPUs());
        result.setProperty("displayName", zSeriesComputer.getName());
        if (zSeriesComputer.getName() == null) {
            result.setProperty("name", zSeriesComputer.getSerialNumber());
            result.setProperty("label", zSeriesComputer.getSerialNumber());
        }
        return result.save();
    }

    public OVertex createOrUpdateLpar(LPAR lpar) {
        String classType = stringUtils.getClassTypeByDlaId(lpar.getDlaId());
        OVertex result = getNodeById(classType, lpar.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", lpar.getId());
            result.setProperty("dlaId", lpar.getDlaId());
            result.setProperty("prefixId", lpar.getPrefixId());
            result.setProperty("source", lpar.getDataSource());
        }

        result.setProperty("scanDate", lpar.getScanDate());
        result.setProperty("label", lpar.getLabel());
        result.setProperty("name", lpar.getName());
        result.setProperty("LPARName", lpar.getLparName());
        result.setProperty("VMID", lpar.getVmId());
        result.setProperty("numCPUs", lpar.getNumCPUs());
        result.setProperty("numSharedCPs", lpar.getNumSharedCPs());
        result.setProperty("numDedicatedCPs", lpar.getNumDedicatedCPs());
        result.setProperty("model", lpar.getModel());
        result.setProperty("modelId", lpar.getModelId());
        result.setProperty("memorySize", lpar.getMemorySize());
        if (lpar.getLparName() == null) {
            result.setProperty("displayName", lpar.getLparName());
        } else {
            result.setProperty("displayName", lpar.getName());
        }
        return result.save();
    }

    public OVertex createOrUpdateSysplex(Sysplex sysplex) {
        String classType = stringUtils.getClassTypeByDlaId(sysplex.getDlaId());
        OVertex result = getNodeById(classType, sysplex.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", sysplex.getId());
            result.setProperty("dlaId", sysplex.getDlaId());
            result.setProperty("prefixId", sysplex.getPrefixId());
            result.setProperty("source", sysplex.getDataSource());
        }

        result.setProperty("scanDate", sysplex.getScanDate());
        result.setProperty("name", sysplex.getName());
        result.setProperty("label", sysplex.getLabel());
        if (sysplex.getMaxSys() != null) {
            result.setProperty("maxSys", sysplex.getMaxSys().toString());
        }
        if (sysplex.getPlexMode() != null) {
            result.setProperty("plexMode", sysplex.getPlexMode().toString());
        }
        if (sysplex.getGRSConfig() != null) {
            result.setProperty("GRSConfig", sysplex.getGRSConfig().toString());
        }
        result.setProperty("displayName", sysplex.getName());
        return result.save();
    }

    public OVertex createOrUpdateZos(ZOS zos) {
        String classType = stringUtils.getClassTypeByDlaId(zos.getDlaId());
        OVertex result = getNodeById(classType, zos.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", zos.getId());
            result.setProperty("dlaId", zos.getDlaId());
            result.setProperty("prefixId", zos.getPrefixId());
            result.setProperty("source", zos.getDataSource());
        }

        result.setProperty("scanDate", zos.getScanDate());
        result.setProperty("label", zos.getLabel());
        result.setProperty("name", zos.getName());
        result.setProperty("smfId", zos.getSmfId());
        result.setProperty("netId", zos.getNetID());
        result.setProperty("sscp", zos.getSscp());
        result.setProperty("fqdn", zos.getFqdn());
        result.setProperty("osName", zos.getOsName());
        result.setProperty("version", zos.getOsVersion());
        result.setProperty("sysResVolume", zos.getSysResVolume());
        result.setProperty("iplParmDataset", zos.getIplParmDataset());
        result.setProperty("iplParmMember", zos.getIplParmMember());
        result.setProperty("iplParmDevice", zos.getIplParmDevice());
        result.setProperty("iplParmVolume", zos.getIplParmVolume());
        result.setProperty("osRsuLevel", zos.getOsRSULevel());
        result.setProperty("osFriendlyName", zos.getOsFriendlyName());
        result.setProperty("displayName", zos.getName());
        return result.save();
    }

    public OVertex createOrUpdateCICSPlex(CICSPlex cicsPlex) {
        String classType = stringUtils.getClassTypeByDlaId(cicsPlex.getDlaId());
        OVertex result = getNodeById(classType, cicsPlex.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", cicsPlex.getId());
            result.setProperty("dlaId", cicsPlex.getDlaId());
            result.setProperty("prefixId", cicsPlex.getPrefixId());
            result.setProperty("source", cicsPlex.getDataSource());
        }

        result.setProperty("scanDate", cicsPlex.getScanDate());
        result.setProperty("mvsSysId", cicsPlex.getMvsSysId());
        result.setProperty("name", cicsPlex.getName());
        result.setProperty("displayName", cicsPlex.getName());
        return result.save();
    }

    // The CICSRegion and the ZSubSystem have the same DlaId, so need to use getNodeByName.
    public OVertex createOrUpdateZSubSystem(ZSubSystem zSubSystem) {
        String classType = stringUtils.getClassTypeByDlaId(zSubSystem.getDlaId());
        OVertex result = getNodeById(classType, zSubSystem.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", zSubSystem.getId());
            result.setProperty("dlaId", zSubSystem.getDlaId());
            result.setProperty("prefixId", zSubSystem.getPrefixId());
            result.setProperty("source", zSubSystem.getDataSource());
        }

        result.setProperty("scanDate", zSubSystem.getScanDate());
        result.setProperty("label", zSubSystem.getLabel());
        result.setProperty("name", zSubSystem.getSubSystemName());
        result.setProperty("type", zSubSystem.getType());
        result.setProperty("version", zSubSystem.getSubSystemVersion());
        result.setProperty("manufacturer", zSubSystem.getSubSystemManufacturer());
        result.setProperty("displayName", zSubSystem.getSubSystemName());
        return result.save();
    }

    // The CICSRegion and the ZSubSystem have the same DlaId, so need to use getNodeByName.
    public OVertex createOrUpdateCICSRegion(CICSRegion cicsRegion) {
        String classType = stringUtils.getClassTypeByDlaId(cicsRegion.getDlaId());
        OVertex result = getNodeById(classType, cicsRegion.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", cicsRegion.getId());
            result.setProperty("dlaId", cicsRegion.getDlaId());
            result.setProperty("prefixId", cicsRegion.getPrefixId());
            result.setProperty("source", cicsRegion.getDataSource());
        }

        result.setProperty("scanDate", cicsRegion.getScanDate());
        result.setProperty("label", cicsRegion.getLabel());
        result.setProperty("name", cicsRegion.getRegionName());
        result.setProperty("applID", cicsRegion.getApplicationId());
        result.setProperty("netID", cicsRegion.getNetId());
        result.setProperty("jobName", cicsRegion.getJobName());
        result.setProperty("dfhCICSType", cicsRegion.getDfhCICSType());
        result.setProperty("dfhCICSHLQ", cicsRegion.getDfhCICSHLQ());
        result.setProperty("dfhLEHLQ", cicsRegion.getDfhLEHLQ());
        result.setProperty("dfhRegionHLQ", cicsRegion.getDfhRegionHLQ());
        result.setProperty("dfhRegionLogstream", cicsRegion.getDfhRegionLogstream());
        result.setProperty("dfhRegionCICSSvc", cicsRegion.getDfhRegionCICSSvc());
        result.setProperty("dfhRegionDefaultUser", cicsRegion.getDfhRegionDefaultUser());
        result.setProperty("version", cicsRegion.getCicsVersion());
        result.setProperty("displayName", cicsRegion.getRegionName());
        return result.save();
    }

    public OVertex createOrUpdateCICSSit(CICSSystemInitTable cicsSit) {
        String classType = stringUtils.getClassTypeByDlaId(cicsSit.getDlaId());
        OVertex result = getNodeById(classType, cicsSit.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", cicsSit.getId());
            result.setProperty("dlaId", cicsSit.getDlaId());
            result.setProperty("prefixId", cicsSit.getPrefixId());
            result.setProperty("source", cicsSit.getDataSource());
        }

        result.setProperty("scanDate", cicsSit.getScanDate());
        result.setProperty("name", cicsSit.getName());
        result.setProperty("applID", cicsSit.getSitApplicationId());
        result.setProperty("CICSSvc", cicsSit.getSitCICSSvc());
        result.setProperty("CPsConn", cicsSit.getSitCPsConn());
        result.setProperty("CSDAcc", cicsSit.getSitCSDAcc());
        result.setProperty("CSDRls", cicsSit.getSitCSDRls());
        result.setProperty("defaultUser", cicsSit.getSitDefaultUser());
        result.setProperty("GMText", cicsSit.getSitGMText());
        result.setProperty("GMTransaction", cicsSit.getSitGMTransaction());
        result.setProperty("IRC", cicsSit.getSitIRC());
        result.setProperty("ISC", cicsSit.getSitISC());
        result.setProperty("jvmProfileDir", cicsSit.getSitJVMProfileDirectory());
        result.setProperty("displayName", cicsSit.getName());
        return result.save();
    }

    public OVertex createOrUpdateCICSSitOverrides(CICSSystemInitTableOverride cicsSitOveride) {
        String classType = stringUtils.getClassTypeByDlaId(cicsSitOveride.getDlaId());
        OVertex result = getNodeById(classType, cicsSitOveride.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", cicsSitOveride.getId());
            result.setProperty("dlaId", cicsSitOveride.getDlaId());
            result.setProperty("prefixId", cicsSitOveride.getPrefixId());
            result.setProperty("source", cicsSitOveride.getDataSource());
        }

        result.setProperty("scanDate", cicsSitOveride.getScanDate());
        result.setProperty("name", cicsSitOveride.getName());
        result.setProperty("applID", cicsSitOveride.getSitApplicationId());
        result.setProperty("CICSSvc", cicsSitOveride.getSitCICSSvc());
        result.setProperty("CPsConn", cicsSitOveride.getSitCPsConn());
        result.setProperty("CSDAcc", cicsSitOveride.getSitCSDAcc());
        result.setProperty("CSDRls", cicsSitOveride.getSitCSDRls());
        result.setProperty("defaultUser", cicsSitOveride.getSitDefaultUser());
        result.setProperty("GMText", cicsSitOveride.getSitGMText());
        result.setProperty("GMTransaction", cicsSitOveride.getSitGMTransaction());
        result.setProperty("IRC", cicsSitOveride.getSitIRC());
        result.setProperty("ISC", cicsSitOveride.getSitISC());
        result.setProperty("jvmProfileDir", cicsSitOveride.getSitJVMProfileDirectory());
        result.setProperty("displayName", cicsSitOveride.getName());
        return result.save();
    }

    // transfer to Program
    public OVertex createOrUpdateCICSProgram(CICSProgram cicsProgram) {
        String classType = stringUtils.getClassTypeByDlaId(cicsProgram.getDlaId());
        OVertex result = getNodeById(classType, cicsProgram.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", cicsProgram.getId());
            result.setProperty("dlaId", cicsProgram.getDlaId());
            result.setProperty("prefixId", cicsProgram.getPrefixId());
            result.setProperty("source", cicsProgram.getDataSource());
        }

        result.setProperty("scanDate", cicsProgram.getScanDate());
        result.setProperty("label", cicsProgram.getLabel());
        result.setProperty("name", cicsProgram.getProgramName());
        result.setProperty("description", cicsProgram.getProgramDescription());
        result.setProperty("group", cicsProgram.getProgramGroup());
        result.setProperty("executionKey", cicsProgram.getProgramExecutionKey());
        result.setProperty("dataLocation", cicsProgram.getProgramDataLocation());
        result.setProperty("language", cicsProgram.getProgramLanguage());
        result.setProperty("displayName", cicsProgram.getProgramName());
        result.setProperty("type", "CICS");
        return result.save();
    }

    public OVertex createOrUpdateCICSTransaction(CICSTransaction cicsTransaction) {
        String classType = stringUtils.getClassTypeByDlaId(cicsTransaction.getDlaId());
        OVertex result = getNodeById(classType, cicsTransaction.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", cicsTransaction.getId());
            result.setProperty("dlaId", cicsTransaction.getDlaId());
            result.setProperty("prefixId", cicsTransaction.getPrefixId());
            result.setProperty("source", cicsTransaction.getDataSource());
        }

        result.setProperty("scanDate", cicsTransaction.getScanDate());
        result.setProperty("label", cicsTransaction.getLabel());
        result.setProperty("name", cicsTransaction.getTransactionName());
        result.setProperty("dataKey", cicsTransaction.getDataKey());
        result.setProperty("dataLocation", cicsTransaction.getDataLocation());
        result.setProperty("initialProgram", cicsTransaction.getInitialProgram());
        result.setProperty("displayName", cicsTransaction.getTransactionName());
        return result.save();
    }

    public OVertex createOrUpdateCICSFile(CICSFile cicsFile) {
        String classType = stringUtils.getClassTypeByDlaId(cicsFile.getDlaId());
        OVertex result = getNodeById(classType, cicsFile.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", cicsFile.getId());
            result.setProperty("dlaId", cicsFile.getDlaId());
            result.setProperty("prefixId", cicsFile.getPrefixId());
            result.setProperty("source", cicsFile.getDataSource());
        }

        result.setProperty("scanDate", cicsFile.getScanDate());
        result.setProperty("label", cicsFile.getLabel());
        result.setProperty("name", cicsFile.getFileName());
        result.setProperty("group", cicsFile.getFileGroup());
        result.setProperty("datasetName", cicsFile.getDatasetName());
        // all the below properties are null
        result.setProperty("recordSize", cicsFile.getRecordSize());
        result.setProperty("keyLength", cicsFile.getKeyLength());
        result.setProperty("status", cicsFile.getStatus());
        result.setProperty("openTime", cicsFile.getOpenTime());
        result.setProperty("add", cicsFile.getAdd());
        result.setProperty("browse", cicsFile.getBrowse());
        result.setProperty("fileRead", cicsFile.getFileRead());
        result.setProperty("fileDelete", cicsFile.getFileDelete());
        result.setProperty("fileUpdate", cicsFile.getFileUpdate());
        result.setProperty("displayName", cicsFile.getFileName());
        return result.save();
    }

    public OVertex createOrUpdateDb2Subsystem(DB2Subsystem db2Subsystem) {
        String classType = stringUtils.getClassTypeByDlaId(db2Subsystem.getDlaId());
        OVertex result = getNodeById(classType, db2Subsystem.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", db2Subsystem.getId());
            result.setProperty("dlaId", db2Subsystem.getDlaId());
            result.setProperty("prefixId", db2Subsystem.getPrefixId());
            result.setProperty("source", db2Subsystem.getDataSource());
        }

        result.setProperty("scanDate", db2Subsystem.getScanDate());
        result.setProperty("label", db2Subsystem.getLabel());
        result.setProperty("name", db2Subsystem.getSubSystemName());
        result.setProperty("versionString", db2Subsystem.getVersionString());
        // the below properties are null
        result.setProperty("modifier", db2Subsystem.getModifier());
        result.setProperty("release", db2Subsystem.getRelease());

        result.setProperty("keyName", db2Subsystem.getKeyName());
        result.setProperty("commandPrefixName", db2Subsystem.getCommandPrefixName());
        result.setProperty("DDFLocation", db2Subsystem.getDDFLocation());
        result.setProperty("displayName", db2Subsystem.getSubSystemName());
        return result.save();
    }

    public OVertex createOrUpdateDb2DataSharingGroup(DB2DataSharingGroup db2DataSharingGroup) {
        String classType = stringUtils.getClassTypeByDlaId(db2DataSharingGroup.getDlaId());
        OVertex result = getNodeById(classType, db2DataSharingGroup.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", db2DataSharingGroup.getId());
            result.setProperty("dlaId", db2DataSharingGroup.getDlaId());
            result.setProperty("prefixId", db2DataSharingGroup.getPrefixId());
            result.setProperty("source", db2DataSharingGroup.getDataSource());
        }

        result.setProperty("scanDate", db2DataSharingGroup.getScanDate());
        result.setProperty("name", db2DataSharingGroup.getName());
        result.setProperty("label", db2DataSharingGroup.getLabel());
        result.setProperty("groupAttachName", db2DataSharingGroup.getGroupAttachName());
        result.setProperty("groupFunction", db2DataSharingGroup.getGroupFunction());
        result.setProperty("displayName", db2DataSharingGroup.getName());
        return result.save();
    }

    public OVertex createOrUpdateDb2Database(DB2Database db2Database) {
        String classType = stringUtils.getClassTypeByDlaId(db2Database.getDlaId());
        OVertex result = getNodeById(classType, db2Database.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", db2Database.getId());
            result.setProperty("dlaId", db2Database.getDlaId());
            result.setProperty("prefixId", db2Database.getPrefixId());
            result.setProperty("source", db2Database.getDataSource());
        }

        result.setProperty("scanDate", db2Database.getScanDate());
        result.setProperty("name", db2Database.getName());
        result.setProperty("label", db2Database.getLabel());
        result.setProperty("displayName", db2Database.getName());
        return result.save();
    }

    public OVertex createOrUpdateDb2TableSpace(DB2TableSpace db2TableSpace) {
        String classType = stringUtils.getClassTypeByDlaId(db2TableSpace.getDlaId());
        OVertex result = getNodeById(classType, db2TableSpace.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", db2TableSpace.getId());
            result.setProperty("dlaId", db2TableSpace.getDlaId());
            result.setProperty("prefixId", db2TableSpace.getPrefixId());
            result.setProperty("source", db2TableSpace.getDataSource());
        }

        result.setProperty("scanDate", db2TableSpace.getScanDate());
        result.setProperty("name", db2TableSpace.getName());
        result.setProperty("label", db2TableSpace.getLabel());
        result.setProperty("size", db2TableSpace.getSize());
        result.setProperty("pageSize", db2TableSpace.getPageSize());
        result.setProperty("spaceId", db2TableSpace.getSpaceId());
        result.setProperty("type", db2TableSpace.getType());
        result.setProperty("displayName", db2TableSpace.getName());
        return result.save();
    }

    public OVertex createOrUpdateDb2BufferPool(DB2BufferPool db2BufferPool) {
        String classType = stringUtils.getClassTypeByDlaId(db2BufferPool.getDlaId());
        OVertex result = getNodeById(classType, db2BufferPool.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", db2BufferPool.getId());
            result.setProperty("dlaId", db2BufferPool.getDlaId());
            result.setProperty("prefixId", db2BufferPool.getPrefixId());
            result.setProperty("source", db2BufferPool.getDataSource());
        }

        result.setProperty("scanDate", db2BufferPool.getScanDate());
        result.setProperty("name", db2BufferPool.getName());
        result.setProperty("label", db2BufferPool.getLabel());
        result.setProperty("numPages", db2BufferPool.getNumPages());
        result.setProperty("pageSize", db2BufferPool.getPageSize());
        result.setProperty("poolId", db2BufferPool.getPoolId());
        result.setProperty("displayName", db2BufferPool.getName());
        return result.save();
    }

    public OVertex createOrUpdateDb2StoredProcedure(DB2StoredProcedure db2StoredProcedure) {
        String classType = stringUtils.getClassTypeByDlaId(db2StoredProcedure.getDlaId());
        OVertex result = getNodeById(classType, db2StoredProcedure.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", db2StoredProcedure.getId());
            result.setProperty("dlaId", db2StoredProcedure.getDlaId());
            result.setProperty("prefixId", db2StoredProcedure.getPrefixId());
            result.setProperty("source", db2StoredProcedure.getDataSource());
        }

        result.setProperty("scanDate", db2StoredProcedure.getScanDate());
        result.setProperty("label", db2StoredProcedure.getLabel());
        result.setProperty("name", db2StoredProcedure.getName());
        result.setProperty("routineType", db2StoredProcedure.getRoutineType());
        result.setProperty("origin", db2StoredProcedure.getOrigin());
        result.setProperty("specificName", db2StoredProcedure.getSpecificName());
        result.setProperty("externalName", db2StoredProcedure.getExternalName());
        result.setProperty("collId", db2StoredProcedure.getCollId());
        result.setProperty("language", db2StoredProcedure.getLanguage());
        return result.save();
    }

    public OVertex getOrCreateDB2Conn(CICSDB2Conn cicsdb2Conn) {
        String classType = stringUtils.getClassTypeByDlaId(cicsdb2Conn.getDlaId());
        OVertex result = getNodeById(classType, cicsdb2Conn.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", cicsdb2Conn.getId());
            result.setProperty("dlaId", cicsdb2Conn.getDlaId());
            result.setProperty("prefixId", cicsdb2Conn.getPrefixId());
            result.setProperty("displayName", cicsdb2Conn.getDlaId());
            result.setProperty("source", cicsdb2Conn.getDataSource());
            result.setProperty("scanDate", cicsdb2Conn.getScanDate());
            return result.save();
        } else {
            return result;
        }
    }

    public OVertex createOrUpdateDb2Table(DB2Table db2Table) {
        String classType = stringUtils.getClassTypeByDlaId(db2Table.getDlaId());
        OVertex result = getNodeById(classType, db2Table.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", db2Table.getId());
            result.setProperty("dlaId", db2Table.getDlaId());
            result.setProperty("prefixId", db2Table.getPrefixId());
            result.setProperty("source", db2Table.getDataSource());
        }

        result.setProperty("scanDate", db2Table.getScanDate());
        result.setProperty("label", db2Table.getLabel());
        result.setProperty("name", db2Table.getName());
        result.setProperty("tableSpace", db2Table.getTableSpace());
        result.setProperty("dataBase", db2Table.getDataBase());
        result.setProperty("sharingGroup", db2Table.getSharingGroup());
        result.setProperty("displayName", db2Table.getName());

        return result.save();
    }

    public OVertex createOrUpdateAddressSpace(AddressSpace addressSpace) {
        String classType =  stringUtils.getClassTypeByDlaId(addressSpace.getDlaId());
        OVertex result = getNodeById(classType, addressSpace.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", addressSpace.getId());
            result.setProperty("dlaId", addressSpace.getDlaId());
            result.setProperty("prefixId", addressSpace.getPrefixId());
            result.setProperty("source", addressSpace.getDataSource());
        }

        result.setProperty("scanDate", addressSpace.getScanDate());
        result.setProperty("label", addressSpace.getLabel());
        result.setProperty("jobName", addressSpace.getJobName());
        result.setProperty("jobType", addressSpace.getJobType());
        result.setProperty("stepName", addressSpace.getStepName());
        result.setProperty("displayName", addressSpace.getLabel());

        return result.save();
    }

    public OVertex createOrUpdateBindAddress(BindAddress bindAddress) {
        String classType =  stringUtils.getClassTypeByDlaId(bindAddress.getDlaId());
        OVertex result = getNodeById(classType, bindAddress.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", bindAddress.getId());
            result.setProperty("dlaId", bindAddress.getDlaId());
            result.setProperty("prefixId", bindAddress.getPrefixId());
            result.setProperty("source", bindAddress.getDataSource());
        }

        result.setProperty("scanDate", bindAddress.getScanDate());
        result.setProperty("path", bindAddress.getPath());
        result.setProperty("portNumber", bindAddress.getPortNumber());
        result.setProperty("displayName", bindAddress.getDlaId());

        return result.save();
    }

    public OVertex createOrUpdateFqdn(Fqdn fqdn) {
        String classType = stringUtils.getClassTypeByDlaId(fqdn.getDlaId());
        OVertex result = getNodeById(classType, fqdn.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", fqdn.getId());
            result.setProperty("dlaId", fqdn.getDlaId());
            result.setProperty("prefixId", fqdn.getPrefixId());
            result.setProperty("source", fqdn.getDataSource());
        }

        result.setProperty("scanDate", fqdn.getScanDate());
        result.setProperty("fqdn", fqdn.getFqdn());
        result.setProperty("displayName", fqdn.getFqdn());

        return result.save();
    }

    public OVertex createOrUpdateIpAddress(IpAddress ipAddress) {
        String classType = stringUtils.getClassTypeByDlaId(ipAddress.getDlaId());
        OVertex result = getNodeById(classType, ipAddress.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", ipAddress.getId());
            result.setProperty("dlaId", ipAddress.getDlaId());
            result.setProperty("prefixId", ipAddress.getPrefixId());
            result.setProperty("source", ipAddress.getDataSource());
        }

        result.setProperty("scanDate", ipAddress.getScanDate());
        result.setProperty("label", ipAddress.getLabel());
        result.setProperty("stringNotation", ipAddress.getStringNotation());
        result.setProperty("displayName", ipAddress.getLabel());

        return result.save();
    }

    public OVertex getOrCreateIpInterface(IpInterface ipInterface) {
        String dlaId = ipInterface.getDlaId();
        String classType = stringUtils.getClassTypeByDlaId(dlaId);
        OVertex result = getNodeById(classType, ipInterface.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", ipInterface.getId());
            result.setProperty("dlaId", ipInterface.getDlaId());
            result.setProperty("prefixId", ipInterface.getPrefixId());
            result.setProperty("source", ipInterface.getDataSource());
            result.setProperty("displayName", dlaId);
            result.setProperty("name", dlaId.split("-")[0]);
            result.setProperty("scanDate", ipInterface.getScanDate());
            return result.save();
        } else {
            return result;
        }
    }

    public OVertex createOrUpdateProcessPool(ProcessPool processPool) {
        String classType = stringUtils.getClassTypeByDlaId(processPool.getDlaId());
        OVertex result = getNodeById(classType, processPool.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", processPool.getId());
            result.setProperty("dlaId", processPool.getDlaId());
            result.setProperty("prefixId", processPool.getPrefixId());
            result.setProperty("source", processPool.getDataSource());
        }

        result.setProperty("scanDate", processPool.getScanDate());
        result.setProperty("label", processPool.getLabel());
        result.setProperty("name", processPool.getName());
        result.setProperty("cmdLine", processPool.getCmdLine());
        result.setProperty("displayName", processPool.getName());

        return result.save();
    }

    public OVertex createOrUpdateTcpPort(TcpPort tcpPort) {
        String classType = stringUtils.getClassTypeByDlaId(tcpPort.getDlaId());
        OVertex result = getNodeById(classType, tcpPort.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", tcpPort.getId());
            result.setProperty("dlaId", tcpPort.getDlaId());
            result.setProperty("prefixId", tcpPort.getPrefixId());
            result.setProperty("source", tcpPort.getDataSource());
        }

        result.setProperty("scanDate", tcpPort.getScanDate());
        result.setProperty("label", tcpPort.getLabel());
        result.setProperty("portNumber", tcpPort.getPortNumber());
        result.setProperty("displayName", tcpPort.getLabel());

        return result.save();
    }

    public OVertex createOrUpdateUdpPort(UdpPort udpPort) {
        String classType = stringUtils.getClassTypeByDlaId(udpPort.getDlaId());
        OVertex result = getNodeById(classType, udpPort.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", udpPort.getId());
            result.setProperty("dlaId", udpPort.getDlaId());
            result.setProperty("prefixId", udpPort.getPrefixId());
            result.setProperty("source", udpPort.getDataSource());
        }

        result.setProperty("scanDate", udpPort.getScanDate());
        result.setProperty("label", udpPort.getLabel());
        result.setProperty("portNumber", udpPort.getPortNumber());
        result.setProperty("displayName", udpPort.getLabel());

        return result.save();
    }

    public OVertex createOrUpdateIdmlOperationTime(IdmlOperationTime idmlOperationTime) {
        OVertex node = getNodeById("IdmlOperationTime", idmlOperationTime.getId());

        if (node == null) {
            node = db.newVertex("IdmlOperationTime");
            node.setProperty("id", idmlOperationTime.getId());
            node.setProperty("dlaId", "");
            node.setProperty("hostLabel", idmlOperationTime.getHostLabel());
            node.setProperty("prefixId", idmlOperationTime.getPrefixId());
            node.setProperty("lastScanDate", idmlOperationTime.getCreateTimestamp());
        } else {
            if (!node.getProperty("lastScanDate").equals(idmlOperationTime.getCreateTimestamp())) {
                node.setProperty("lastScanDate", idmlOperationTime.getCreateTimestamp());
            }
        }

        return node.save();
    }

    public OVertex createOrUpdateMQSubsystem(MQSubsystem mqSubsystem) {
        String classType = stringUtils.getClassTypeByDlaId(mqSubsystem.getDlaId());
        OVertex node = getNodeById(classType, mqSubsystem.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqSubsystem.getId());
            node.setProperty("dlaId", mqSubsystem.getDlaId());
            node.setProperty("prefixId", mqSubsystem.getPrefixId());
            node.setProperty("source", mqSubsystem.getDataSource());
        }

        node.setProperty("scanDate", mqSubsystem.getScanDate());
        node.setProperty("displayName", mqSubsystem.getSubsystemName());
        node.setProperty("label", mqSubsystem.getLabel());
        node.setProperty("name", mqSubsystem.getSubsystemName());
        node.setProperty("commandPrefixName", mqSubsystem.getCommandPrefixName());
        node.setProperty("controllingAddressSpace", mqSubsystem.getControllingAddressSpace());
        node.setProperty("versionString", mqSubsystem.getVersionString());

        return node.save();
    }

    public OVertex createOrUpdateMQAliasQueue(MQAliasQueue mqAliasQueue) {
        String classType = stringUtils.getClassTypeByDlaId(mqAliasQueue.getDlaId());
        OVertex node = getNodeById(classType, mqAliasQueue.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqAliasQueue.getId());
            node.setProperty("dlaId", mqAliasQueue.getDlaId());
            node.setProperty("prefixId", mqAliasQueue.getPrefixId());
            node.setProperty("source", mqAliasQueue.getDataSource());
        }

        node.setProperty("scanDate", mqAliasQueue.getScanDate());
        node.setProperty("displayName", mqAliasQueue.getName());
        node.setProperty("label", mqAliasQueue.getLabel());
        node.setProperty("name", mqAliasQueue.getName());
        node.setProperty("description", mqAliasQueue.getDescription());
        node.setProperty("defaultPersistence", mqAliasQueue.getDefaultPersistence());
        node.setProperty("get", mqAliasQueue.getGet());
        node.setProperty("put", mqAliasQueue.getPut());
        node.setProperty("qsgdisp", mqAliasQueue.getQsgdisp());
        node.setProperty("targetQueue", mqAliasQueue.getTargetQueue());

        return node.save();
    }

    public OVertex createOrUpdateMQAuthInfo(MQAuthInfo mqAuthInfo) {
        String classType = stringUtils.getClassTypeByDlaId(mqAuthInfo.getDlaId());
        OVertex node = getNodeById(classType, mqAuthInfo.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqAuthInfo.getId());
            node.setProperty("dlaId", mqAuthInfo.getDlaId());
            node.setProperty("prefixId", mqAuthInfo.getPrefixId());
            node.setProperty("source", mqAuthInfo.getDataSource());
        }

        node.setProperty("scanDate", mqAuthInfo.getScanDate());
        node.setProperty("displayName", mqAuthInfo.getName());
        node.setProperty("label", mqAuthInfo.getLabel());
        node.setProperty("name", mqAuthInfo.getName());
        node.setProperty("type", mqAuthInfo.getType());
        node.setProperty("userName", mqAuthInfo.getUserName());
        node.setProperty("ldapServerName", mqAuthInfo.getLdapServerName());
        node.setProperty("queueManager", mqAuthInfo.getQueueManager());

        return node.save();
    }

    public OVertex createOrUpdateMQBufferPool(MQBufferPool mqBufferPool) {
        String classType = stringUtils.getClassTypeByDlaId(mqBufferPool.getDlaId());
        OVertex node = getNodeById(classType, mqBufferPool.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqBufferPool.getId());
            node.setProperty("dlaId", mqBufferPool.getDlaId());
            node.setProperty("prefixId", mqBufferPool.getPrefixId());
            node.setProperty("source", mqBufferPool.getDataSource());
        }

        node.setProperty("scanDate", mqBufferPool.getScanDate());
        node.setProperty("displayName", mqBufferPool.getLabel());
        node.setProperty("label", mqBufferPool.getLabel());
        node.setProperty("idSequence", mqBufferPool.getIdSequence());
        node.setProperty("number", mqBufferPool.getNumber());
        node.setProperty("name", mqBufferPool.getLabel());

        return node.save();
    }

    public OVertex createOrUpdateMQClientConnectionChannel(MQClientConnectionChannel mqClientConnectionChannel) {
        String dlaId = mqClientConnectionChannel.getDlaId();
        String classType = stringUtils.getClassTypeByDlaId(dlaId);
        OVertex node = getNodeById(classType, mqClientConnectionChannel.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqClientConnectionChannel.getId());
            node.setProperty("dlaId", mqClientConnectionChannel.getDlaId());
            node.setProperty("prefixId", mqClientConnectionChannel.getPrefixId());
            node.setProperty("source", mqClientConnectionChannel.getDataSource());
        }

        node.setProperty("scanDate", mqClientConnectionChannel.getScanDate());
        node.setProperty("displayName", mqClientConnectionChannel.getName());
        node.setProperty("label", mqClientConnectionChannel.getLabel());
        node.setProperty("name", mqClientConnectionChannel.getName());
        node.setProperty("queueSharingGroupDisposition", mqClientConnectionChannel.getQueueSharingGroupDisposition());
        node.setProperty("headerCompression", mqClientConnectionChannel.getHeaderCompression());
        node.setProperty("messageCompression", mqClientConnectionChannel.getMessageCompression());
        node.setProperty("heartbeatInterval", mqClientConnectionChannel.getHeartbeatInterval());
        node.setProperty("keepAliveInterval", mqClientConnectionChannel.getKeepAliveInterval());
        node.setProperty("maxMessageLength", mqClientConnectionChannel.getMaxMessageLength());

        return node.save();
    }

    public OVertex createOrUpdateMQClusterReceiverChannel(MQClusterReceiverChannel mqClusterReceiverChannel) {
        String classType = stringUtils.getClassTypeByDlaId(mqClusterReceiverChannel.getDlaId());
        OVertex node = getNodeById(classType, mqClusterReceiverChannel.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqClusterReceiverChannel.getId());
            node.setProperty("dlaId", mqClusterReceiverChannel.getDlaId());
            node.setProperty("prefixId", mqClusterReceiverChannel.getPrefixId());
            node.setProperty("source", mqClusterReceiverChannel.getDataSource());
        }

        node.setProperty("scanDate", mqClusterReceiverChannel.getScanDate());
        node.setProperty("displayName", mqClusterReceiverChannel.getName());
        node.setProperty("label", mqClusterReceiverChannel.getLabel());
        node.setProperty("name", mqClusterReceiverChannel.getName());
        node.setProperty("queueSharingGroupDisposition", mqClusterReceiverChannel.getQueueSharingGroupDisposition());
        node.setProperty("connectionName", mqClusterReceiverChannel.getConnectionName());
        node.setProperty("clwlChannelWeight", mqClusterReceiverChannel.getClwlChannelWeight());
        node.setProperty("clwlChannelPriority", mqClusterReceiverChannel.getClwlChannelPriority());
        node.setProperty("clwlChannelRank", mqClusterReceiverChannel.getClwlChannelRank());
        node.setProperty("dataConversion", mqClusterReceiverChannel.getDataConversion());
        node.setProperty("longRetryTimer", mqClusterReceiverChannel.getLongRetryTimer());
        node.setProperty("longRetryCount", mqClusterReceiverChannel.getLongRetryCount());
        node.setProperty("messageRetryCount", mqClusterReceiverChannel.getMessageRetryCount());
        node.setProperty("messageRetryInterval", mqClusterReceiverChannel.getMessageRetryInterval());
        node.setProperty("putAuthority", mqClusterReceiverChannel.getPutAuthority());
        node.setProperty("shortRetryTimer", mqClusterReceiverChannel.getShortRetryTimer());
        node.setProperty("shortRetryCount", mqClusterReceiverChannel.getShortRetryCount());
        node.setProperty("batchSize", mqClusterReceiverChannel.getBatchSize());
        node.setProperty("batchInterval", mqClusterReceiverChannel.getBatchInterval());
        node.setProperty("batchHeartbeatInterval", mqClusterReceiverChannel.getBatchHeartbeatInterval());
        node.setProperty("headerCompression", mqClusterReceiverChannel.getHeaderCompression());
        node.setProperty("messageCompression", mqClusterReceiverChannel.getMessageCompression());
        node.setProperty("disconnectInterval", mqClusterReceiverChannel.getDisconnectInterval());
        node.setProperty("heartbeatInterval", mqClusterReceiverChannel.getHeartbeatInterval());
        node.setProperty("keepAliveInterval", mqClusterReceiverChannel.getKeepAliveInterval());
        node.setProperty("maxMessageLength", mqClusterReceiverChannel.getMaxMessageLength());
        node.setProperty("mcaType", mqClusterReceiverChannel.getMcaType());
        node.setProperty("nonPersistentMessageSpeed", mqClusterReceiverChannel.getNonPersistentMessageSpeed());
        node.setProperty("sslClientAuthentication", mqClusterReceiverChannel.getSslClientAuthentication());

        return node.save();
    }

    public OVertex createOrUpdateMQClusterSenderChannel(MQClusterSenderChannel mqClusterSenderChannel) {
        String classType = stringUtils.getClassTypeByDlaId(mqClusterSenderChannel.getDlaId());
        OVertex node = getNodeById(classType, mqClusterSenderChannel.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqClusterSenderChannel.getId());
            node.setProperty("dlaId", mqClusterSenderChannel.getDlaId());
            node.setProperty("prefixId", mqClusterSenderChannel.getPrefixId());
            node.setProperty("source", mqClusterSenderChannel.getDataSource());
        }

        node.setProperty("scanDate", mqClusterSenderChannel.getScanDate());
        node.setProperty("displayName", mqClusterSenderChannel.getName());
        node.setProperty("label", mqClusterSenderChannel.getLabel());
        node.setProperty("name", mqClusterSenderChannel.getName());
        node.setProperty("queueSharingGroupDisposition", mqClusterSenderChannel.getQueueSharingGroupDisposition());
        node.setProperty("connectionName", mqClusterSenderChannel.getConnectionName());
        node.setProperty("clwlChannelWeight", mqClusterSenderChannel.getClwlChannelWeight());
        node.setProperty("clwlChannelPriority", mqClusterSenderChannel.getClwlChannelPriority());
        node.setProperty("clwlChannelRank", mqClusterSenderChannel.getClwlChannelRank());
        node.setProperty("dataConversion", mqClusterSenderChannel.getDataConversion());
        node.setProperty("longRetryTimer", mqClusterSenderChannel.getLongRetryTimer());
        node.setProperty("longRetryCount", mqClusterSenderChannel.getLongRetryCount());
        node.setProperty("shortRetryTimer", mqClusterSenderChannel.getShortRetryTimer());
        node.setProperty("shortRetryCount", mqClusterSenderChannel.getShortRetryCount());
        node.setProperty("batchSize", mqClusterSenderChannel.getBatchSize());
        node.setProperty("batchInterval", mqClusterSenderChannel.getBatchInterval());
        node.setProperty("batchHeartbeatInterval", mqClusterSenderChannel.getBatchHeartbeatInterval());
        node.setProperty("headerCompression", mqClusterSenderChannel.getHeaderCompression());
        node.setProperty("messageCompression", mqClusterSenderChannel.getMessageCompression());
        node.setProperty("disconnectInterval", mqClusterSenderChannel.getDisconnectInterval());
        node.setProperty("heartbeatInterval", mqClusterSenderChannel.getHeartbeatInterval());
        node.setProperty("keepAliveInterval", mqClusterSenderChannel.getKeepAliveInterval());
        node.setProperty("maxMessageLength", mqClusterSenderChannel.getMaxMessageLength());
        node.setProperty("nonPersistentMessageSpeed", mqClusterSenderChannel.getNonPersistentMessageSpeed());

        return node.save();
    }

    public OVertex createOrUpdateMQLocalQueue(MQLocalQueue mqLocalQueue) {
        String classType = stringUtils.getClassTypeByDlaId(mqLocalQueue.getDlaId());
        OVertex node = getNodeById(classType,mqLocalQueue.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqLocalQueue.getId());
            node.setProperty("dlaId", mqLocalQueue.getDlaId());
            node.setProperty("prefixId", mqLocalQueue.getPrefixId());
            node.setProperty("source", mqLocalQueue.getDataSource());
        }

        node.setProperty("scanDate", mqLocalQueue.getScanDate());
        node.setProperty("displayName", mqLocalQueue.getName());
        node.setProperty("label", mqLocalQueue.getLabel());
        node.setProperty("name", mqLocalQueue.getName());
        node.setProperty("get", mqLocalQueue.getGet());
        node.setProperty("put", mqLocalQueue.getPut());
        node.setProperty("definitionType", mqLocalQueue.getDefinitionType());
        node.setProperty("transmissionUsage", mqLocalQueue.getTransmissionUsage());
        node.setProperty("maxMessageLength", mqLocalQueue.getMaxMessageLength());
        node.setProperty("maxQueueDepth", mqLocalQueue.getMaxQueueDepth());
        node.setProperty("triggerControl", mqLocalQueue.getTriggerControl());
        node.setProperty("triggerData", mqLocalQueue.getTriggerData());
        node.setProperty("triggerDepth", mqLocalQueue.getTriggerDepth());
        node.setProperty("triggerType", mqLocalQueue.getTriggerType());
        node.setProperty("defaultPersistence", mqLocalQueue.getDefaultPersistence());
        node.setProperty("qsgdisp", mqLocalQueue.getQsgdisp());

        return node.save();
    }

    public OVertex createOrUpdateMQModelQueue(MQModelQueue mqModelQueue) {
        String classType = stringUtils.getClassTypeByDlaId(mqModelQueue.getDlaId());
        OVertex node = getNodeById(classType, mqModelQueue.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqModelQueue.getId());
            node.setProperty("dlaId", mqModelQueue.getDlaId());
            node.setProperty("prefixId", mqModelQueue.getPrefixId());
            node.setProperty("source", mqModelQueue.getDataSource());
        }

        node.setProperty("scanDate", mqModelQueue.getScanDate());
        node.setProperty("displayName", mqModelQueue.getName());
        node.setProperty("label", mqModelQueue.getLabel());
        node.setProperty("name", mqModelQueue.getName());
        node.setProperty("initiationQueue", mqModelQueue.getInitiationQueue());
        node.setProperty("description", mqModelQueue.getDescription());
        node.setProperty("get", mqModelQueue.getGet());
        node.setProperty("put", mqModelQueue.getPut());
        node.setProperty("definitionType", mqModelQueue.getDefinitionType());
        node.setProperty("transmissionUsage", mqModelQueue.getTransmissionUsage());
        node.setProperty("triggerControl", mqModelQueue.getTriggerControl());
        node.setProperty("triggerData", mqModelQueue.getTriggerData());
        node.setProperty("triggerDepth", mqModelQueue.getTriggerDepth());
        node.setProperty("triggerType", mqModelQueue.getTriggerType());
        node.setProperty("defaultPersistence", mqModelQueue.getDefaultPersistence());
        node.setProperty("qsgdisp", mqModelQueue.getQsgdisp());

        return node.save();
    }

    public OVertex createOrUpdateMQReceiverChannel(MQReceiverChannel mqReceiverChannel) {
        String dlaId = mqReceiverChannel.getDlaId();
        String classType = stringUtils.getClassTypeByDlaId(dlaId);
        OVertex node = getNodeById(classType, mqReceiverChannel.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqReceiverChannel.getId());
            node.setProperty("dlaId", mqReceiverChannel.getDlaId());
            node.setProperty("prefixId", mqReceiverChannel.getPrefixId());
            node.setProperty("source", mqReceiverChannel.getDataSource());
        }

        node.setProperty("scanDate", mqReceiverChannel.getScanDate());
        node.setProperty("displayName", mqReceiverChannel.getName());
        node.setProperty("label", mqReceiverChannel.getLabel());
        node.setProperty("name", mqReceiverChannel.getName());
        node.setProperty("queueSharingGroupDisposition", mqReceiverChannel.getQueueSharingGroupDisposition());
        node.setProperty("messageRetryCount", mqReceiverChannel.getMessageRetryCount());
        node.setProperty("messageRetryInterval", mqReceiverChannel.getMessageRetryInterval());
        node.setProperty("putAuthority", mqReceiverChannel.getPutAuthority());
        node.setProperty("batchSize", mqReceiverChannel.getBatchSize());
        node.setProperty("headerCompression", mqReceiverChannel.getHeaderCompression());
        node.setProperty("messageCompression", mqReceiverChannel.getMessageCompression());
        node.setProperty("heartbeatInterval", mqReceiverChannel.getHeartbeatInterval());
        node.setProperty("keepAliveInterval", mqReceiverChannel.getKeepAliveInterval());
        node.setProperty("maxMessageLength", mqReceiverChannel.getMaxMessageLength());
        node.setProperty("nonPersistentMessageSpeed", mqReceiverChannel.getNonPersistentMessageSpeed());
        node.setProperty("sslClientAuthentication", mqReceiverChannel.getSslClientAuthentication());
        node.setProperty("sslCipherSpecification", mqReceiverChannel.getSslCipherSpecification());

        return node.save();
    }

    public OVertex createOrUpdateMQRemoteQueue(MQRemoteQueue mqRemoteQueue) {
        String dlaId = mqRemoteQueue.getDlaId();
        String classType = stringUtils.getClassTypeByDlaId(mqRemoteQueue.getDlaId());
        OVertex node = getNodeById(classType, mqRemoteQueue.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqRemoteQueue.getId());
            node.setProperty("dlaId", mqRemoteQueue.getDlaId());
            node.setProperty("prefixId", mqRemoteQueue.getPrefixId());
            node.setProperty("source", mqRemoteQueue.getDataSource());
        }

        node.setProperty("scanDate", mqRemoteQueue.getScanDate());
        node.setProperty("displayName", mqRemoteQueue.getName());
        node.setProperty("label", mqRemoteQueue.getLabel());
        node.setProperty("name", mqRemoteQueue.getName());
        node.setProperty("put", mqRemoteQueue.getPut());
        node.setProperty("qsgdisp", mqRemoteQueue.getQsgdisp());
        node.setProperty("remoteName", mqRemoteQueue.getRemoteName());
        node.setProperty("remoteQueueMgrName", mqRemoteQueue.getRemoteQueueMgrName());
        node.setProperty("defaultPersistence", mqRemoteQueue.getDefaultPersistence());

        return node.save();
    }

    public OVertex createOrUpdateMQSenderChannel(MQSenderChannel mqSenderChannel) {
        String classType = stringUtils.getClassTypeByDlaId(mqSenderChannel.getDlaId());
        OVertex node = getNodeById(classType, mqSenderChannel.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqSenderChannel.getId());
            node.setProperty("dlaId", mqSenderChannel.getDlaId());
            node.setProperty("prefixId", mqSenderChannel.getPrefixId());
            node.setProperty("source", mqSenderChannel.getDataSource());
        }

        node.setProperty("scanDate", mqSenderChannel.getScanDate());
        node.setProperty("displayName", mqSenderChannel.getName());
        node.setProperty("label", mqSenderChannel.getLabel());
        node.setProperty("name", mqSenderChannel.getName());
        node.setProperty("queueSharingGroupDisposition", mqSenderChannel.getQueueSharingGroupDisposition());
        node.setProperty("connectionName", mqSenderChannel.getConnectionName());
        node.setProperty("dataConversion", mqSenderChannel.getDataConversion());
        node.setProperty("longRetryTimer", mqSenderChannel.getLongRetryTimer());
        node.setProperty("longRetryCount", mqSenderChannel.getLongRetryCount());
        node.setProperty("shortRetryTimer", mqSenderChannel.getShortRetryTimer());
        node.setProperty("shortRetryCount", mqSenderChannel.getShortRetryCount());
        node.setProperty("batchSize", mqSenderChannel.getBatchSize());
        node.setProperty("batchInterval", mqSenderChannel.getBatchInterval());
        node.setProperty("batchHeartbeatInterval", mqSenderChannel.getBatchHeartbeatInterval());
        node.setProperty("headerCompression", mqSenderChannel.getHeaderCompression());
        node.setProperty("messageCompression", mqSenderChannel.getMessageCompression());
        node.setProperty("disconnectInterval", mqSenderChannel.getDisconnectInterval());
        node.setProperty("heartbeatInterval", mqSenderChannel.getHeartbeatInterval());
        node.setProperty("keepAliveInterval", mqSenderChannel.getKeepAliveInterval());
        node.setProperty("maxMessageLength", mqSenderChannel.getMaxMessageLength());
        node.setProperty("nonPersistentMessageSpeed", mqSenderChannel.getNonPersistentMessageSpeed());
        node.setProperty("transmissionQueue", mqSenderChannel.getTransmissionQueue());

        return node.save();
    }

    public OVertex createOrUpdateMQServerConnectionChannel(MQServerConnectionChannel mqServerConnectionChannel) {
        String classType = stringUtils.getClassTypeByDlaId(mqServerConnectionChannel.getDlaId());
        OVertex node = getNodeById(classType, mqServerConnectionChannel.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqServerConnectionChannel.getId());
            node.setProperty("dlaId", mqServerConnectionChannel.getDlaId());
            node.setProperty("prefixId", mqServerConnectionChannel.getPrefixId());
            node.setProperty("source", mqServerConnectionChannel.getDataSource());
        }

        node.setProperty("scanDate", mqServerConnectionChannel.getScanDate());
        node.setProperty("displayName", mqServerConnectionChannel.getName());
        node.setProperty("label", mqServerConnectionChannel.getLabel());
        node.setProperty("name", mqServerConnectionChannel.getName());
        node.setProperty("queueSharingGroupDisposition", mqServerConnectionChannel.getQueueSharingGroupDisposition());
        node.setProperty("headerCompression", mqServerConnectionChannel.getHeaderCompression());
        node.setProperty("messageCompression", mqServerConnectionChannel.getMessageCompression());
        node.setProperty("disconnectInterval", mqServerConnectionChannel.getDisconnectInterval());
        node.setProperty("heartbeatInterval", mqServerConnectionChannel.getHeartbeatInterval());
        node.setProperty("keepAliveInterval", mqServerConnectionChannel.getKeepAliveInterval());
        node.setProperty("maxMessageLength", mqServerConnectionChannel.getMaxMessageLength());
        node.setProperty("sslClientAuthentication", mqServerConnectionChannel.getSslClientAuthentication());
        node.setProperty("description", mqServerConnectionChannel.getSslClientAuthentication());

        return node.save();
    }

    public OVertex createOrUpdateIMSSubystem(IMSSubsystem imsSubsystem) {
        String classType = stringUtils.getClassTypeByDlaId(imsSubsystem.getDlaId());
        OVertex node = getNodeById(classType, imsSubsystem.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", imsSubsystem.getId());
            node.setProperty("dlaId", imsSubsystem.getDlaId());
            node.setProperty("prefixId", imsSubsystem.getPrefixId());
            node.setProperty("source", imsSubsystem.getDataSource());
        }

        node.setProperty("scanDate", imsSubsystem.getScanDate());
        node.setProperty("displayName", imsSubsystem.getSubsystemName());
        node.setProperty("label", imsSubsystem.getLabel());
        node.setProperty("keyName", imsSubsystem.getKeyName());
        node.setProperty("name", imsSubsystem.getSubsystemName());
        node.setProperty("versionString", imsSubsystem.getVersionString());
        node.setProperty("commandPrefixName", imsSubsystem.getCommandPrefixName());
        node.setProperty("controllingAddressSpace", imsSubsystem.getControllingAddressSpace());
        node.setProperty("IMSSubsysType", imsSubsystem.getIMSSubsysType());
        node.setProperty("IMSPlexGroupName", imsSubsystem.getIMSPlexGroupName());
        node.setProperty("IRLMGroupName", imsSubsystem.getIRLMGroupName());
        node.setProperty("CQSGroupName", imsSubsystem.getCQSGroupName());
        node.setProperty("databasesChecksum", imsSubsystem.getDatabasesChecksum());
        node.setProperty("programsChecksum", imsSubsystem.getProgramsChecksum());
        node.setProperty("transactionsChecksum", imsSubsystem.getTransactionsChecksum());

        return node.save();
    }

    public OVertex createOrUpdateIMSDatabase(IMSDatabase imsDatabase) {
        String classType = stringUtils.getClassTypeByDlaId(imsDatabase.getDlaId());
        OVertex node = getNodeById(classType, imsDatabase.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", imsDatabase.getId());
            node.setProperty("dlaId", imsDatabase.getDlaId());
            node.setProperty("prefixId", imsDatabase.getPrefixId());
            node.setProperty("source", imsDatabase.getDataSource());
        }

        node.setProperty("scanDate", imsDatabase.getScanDate());
        node.setProperty("displayName", imsDatabase.getName());
        node.setProperty("label", imsDatabase.getLabel());
        node.setProperty("name", imsDatabase.getName());
        node.setProperty("IMSDatabaseType", imsDatabase.getIMSDatabaseType());

        return node.save();
    }

    // transfer to Program
    public OVertex createOrUpdateIMSProgram(IMSProgram imsProgram) {
        String classType = stringUtils.getClassTypeByDlaId(imsProgram.getDlaId());
        OVertex result = getNodeById(classType, imsProgram.getId());
        if (result == null) {
            result = db.newVertex(classType);
            result.setProperty("id", imsProgram.getId());
            result.setProperty("dlaId", imsProgram.getDlaId());
            result.setProperty("prefixId", imsProgram.getPrefixId());
            result.setProperty("source", imsProgram.getDataSource());
        }

        result.setProperty("scanDate", imsProgram.getScanDate());
        result.setProperty("displayName", imsProgram.getName());
        result.setProperty("label", imsProgram.getLabel());
        result.setProperty("name", imsProgram.getName());
        result.setProperty("type", "IMS");

        return result.save();
    }
    
    public OVertex createOrUpdateIMSTransaction(IMSTransaction imsTransaction) {
        String classType = stringUtils.getClassTypeByDlaId(imsTransaction.getDlaId());
        OVertex node = getNodeById(classType, imsTransaction.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", imsTransaction.getId());
            node.setProperty("dlaId", imsTransaction.getDlaId());
            node.setProperty("prefixId", imsTransaction.getPrefixId());
            node.setProperty("source", imsTransaction.getDataSource());
        }

        node.setProperty("scanDate", imsTransaction.getScanDate());
        node.setProperty("displayName", imsTransaction.getName());
        node.setProperty("label", imsTransaction.getLabel());
        node.setProperty("name", imsTransaction.getName());

        return node.save();
    }

    public OVertex createOrUpdateIMSSysplexGroup(IMSSysplexGroup imsSysplexGroup) {
        String classType = stringUtils.getClassTypeByDlaId(imsSysplexGroup.getDlaId());
        OVertex node = getNodeById(classType, imsSysplexGroup.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", imsSysplexGroup.getId());
            node.setProperty("dlaId", imsSysplexGroup.getDlaId());
            node.setProperty("prefixId", imsSysplexGroup.getPrefixId());
            node.setProperty("source", imsSysplexGroup.getDataSource());
        }

        node.setProperty("scanDate", imsSysplexGroup.getScanDate());
        node.setProperty("label", imsSysplexGroup.getLabel());
        node.setProperty("name", imsSysplexGroup.getName());
        node.setProperty("groupFunction", imsSysplexGroup.getGroupFunction());

        return node.save();
    }

    public OVertex createOrUpdateMQQueueSharingGroup(MQQueueSharingGroup mqQueueSharingGroup) {
        String classType = stringUtils.getClassTypeByDlaId(mqQueueSharingGroup.getDlaId());
        OVertex node = getNodeById(classType, mqQueueSharingGroup.getId());
        if (node == null) {
            node = db.newVertex(classType);
            node.setProperty("id", mqQueueSharingGroup.getId());
            node.setProperty("dlaId", mqQueueSharingGroup.getDlaId());
            node.setProperty("prefixId", mqQueueSharingGroup.getPrefixId());
            node.setProperty("source", mqQueueSharingGroup.getDataSource());
        }

        node.setProperty("scanDate", mqQueueSharingGroup.getScanDate());
        node.setProperty("label", mqQueueSharingGroup.getLabel());
        node.setProperty("name", mqQueueSharingGroup.getName());
        node.setProperty("groupFunction", mqQueueSharingGroup.getGroupFunction());

        return node.save();
    }
}
