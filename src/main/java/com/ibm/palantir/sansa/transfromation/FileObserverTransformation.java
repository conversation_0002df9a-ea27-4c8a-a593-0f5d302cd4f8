/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021, 2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.transfromation;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

import com.bazaarvoice.jolt.Chainr;
import com.bazaarvoice.jolt.JsonUtils;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;

public class FileObserverTransformation {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = FileObserverTransformation.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    public static final long LIMIT = 5000000;
    public static final String filepath = "./asmFileObserver";
    public static final String specPath = "./spec/";
    public static final String filename = "asmUploadFile";
    private ArrayList<String> fileList;

    public FileObserverTransformation() {
        fileList = new ArrayList<>();
        File directory = new File(filepath);
        if (!directory.isDirectory()) {
            directory.mkdir();
        }
    }

    public ArrayList<String> getFileList() {
        return this.fileList;
    }

    private File getFilename() {
        if (!fileList.isEmpty()) {
            String fileName = fileList.get(fileList.size() - 1);
            File file = new File(fileName);
            if (file.isFile() && file.length() < LIMIT) {
                return file;
            }
        }
        String fileName = filepath + "/" + filename + "." + (fileList.size() + 1) + ".txt";
        File file = new File(fileName);
        if (file.isFile()) {
            file.delete();
        }
        fileList.add(fileName);
        return file;
    }

    /**
     * export file
     *
     * @param dataList data
     * @return
     */
    public boolean export(List<String> dataList) {
        if (dataList.size() <= 500) {
            return exportBatch(dataList);
        } else {
            int offset;
            for (offset = 0; dataList.size() - offset > 500; offset += 500) {
                List<String> subList = dataList.subList(offset, offset + 500);
                exportBatch(subList);
            }
            List<String> subList = dataList.subList(offset, dataList.size());
            return exportBatch(subList);
        }
    }

    public boolean exportBatch(List<String> dataList) {

        File file = getFilename();

        String PluginIOCode = ErrorCode.PluginIOError.getCodeStr();
        String PluginIOMsg = MessageFormat.format(MsgTemp.get(PluginIOCode), "Sansa", "FileObserverTransformation");

        boolean isSuccess;
        FileOutputStream out = null;
        OutputStreamWriter osw = null;
        BufferedWriter bw = null;
        try {
            out = new FileOutputStream(file, true);
            osw = new OutputStreamWriter(out);
            bw = new BufferedWriter(osw);

            if (dataList != null && !dataList.isEmpty()) {
                for (String data : dataList) {
                    bw.append(data).append("\r\n");
                }
            }
            isSuccess = true;
        } catch (Exception e) {
            String errCode = ErrorCode.PluginError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "FileObserverTransformation",
                    file.getName());
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportBatch", msg);
            isSuccess = false;
        } finally {
            if (bw != null) {
                try {
                    bw.close();
                    bw = null;
                } catch (IOException e) {
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportBatch", e.toString());
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportBatch", PluginIOMsg);
                }
            }
            if (osw != null) {
                try {
                    osw.close();
                    osw = null;
                } catch (IOException e) {
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportBatch", e.toString());
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportBatch", PluginIOMsg);
                }
            }
            if (out != null) {
                try {
                    out.close();
                    out = null;
                } catch (IOException e) {
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportBatch", e.toString());
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportBatch", PluginIOMsg);
                }
            }
        }
        return isSuccess;
    }

    public void exportFile(JsonArray jsonArray, String type) {
        List<String> csvString = new ArrayList<>();
        for (JsonElement jsonObject : jsonArray) {
            StringBuilder sbValue = new StringBuilder();
            sbValue.append("V:");
            sbValue.append(convertJSON(jsonObject, type));
            csvString.add(sbValue.toString());
        }

        export(csvString);
    }

    public void exportRelationFile(JsonArray jsonArray) {
        List<String> csvString = new ArrayList<>();
        for (JsonElement jsonObject : jsonArray) {
            StringBuilder sbValue = new StringBuilder();
            sbValue.append("E:");
            sbValue.append(jsonObject.toString());
            csvString.add(sbValue.toString());
        }
        export(csvString);
    }

    private String convertJSON(JsonElement jsonObject, String type) {
        String filename = specPath + type + ".json";
        File file = new File(filename);
        if (file.exists() && file.isFile()) {
            List<Object> chainrSpecJSON = JsonUtils.filepathToList(filename);
            Chainr chainr = Chainr.fromSpec(chainrSpecJSON);
            Object transformedOutput = chainr.transform(JsonUtils.jsonToObject(jsonObject.toString()));
            Gson gson = new Gson();
            return gson.toJson(transformedOutput);
        } else {
            return jsonObject.toString();
        }
    }

}
