/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.extractor;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.text.MessageFormat;

import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.jaxb.Idml;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.exception.ServiceExceptionStore;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;

public class IdmlExtractor {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = IdmlExtractor.class.getSimpleName();
    private static final String REPONAME = "Sansa";
    private static final ServiceExceptionStore errorStore = ServiceExceptionStore.getInstance();

    public static MultipartFile stringToMultipartFile(String XMLContent, String fileName, String contentType) throws IOException {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "stringToMultipartFile", "Running stringToMultipartFile");
        byte[] bytes = XMLContent.getBytes();
        return new MockMultipartFile(fileName, fileName, contentType, new ByteArrayInputStream(bytes));
    }

    public Idml extractIdml(MultipartFile file, String filePath) throws Exception {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "extractIdml", "Running extractIdml");
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(Idml.class);
            Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
            String XMLContent = new String(file.getBytes());
            if(XMLContent.contains("&num")){
                XMLContent = XMLContent.replace("&num", "&amp;num");
                file = stringToMultipartFile(XMLContent, file.getName(), file.getContentType());
            }
            Idml idml = (Idml) jaxbUnmarshaller.unmarshal(file.getInputStream());
            file.getInputStream().close();
            file = null;
            LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "extractIdml", "JAXBContext newInstance and createUnmarshaller");
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "extractIdml", "extracted the Idml successfully.");
            return idml;
        } catch (JAXBException e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "extractIdml", e.toString());
            String errCode = ErrorCode.PluginJAXBError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "extractIdml");
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "extractIdml", "Error: {}", msg);
            throw new ServiceException(errCode, "Failed to extract the file: " + filePath + "\n" + e.getLinkedException().toString() , REPONAME, CLASSNAME, "extractIdml");
        } catch (IOException e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "extractIdml", e.toString());
            String errCode = ErrorCode.PluginIOError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "extractIdml");
            ServiceException se = new ServiceException(errCode, msg , REPONAME, CLASSNAME, "extractIdml");
            throw se;
        }
    }
}
