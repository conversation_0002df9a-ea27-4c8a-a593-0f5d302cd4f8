/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.extractor;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ibm.palantir.catelyn.util.HttpUtils;

public class TopologyExtractor {

    private static final String authorizePath = "/authorize";

    private static final String resourcePath = "/resources";

    public JsonArray getIDList(String url, Map<String, String> headers) throws Exception {
        String address = url + resourcePath;
        Map<String, String> parameters = new HashMap<>();
        parameters.put("_filter", "entityTypes=service");
        parameters.put("_field", "name");
        parameters.put("_limit", "100");
        parameters.put("_include_global_resources", "false");
        parameters.put("_include_count", "false");
        parameters.put("_include_status", "false");
        parameters.put("_include_status_severity", "false");
        parameters.put("_include_metadata", "false");
        parameters.put("_return_composites", "false");

        JsonArray resultArray = new JsonArray();
        int number = 0;
        while (true) {
            String result = HttpUtils.get(address, headers, parameters, true);
            JsonElement jsonObject = JsonParser.parseString(result);
            if (jsonObject.isJsonObject() && jsonObject.getAsJsonObject().has("_items")) {
                JsonArray jsonArray = jsonObject.getAsJsonObject().getAsJsonArray("_items");
                resultArray.addAll(jsonArray);
                if (jsonArray.size() < 100) {
                    break;
                }
                number += 100;
                parameters.put("_offset", number + "");
            }
        }
        return resultArray;
    }

    public JsonObject getSource(String url, Map<String, String> headers, String id) throws Exception {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("_follow_composites", "false");
        parameters.put("_deleted", "false");
        parameters.put("_include_status", "false");
        parameters.put("_include_status_severity", "false");
        parameters.put("_include_metadata", "false");
        String address = url + resourcePath + "/" + id;
        String result = HttpUtils.get(address, headers, parameters, true);
        JsonElement jsonObject = JsonParser.parseString(result);
        return jsonObject.getAsJsonObject();
    }

    public String getToken(String url, String username, String api_key) throws Exception {
        String address = url + authorizePath;
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json");
        headers.put("Content-Type", "application/json");
        String jsonObject = "{\"username\": \"" + username + "\", \"api_key\": \"" + api_key + "\"}";
        String result = HttpUtils.post(address, headers, null, jsonObject, true);
        return "Bearer " + JsonParser.parseString(result).getAsJsonObject().get("token").toString();
    }

    public String getBasic(String username, String password) throws Exception {
        String authString = username + ":" + password;
        byte[] authEncBytes = Base64.getEncoder().encode(authString.getBytes(StandardCharsets.UTF_8));

        String authStringEnc = new String(authEncBytes);
        return "Basic " + authStringEnc;

    }
}
