/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.extractor;

import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.jaxb.*;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.exception.ServiceExceptionStore;

import jakarta.xml.bind.JAXBElement;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Field;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

public class ElementExtractor {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = ElementExtractor.class.getSimpleName();
    private static final String REPONAME = "Sansa";
    private static final ServiceExceptionStore errorStore = ServiceExceptionStore.getInstance();

    public List<JAXBElement<?>> managedElementsAndRelationships = new ArrayList<>();
    public ProcessManagementSoftwareSystem processManagementSoftwareSystem = new ProcessManagementSoftwareSystem();
    public String createTimestamp = "";
    public Boolean isEmpty = true;
    public String filePath = "";

    public ElementExtractor() {
    }

    public ElementExtractor(String filePath, String ip, String account, String pwd) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "ElementExtractor", "Running ElementExtractor");
        IdmlExtractor idmlExtractor = new IdmlExtractor();
        GetXMLFile getXMLFile = new GetXMLFile();
        try {
            MultipartFile xml = getXMLFile.getFile(filePath, ip, account, pwd);
            Idml idml = idmlExtractor.extractIdml(xml, filePath);
            this.resolveIdml(idml);
            this.filePath = filePath;
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "ElementExtractor", e.toString());
            errorStore.addError("Failed to extract and resolve the file: " + filePath + ". Error: " + e.toString());
        }
    }

    public ElementExtractor(String filePath) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "ElementExtractor",
                "Running ElementExtractor with param - filePath: {}", filePath);
        IdmlExtractor idmlExtractor = new IdmlExtractor();
        GetXMLFile getXMLFile = new GetXMLFile();
        try {
            MultipartFile xml = getXMLFile.getFile(filePath, "", "", "");
            Idml idml = idmlExtractor.extractIdml(xml, filePath);
            this.resolveIdml(idml);
            this.filePath = filePath;
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "ElementExtractor", e.toString());
            errorStore.addError("Failed to extract and resolve the file: " + filePath + ". Error: " + e.toString());
        }
    }

    public void resolveIdml(Idml idml) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resolveIdml", "Running resolveIdml");
        if (idml != null) {
            if (idml.getSource() != null) {
                this.processManagementSoftwareSystem = idml.getSource().getProcessManagementSoftwareSystem();
            }

            if (!idml.getOperationSets().isEmpty()) {
                Create create = null;
                Object createsAndModifiesAndDeletes = null;
                try {
                    createsAndModifiesAndDeletes = idml.getOperationSets().get(0).getCreatesAndModifiesAndDeletes()
                            .get(0);
                } catch (Exception e) {
                    String errCode = ErrorCode.ValueDoNotExist.getCodeStr();
                    String msg = MessageFormat.format(MsgTemp.get(errCode), "CreatesAndModifiesAndDeletes");
                    errorStore.addError(msg);
                }
                if (createsAndModifiesAndDeletes != null) {
                    if (createsAndModifiesAndDeletes.getClass().equals(Refresh.class)) {
                        Refresh refresh = (Refresh) createsAndModifiesAndDeletes;
                        // use the refresh timestamp as scan_date
                        this.createTimestamp = refresh.getTimestamp().toString();
                        create = refresh.getCreates().get(0);
                    } else if (createsAndModifiesAndDeletes.getClass().equals(Create.class)) {
                        create = (Create) createsAndModifiesAndDeletes;
                    } else {
                        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "ElementExtractor",
                                "No 'Refresh' or 'Create' section found in the IDML book.");
                    }

                }

                if (create != null) {
                    if (create.getTimestamp() != null) {
                        this.createTimestamp = create.getTimestamp().toString();
                    }

                    if (!create.getCDMERSpecifications().isEmpty()) {
                        CDMERSpecification spec = create.getCDMERSpecifications().get(0);
                        this.managedElementsAndRelationships = spec.getManagedElementsAndRelationships();
                        this.isEmpty = false;
                    }
                }

            }
        }
    }

    public List<Object> getRelationshipElementList() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "getRelationshipElementList", "Running getRelationshipElementList");
        List<Object> objectList = new ArrayList<>();
        for (JAXBElement<?> element : managedElementsAndRelationships) {
            try {
                Class clazz = Class.forName(element.getDeclaredType().getName());
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    // Tips: only those Relationship Elements have "target" property
                    if (field.getName().equals("target")) {
                        objectList.add(element.getValue());
                        break;
                    }
                }
            } catch (ClassNotFoundException e) {
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "getRelationshipElementList", e.toString());
                String errCode = ErrorCode.PluginClassNotFoundError.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa",
                        "ElementExtractor.getRelationshipElementList");
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "getRelationshipElementList", msg);
            }
        }
        return objectList;
    }

    public Object getElementByClassName(String targetClassName) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "getElementByClassName",
                "Running getElementByClassName with param - targetClassName: {}", targetClassName);
        for (JAXBElement<?> element : managedElementsAndRelationships) {
            if (element.getDeclaredType().getName().equals(targetClassName)) {
                return element.getValue();
            }
        }
        return null;
    }

    public List<Object> getElementListByClassName(String targetClassName) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "getElementListByClassName",
                "Running getElementListByClassName with param - targetClassName: {}", targetClassName);
        List<Object> objectList = new ArrayList<>();
        for (JAXBElement<?> element : managedElementsAndRelationships) {
            if (element.getDeclaredType().getName().equals(targetClassName)) {
                objectList.add(element.getValue());
            }
        }
        return objectList;
    }

    public List<SysZOSZReportFile> getZReportFiles(String keyword) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "getZReportFiles",
                "Running getZReportFiles with param - keyword: {}", keyword);
        List<SysZOSZReportFile> zosReportFileList = new ArrayList<>();
        for (JAXBElement<?> element : managedElementsAndRelationships) {
            if (element.getDeclaredType().getName().equals(SysZOSZReportFile.class.getName())) {
                SysZOSZReportFile zosReportFile = (SysZOSZReportFile) element.getValue();
                if (zosReportFile.getId().contains(keyword)) { // only get those concerned ZReportFiles
                    zosReportFileList.add(zosReportFile);
                }
            }
        }
        return zosReportFileList;
    }
}
