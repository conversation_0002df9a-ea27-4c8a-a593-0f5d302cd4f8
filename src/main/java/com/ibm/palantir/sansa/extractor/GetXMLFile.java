/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021, 2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.extractor;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;

import org.apache.http.entity.ContentType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.jaxb.Idml;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.sansa.exception.ServiceExceptionStore;
import com.ibm.palantir.sansa.utils.FtpUtils;
import com.trilead.ssh2.Connection;

public class GetXMLFile extends AbstractPipeline {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = GetXMLFile.class.getSimpleName();
    private static final String REPONAME = "Sansa";
    private static final ServiceExceptionStore errorStore = ServiceExceptionStore.getInstance();

    public static void main(String[] args) {
        GetXMLFile getXMLFile = new GetXMLFile();

        // need add /upload-dir/cics55.xml file under ${pom.basedir}
        // String url = "http://localhost:8080/files/cics55.xml";
        // MultipartFile r = getXMLFile.getFile(url, "", "", "");

        String ip = "";
        int port = 22;
        String account = "root";
        String pwd = "";
        String filePath = "/home/<USER>/ftp/<EMAIL>";
        MultipartFile r = getXMLFile.getFile(filePath, ip, account, pwd);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "main", String.valueOf(r.getSize()));

        try {
            IdmlExtractor idmlExtractor = new IdmlExtractor();
            Idml idml = idmlExtractor.extractIdml(r, filePath);
            LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "main", idml.getSource().toString());
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "main", e.toString());
        }
    }

    public String run(String parameter) {

        return "to be called by other pipeline!";
    }

    public MultipartFile getFile(String fileUrl, String ip, String account, String pwd) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "getFile", "Running getFile with params - fileUrl: {}", fileUrl);
        if (fileUrl.isEmpty()) {
            return null;
        }

        if (account.equals("") && pwd.equals("")) {
            return readLocalFile(fileUrl);
        } else {
            return requestRemoteFile(fileUrl, ip, account, pwd);
        }
    }

    private MultipartFile readLocalFile(String fileUrl) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "readLocalFile", "Running readLocalFile with params - fileUrl: {}", fileUrl);
        String filePath;
        if (fileUrl.contains("junit")) {
            filePath = fileUrl; // just fileName
        } else { // url path or name
            // First try to find the file in the upload-dir
            filePath = "./upload-dir/" + fileUrl;
        }

        MultipartFile multipartFile = null;
        try {
            // Check if the file exists in the upload-dir
            File file = new File(filePath);

            // If not found in upload-dir and it's a .partial file, check alternative
            // locations
            if (!file.exists() && fileUrl.endsWith(".partial")) {
                // Check in the current directory
                String rootFilePath = "./" + fileUrl;
                File rootFile = new File(rootFilePath);
                if (rootFile.exists()) {
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "readLocalFile", "File {} found in root directory instead of upload-dir", fileUrl);
                    filePath = rootFilePath;
                    file = rootFile;
                } else {
                    // Also check one level up for Docker container structure
                    String parentDirPath = "../" + fileUrl;
                    File parentDirFile = new File(parentDirPath);
                    if (parentDirFile.exists()) {
                        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "readLocalFile", "File {} found in parent directory", fileUrl);
                        filePath = parentDirPath;
                        file = parentDirFile;
                    }
                }
            }

            if (!file.exists()) {
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "readLocalFile", "File not found: {}", fileUrl);
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "readLocalFile", "Attempted to find file in: ./upload-dir/{}, ./{}, and ../{}", fileUrl, fileUrl, fileUrl);
                return null;
            }

            FileInputStream fileInputStream = new FileInputStream(file);
            multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                    ContentType.APPLICATION_XML.toString(), fileInputStream);
            fileInputStream.close();
            fileInputStream = null;
            file = null;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "readLocalFile", "readLocalFile {} successfully.", filePath);
        } catch (IOException e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "readLocalFile", e.toString());
            String errCode = ErrorCode.PluginIOError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "readLocalFile");
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "readLocalFile", msg);
        }

        return multipartFile;
    }

    // File stored at remote file server
    private MultipartFile requestRemoteFile(String filePath, String ip, String account, String pwd) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "requestRemoteFile", "Running requestRemoteFile with params - filePath: {}", filePath);
        MultipartFile multipartFile = null;

        try {
            Connection conn = FtpUtils.getConn(ip, 22, account, pwd);
            InputStream is = FtpUtils.readFile(conn, filePath, ip);
            multipartFile = new MockMultipartFile(filePath, is);
            FtpUtils.closeConn(conn);
        } catch (IOException e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "requestRemoteFile", e.toString());
            String errCode = ErrorCode.PluginIOError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "requestRemoteFile");
            errorStore.addError(msg);
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "requestRemoteFile", e.toString());
            String errCode = ErrorCode.PluginError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "requestRemoteFile", filePath);
            errorStore.addError(msg);
        }

        return multipartFile;
    }
}
