/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.utils;

public class StringUtils {

    public String composeUniqueId(String str, String prefix) {
        if (prefix.isEmpty()) {
            return str;
        } else {
            return String.join("-", prefix, str);
        }
    }

    public String getClassTypeByDlaId(String dlaId) {
        String classType = getClassTypeByDlaIdIgnoreProgram(dlaId);

        // special for Program
        if (classType.equals("CICSProgram") || classType.equals("IMSProgram")) {
            classType = "Program";
        }

        return classType;
    }

    public String getClassTypeByDlaIdIgnoreProgram(String dlaId) {
        String classType;
        if (dlaId.contains("CICS_SIT_Overrides")) {
            classType = "CICSSitOverrides";
        } else if (dlaId.contains("CICS_SIT")) {
            classType = "CICSSit";
        } else if (dlaId.contains("PRSM")) {
            classType = "PRSMLpar";
        }else {
            String[] strings = dlaId.split("-");
            classType = strings[strings.length - 1];
        }
        return classType;
    }

    public String getRelationFromClassName(String className) {
        String[] strings = className.split("\\.");
        String relationName = strings[strings.length - 1];
        // Initial to lowercase
        if (Character.isLowerCase(relationName.charAt(0))) {
            return relationName;
        } else {
            return (new StringBuilder()).append(Character.toLowerCase(relationName.charAt(0))).append(relationName.substring(1)).toString();
        }
    }

    public String replaceClassTypeInDlaID(String dlaId, String classType) {
        String[] strings = dlaId.split("-");
        strings[strings.length - 1] = classType;
        String newDlaId = String.join("-", strings);
        return newDlaId;
    }
}
