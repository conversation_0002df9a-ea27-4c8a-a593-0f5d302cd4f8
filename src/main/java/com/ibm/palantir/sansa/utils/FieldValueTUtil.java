/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.utils;

import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;

import jakarta.persistence.Column;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FieldValueTUtil<T> {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = FieldValueTUtil.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    /**
     * Get field value by name
     */
    public Object getFieldValueByName(String fieldName, Object o) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter, new Class[]{});
            Object value = method.invoke(o, new Object[]{});
            return value;
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "getFieldValueByName", e.toString());
            String errCode = ErrorCode.PluginError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "getFieldValueByName", fieldName);
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "getFieldValueByName", msg);
            return null;
        }
    }

    /**
     * get all field name
     */
    public String[] getFieldName(Object o) {
        Field[] fields = o.getClass().getDeclaredFields();
        String[] fieldNames = new String[fields.length];
        for (int i = 0; i < fields.length; i++) {
            System.out.println(fields[i].getType());
            fieldNames[i] = fields[i].getName();
        }
        return fieldNames;
    }

    /**
     * get a list of map which contains type, name, value
     */
    private List getFiledsInfo(Object o) {
        Field[] fields = o.getClass().getDeclaredFields();
        String[] fieldNames = new String[fields.length];
        List list = new ArrayList();
        Map infoMap = null;
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            infoMap = new HashMap();
            infoMap.put("type", fields[i].getType().toString());
            infoMap.put("name", fields[i].getName());
            infoMap.put("value", getFieldValueByName(fields[i].getName(), o));
            list.add(infoMap);
        }
        return list;
    }


    /**
     * get a list of map which contains type, name, value
     */
    public List<Map<String,String>> getTableName(Object o, String tableName) {
        Field[] fields = o.getClass().getDeclaredFields();
        List<Map<String,String>> list = new ArrayList();
        for (int i = 0; i < fields.length; i++) {
            Map<String,String> infoMap = new HashMap<>();
            Field field = fields[i];
            Annotation annotation = field.getAnnotation(Column.class);

            if (annotation instanceof Column) {
                Column myAnnotation = (Column) annotation;
                infoMap.put("columnName", tableName + "_" + myAnnotation.name());
            } else {
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "getTableName", "Annotation is not a column");
            }

            infoMap.put("name", fields[i].getName());
            list.add(infoMap);
        }
        return list;
    }

    /**
     * Get all field values from a object, and return a object list
     */
    public Object[] getFiledValues(Object o) {
        String[] fieldNames = this.getFieldName(o);
        Object[] value = new Object[fieldNames.length];
        for (int i = 0; i < fieldNames.length; i++) {
            value[i] = this.getFieldValueByName(fieldNames[i], o);
        }
        return value;
    }

}
