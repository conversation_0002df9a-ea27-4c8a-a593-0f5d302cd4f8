/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021, 2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.utils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.lang.annotation.Annotation;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;

import jakarta.persistence.Table;

public class CSVUtil<T> {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = CSVUtil.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    /**
     * export csv file
     *
     * @param file     file path and file name, if not exist, create it
     * @param dataList data
     * @return
     */
    public static boolean exportCsv(File file, List<String> dataList) {

        String PluginIOCode = ErrorCode.PluginIOError.getCodeStr();
        String PluginIOMsg = MessageFormat.format(MsgTemp.get(PluginIOCode), "Sansa", "exportCsv");

        boolean isSuccess;
        FileOutputStream out = null;
        OutputStreamWriter osw = null;
        BufferedWriter bw = null;
        try {
            out = new FileOutputStream(file, true);
            osw = new OutputStreamWriter(out);
            bw = new BufferedWriter(osw);

            if (dataList != null && !dataList.isEmpty()) {
                for (String data : dataList) {
                    bw.append(data).append("\r\n");
                }
            }
            isSuccess = true;
        } catch (Exception e) {
            String errCode = ErrorCode.PluginError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "exportCsv", file.getName());
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportCsv", msg);
            isSuccess = false;
        } finally {
            if (bw != null) {
                try {
                    bw.close();
                    bw = null;
                } catch (IOException e) {
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportCsv", e.toString());
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportCsv", PluginIOMsg);
                }
            }
            if (osw != null) {
                try {
                    osw.close();
                    osw = null;
                } catch (IOException e) {
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportCsv", e.toString());
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportCsv", PluginIOMsg);
                }
            }
            if (out != null) {
                try {
                    out.close();
                    out = null;
                } catch (IOException e) {
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportCsv", e.toString());
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "exportCsv", PluginIOMsg);
                }
            }
        }
        return isSuccess;
    }

    /**
     * @param path      csv file path
     * @param dataList  the data need to be export, first object is the header
     * @param classType
     * @return
     */
    public boolean tableExportCSV(String path, List<T> dataList, Class classType) {
        // , List<String> idNames, Map<String, Map<String,String>> values
        try {
            if (dataList != null) {
                FieldValueTUtil<T> fieldValueTUtil = new FieldValueTUtil<>();

                T t = dataList.get(0);
                Annotation annotation = classType.getAnnotation(Table.class);
                String tableName = "";
                if (annotation instanceof Table) {
                    Table myAnnotation = (Table) annotation;
                    tableName = myAnnotation.name();
                } else {
                    LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "tableExportCSV", "Annotation is not table");
                }
                File file = new File(path + "/" + tableName + ".csv");
                StringBuilder sbName = new StringBuilder();
                List<Map<String, String>> fieldInfo = fieldValueTUtil.getTableName(t, tableName);

                for (int i = 0; i < fieldInfo.size(); i++) {
                    sbName.append(fieldInfo.get(i).get("columnName"));
                    if (i != fieldInfo.size() - 1) {
                        sbName.append("\t");
                    }
                }
                // for (int i = 0; i < idNames.size(); i++){
                // sbName.append(idNames.get(i));
                // if (i != idNames.size() - 1) {
                // sbName.append("\t");
                // }
                // }

                List<String> csvString = new ArrayList<>();
                csvString.add(sbName.toString());

                for (T tData : dataList) {
                    StringBuilder sbValue = new StringBuilder();
                    // String idValue = "";
                    for (int i = 0; i < fieldInfo.size(); i++) {
                        String name = fieldInfo.get(i).get("name");
                        Object value = fieldValueTUtil.getFieldValueByName(name, tData);
                        sbValue.append(value);
                        if (i != fieldInfo.size() - 1) {
                            sbValue.append("\t");
                        }
                        // if(name.equals("dlaId")){
                        // idValue = String.valueOf(value);
                        // }
                    }
                    csvString.add(sbValue.toString());
                }

                CSVUtil.exportCsv(file, csvString);
                return true;
            } else {
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "tableExportCSV", "dataList is null!");
                return false;
            }
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "tableExportCSV", e.toString());
            String errCode = ErrorCode.PluginError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "tableExportCSV", path);
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "tableExportCSV", msg);
            return false;
        }
    }
}
