/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.utils;

import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.trilead.ssh2.*;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

public class FtpUtils {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = FtpUtils.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    public static Connection getConn(String ip, int port, String user, String pwd) throws Exception {
        Connection conn = new Connection(ip, port);
        if (conn.isAuthenticationComplete()) {
            return conn;
        }

        conn.connect();
        boolean isAuthenticated = conn.authenticateWithPassword(user, pwd);
        if (isAuthenticated) {
            return conn;
        } else {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "getConn", "FtpServer authentication failed! ");
            return null;
        }
    }

    public static void closeConn(Connection conn){
        conn.close();
    }

    public static Session getSession(Connection conn) throws IOException {
        Session session = conn.openSession();
        return session;
    }

    public static SFTPv3Client getClient(Connection conn) throws IOException {
        SFTPv3Client client = new SFTPv3Client(conn);
        return client;
    }

    public static InputStream readFile(Connection conn, String filePath, String ip) throws Exception {
        Session session = getSession(conn);

        // get file size
        session.execCommand("du -b ".concat(filePath));
        InputStream sizeIn = new StreamGobbler(session.getStdout());
        // converse from bytes to character
        InputStreamReader isr = new InputStreamReader(sizeIn);
        // create character stream buffer
        BufferedReader bufr = new BufferedReader(isr);

        String line;
        int fileSize = 0;
        while((line = bufr.readLine()) != null) {
            String[] fileAttr = line.split("\t");
            fileSize = Integer.parseInt(fileAttr[0]);
        }
        isr.close();
        session.close();
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "readFile", "    [{}]-[{}] [fileSize] {}", ip, filePath, fileSize);

        // read content
        session = getSession(conn);
        session.execCommand("cat ".concat(filePath));
        // wait for 1000ms to avoid the network's error
        InputStream is = new StreamGobbler(session.getStdout());
        session.waitForCondition(ChannelCondition.EXIT_STATUS, 1000);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "readFile", "    [{}]-[{}] [FirstFileSize] {}", ip, filePath, is.available());
        int i = 0;
        while (fileSize != is.available()) {
            i++;
            Thread.sleep(1000);
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "readFile", "    [{}]-[{}] [times: {}, FileSize]", ip, filePath, i, is.available());
        }
        session.close();
        return is;
    }
}
