/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.utils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

public class PipeCounter {

    private final HashMap<String, Integer> countMap;
    private Integer total;
    private final HashMap<String, HashSet<String>> uniqueIds;
    private final HashMap<String, HashMap<String, Integer>> fileCounterMap;
    private final HashSet<String> failedDlaIds;

    public PipeCounter() {
        this.countMap = new HashMap<>();
        this.total = 0;
        this.uniqueIds = new HashMap<>();
        this.fileCounterMap = new HashMap<>();
        this.failedDlaIds = new HashSet<>();
    }

    public void count(String key, Integer num) {

        this.total += num;

        if (this.countMap.containsKey(key)) {
            num += this.countMap.get(key);
        }
        this.countMap.put(key, num);
    }

    // only count when the id has not been collected
    public void uniqueCount(String key, String id) {
        HashSet set = new HashSet();
        if (this.uniqueIds.keySet().contains(key)) {
            set = this.uniqueIds.get(key);
        }
        this.total -= set.size();

        set.add(id);
        this.uniqueIds.put(key, set);
        this.countMap.put(key, set.size());
        this.total += set.size();
    }

    public void uniqueCount(String key, List<String> ids) {
        HashSet set = new HashSet();
        if (this.uniqueIds.keySet().contains(key)) {
            set = this.uniqueIds.get(key);
        }
        this.total -= set.size();

        set.addAll(ids);
        this.uniqueIds.put(key, set);
        this.countMap.put(key, set.size());
        this.total += set.size();
    }

    public Integer getTotal() {
        return this.total;
    }

    public Integer countFile() {
        if (this.fileCounterMap.size() == 0 && this.countMap.size() > 0) {
            return 1;
        } else {
            return this.fileCounterMap.size();
        }
    }

    public void addFailedDlaIds(List<String> dlaIds) {
        this.failedDlaIds.addAll(dlaIds);
    }

    public void addFailedDlaId(String dlaId) {
        this.failedDlaIds.add(dlaId);
    }

    public HashSet<String> getFailedDlaIds() {
        return this.failedDlaIds;
    }

    public void mergeCounter(String name, PipeCounter counter) {
        // save fileCounterMap firstly
        this.fileCounterMap.put(name, counter.countMap);

        // process countMap data
        for (String k : counter.countMap.keySet()) {
            Integer n = counter.countMap.get(k);
            Integer m = 0;
            if (this.countMap.containsKey(k)) {
                m = this.countMap.get(k);
            }
            m += n;
            this.total += n;
            this.countMap.put(k, m);
        }

        // process the uniqueCount and fix the total
        for (String k : counter.uniqueIds.keySet()) {
            HashSet<String> t = counter.uniqueIds.get(k);
            HashSet<String> s = new HashSet<>();
            if (this.uniqueIds.containsKey(k)) {
                s = this.uniqueIds.get(k);
            }

            s.addAll(t);
            this.uniqueIds.put(k, s);
            Integer m = s.size();
            Integer delta = this.countMap.get(k) - m;
            this.total -= delta;
            this.countMap.put(k, m);
        }

        this.failedDlaIds.addAll(counter.failedDlaIds);
    }

    public String getStrContent() {
        String str = "PipeCounter detail: \n";
        for (String name : this.fileCounterMap.keySet()) {
            HashMap map = this.fileCounterMap.get(name);
            str += String.format("  %s, countMap: %s\n", name, map.toString());
        }
        str += String.format("  file count: %d, total data: %d, countMap: %s", this.countFile(), this.getTotal(),
                this.countMap.toString());
        if (!this.failedDlaIds.isEmpty()) {
            str += String.format("\n  Unexpected %d objects, dlaIds: %s", this.failedDlaIds.size(),
                    this.failedDlaIds.toString());
        }
        return str;
    }

}
