package com.ibm.palantir.sansa.utils;

import java.util.HashMap;
import java.util.List;

import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.extractor.ElementExtractor;
import com.ibm.palantir.sansa.parser.BaseInfoParser;
import com.ibm.palantir.sansa.parser.DeltaDLAGenerator;

public class CommonUtils {
    private DeltaDLAGenerator deltaDLAGenerator;
    private BaseInfoParser baseInfoParser;
    String previousFileCpcPrefixId;
    String previousFileZosPrefixId;

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = CommonUtils.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    public ElementExtractor setPreviousFileInfo(String fileUrl, ElementExtractor elementExtractor,
            StringBuilder infoLog, StringBuilder warnLog, StringBuilder errorLog) {
        try {
            this.deltaDLAGenerator = new DeltaDLAGenerator();
            this.baseInfoParser = new BaseInfoParser();

            String previousFilePath = this.deltaDLAGenerator.getPreviousFilePath(fileUrl,
                    elementExtractor.createTimestamp);
            if (previousFilePath.isBlank()) {
                String logMessage = "Not able to fetch Previous file path in Delta Mode";
                logMessage(logMessage, infoLog, warnLog, errorLog, "error", "setPreviousFileInfo");
                return null;
            }
            // No FileTrace record
            if (previousFilePath.equals("NoFileTraceRecord")) {
                String logMessage = String.format(
                        "Skip process the unexpected filePath %s, which has no fileTrace record.", fileUrl);
                logMessage(logMessage, infoLog, warnLog, errorLog, "error", "setPreviousFileInfo");
                return null;
            }

            ElementExtractor previousFileElementExtractor = new ElementExtractor(previousFilePath);
            if (previousFileElementExtractor.isEmpty) {
                String logMessage = "Pipeline PopulateDeltaDLA failed to create ElementExtractor from "
                        + previousFilePath;
                logMessage(logMessage, infoLog, warnLog, errorLog, "error", "setPreviousFileInfo");
                return null;
            }

            HashMap<String, String> previousFileBaseInfo = baseInfoParser
                    .extractBaseInfo(previousFileElementExtractor);
            if (previousFileBaseInfo == null) {
                String logMessage = "Pipeline PopulateDeltaDLA failed to extract base info from file: "
                        + previousFilePath;
                logMessage(logMessage, infoLog, warnLog, errorLog, "error", "setPreviousFileInfo");
                return null;
            }

            previousFileCpcPrefixId = previousFileBaseInfo.get("cpcPrefixId");
            previousFileZosPrefixId = previousFileBaseInfo.get("zosPrefixId");
            return previousFileElementExtractor;
        } catch (Exception e) {
            String logMessage = "Failed to set previous file info in Delta mode, error is: " + e.toString();
            logMessage(logMessage, infoLog, warnLog, errorLog, "error", "setPreviousFileInfo");

        }
        return null;
    }

    public String getPreviousFileCpcPrefixId() {
        return previousFileCpcPrefixId;
    }

    public String getPreviousFileZosPrefixId() {
        return previousFileZosPrefixId;
    }

    public void logMessage(String logMessage, StringBuilder infoLog, StringBuilder warnLog, StringBuilder errorLog,
            String logLevel, String methodName) {
        switch (logLevel.toLowerCase()) {
            case "warn" -> {
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, methodName, logMessage);
                warnLog.append(logMessage).append("\n");
            }
            case "info" -> {
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, methodName, logMessage);
                infoLog.append(logMessage).append("\n");
            }
            case "error" -> {
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, methodName, logMessage);
                errorLog.append(logMessage).append("\n");
            }
            default -> {
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, methodName, logMessage); // Default to info if no valid log
                                                                                     // level is provided
                infoLog.append(logMessage).append("\n");
            }
        }
    }

    public void setValuesAtIndexes(List<String> list, int startIndex, String... values) {
        for (int i = 0; i < values.length; i++) {
            setValueAtIndex(list, startIndex + i, values[i]);
        }
    }

    public void setValueAtIndex(List<String> list, int index, String value) {
        while (list.size() <= index) {
            list.add("");
        }

        list.set(index, value == null ? "" : value);
    }

}
