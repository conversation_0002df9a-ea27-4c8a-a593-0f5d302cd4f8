/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class TimestampConverter {

    // ISODateTimeStr example: "2023-08-18T07:24:55Z"
    public LocalDateTime getLocalDateTimeFromISODateTimeStr(String isoDateTimeStr) {
        // Parse the ISO 8601 string (with 'Z' for UTC)
        DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(isoDateTimeStr, formatter);
        // Convert ZonedDateTime to LocalDateTime (removes the timezone)
        return zonedDateTime.toLocalDateTime();
    }

    // ISODateTimeStr example: "2023-08-18T07:24:55Z"
    // zoneIdStr example: "America/New_York"
    public long getSpecificTimestampFromISODateTimeStr(String isoDateTimeStr, String zoneIdStr) {

        DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(isoDateTimeStr, formatter);

        ZoneId zoneId = ZoneId.of(zoneIdStr);
        long timestamp = zonedDateTime.withZoneSameInstant(zoneId).toEpochSecond();
        return timestamp;
    }

    // input example: System.currentTimeMillis()
    // output example: "2023-08-18T07:24:55Z"
    public String getISODateTimeStrFromUTCTimestamp(long timestamp) {

        // convert timestamp to UTC time
        Instant instant = Instant.ofEpochSecond(timestamp);
        // set date time format
        DateTimeFormatter formatter = DateTimeFormatter.ISO_INSTANT;
        String isoDateTimeStr = instant.atOffset(ZoneOffset.UTC).format(formatter);
        return isoDateTimeStr;
    }
}
