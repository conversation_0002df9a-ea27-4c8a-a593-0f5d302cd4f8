package com.ibm.palantir.sansa.exception;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.ServiceException;

public class ServiceExceptionStore {

    private ServiceExceptionStore() {
    }

    private static class Holder {
        private static final ServiceExceptionStore INSTANCE = new ServiceExceptionStore();
    }

    public static ServiceExceptionStore getInstance() {
        return Holder.INSTANCE;
    }

    private final List<String> errors = Collections.synchronizedList(new ArrayList<>());

    public void addError(String message) {
        errors.add(message);
    }

    public boolean hasErrors() {
        return !errors.isEmpty();
    }

    public List<String> getErrors() {
        synchronized (errors) {
            return Collections.unmodifiableList(new ArrayList<>(errors));
        }
    }

    public void throwIfAny() throws ServiceException {
        if (hasErrors()) {
            throw new ServiceException(ErrorCode.ProcessError.getCodeStr(), toString());
        }
    }

    public void clear() {
        errors.clear();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("\n Errors List:");
        synchronized (errors) {
            for (String e : errors) {
                sb.append("\n ").append(e);
            }
        }
        return sb.toString();
    }
}
