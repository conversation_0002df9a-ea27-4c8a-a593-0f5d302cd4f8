/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.loader;

import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IdmlOperationTime;
import com.ibm.palantir.catelyn.jpa.repository.dla.IdmlOperationTimeRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.utils.StringUtils;

public class OperationTimeLoader {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = OperationTimeLoader.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private final StringUtils stringUtils = new StringUtils();
    private final IdmlOperationTimeRepository idmlOperationTimeRepository;

    public OperationTimeLoader(IdmlOperationTimeRepository idmlOperationTimeRepository) {
        this.idmlOperationTimeRepository = idmlOperationTimeRepository;
    }

    public void loadCreateTimestamp(String hostLabel, String prefixId, String createTimestamp) {
        if (createTimestamp == null || createTimestamp.isEmpty()) {
            return;
        }

        // Ensure to save the latest CreateTimestamp
        IdmlOperationTime idmlOperationTime = idmlOperationTimeRepository.findByHostLabelAndPrefixId(hostLabel,
                prefixId);

        if (idmlOperationTime == null) {
            idmlOperationTime = new IdmlOperationTime();
            idmlOperationTime.setHostLabel(hostLabel);
            idmlOperationTime.setPrefixId(prefixId);
            idmlOperationTime.setCreateTimestamp(createTimestamp);
            idmlOperationTime.setId(stringUtils.composeUniqueId(hostLabel, prefixId));
            try {
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "loadCreateTimestamp", "save idml:create timestamp: {} of {}", createTimestamp, hostLabel);
                idmlOperationTimeRepository.save(idmlOperationTime);
            } catch (Exception e) {
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "loadCreateTimestamp", "Failed to save IdmlOperationTime: {} {}", prefixId, hostLabel);
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "loadCreateTimestamp", e.toString());
            }

        } else if (idmlOperationTime.getCreateTimestamp().compareToIgnoreCase(createTimestamp) < 0) {
            idmlOperationTime.setCreateTimestamp(createTimestamp);
            idmlOperationTimeRepository.save(idmlOperationTime);
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "loadCreateTimestamp", "update idml:create timestamp: {} of {}", createTimestamp, hostLabel);

        }

    }
}
