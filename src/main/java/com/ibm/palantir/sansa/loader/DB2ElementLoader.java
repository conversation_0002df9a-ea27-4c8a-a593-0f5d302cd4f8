/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.loader;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2BufferPool;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2Database;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2StoredProcedure;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2Table;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2TableSpace;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2DataSharingGroup;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2Subsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.DB2BufferPoolMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.DB2DataSharingGroupMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.DB2DatabaseMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.DB2StoredProcedureMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.DB2SubsystemMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.DB2TableMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.DB2TableSpaceMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2BufferPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2DataSharingGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Database;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2StoredProcedure;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Subsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Table;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2TableSpace;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2BufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DataSharingGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2StoredProcedureRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2SubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2BufferPoolMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2DataSharingGroupMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2DatabaseMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2StoredProcedureMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2SubsystemMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2TableMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2TableSpaceMetaRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.utils.CommonUtils;
import com.ibm.palantir.sansa.utils.StringUtils;

public class DB2ElementLoader {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = DB2ElementLoader.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private final VertexMapperProcessor vmProcessor = new VertexMapperProcessor();
    private final StringUtils stringUtils = new StringUtils();

    private final DB2DataSharingGroupRepository db2DataSharingGroupRepository;
    private final DB2SubsystemRepository db2SubsystemRepository;
    private final DB2DatabaseRepository db2DatabaseRepository;
    private final DB2TableSpaceRepository db2TableSpaceRepository;
    private final DB2TableRepository db2TableRepository;
    private final DB2BufferPoolRepository db2BufferPoolRepository;
    private final DB2StoredProcedureRepository db2StoredProcedureRepository;

    private final DB2DataSharingGroupMetaRepository db2DataSharingGroupMetaRepository;
    private final DB2SubsystemMetaRepository db2SubsystemMetaRepository;
    private final DB2DatabaseMetaRepository db2DatabaseMetaRepository;
    private final DB2TableSpaceMetaRepository db2TableSpaceMetaRepository;
    private final DB2TableMetaRepository db2TableMetaRepository;
    private final DB2BufferPoolMetaRepository db2BufferPoolMetaRepository;
    private final DB2StoredProcedureMetaRepository db2StoredProcedureMetaRepository;
    private CommonUtils commonUtils;

    public DB2ElementLoader(DB2DataSharingGroupRepository db2DataSharingGroupRepository,
            DB2SubsystemRepository db2SubsystemRepository,
            DB2DatabaseRepository db2DatabaseRepository, DB2TableSpaceRepository db2TableSpaceRepository,
            DB2TableRepository db2TableRepository, DB2BufferPoolRepository db2BufferPoolRepository,
            DB2StoredProcedureRepository db2StoredProcedureRepository,
            DB2DataSharingGroupMetaRepository db2DataSharingGroupMetaRepository,
            DB2SubsystemMetaRepository db2SubsystemMetaRepository, DB2DatabaseMetaRepository db2DatabaseMetaRepository,
            DB2TableSpaceMetaRepository db2TableSpaceMetaRepository,
            DB2TableMetaRepository db2TableMetaRepository, DB2BufferPoolMetaRepository db2BufferPoolMetaRepository,
            DB2StoredProcedureMetaRepository db2StoredProcedureMetaRepository) {
        this.db2DataSharingGroupRepository = db2DataSharingGroupRepository;
        this.db2SubsystemRepository = db2SubsystemRepository;
        this.db2DatabaseRepository = db2DatabaseRepository;
        this.db2TableSpaceRepository = db2TableSpaceRepository;
        this.db2TableRepository = db2TableRepository;
        this.db2BufferPoolRepository = db2BufferPoolRepository;
        this.db2StoredProcedureRepository = db2StoredProcedureRepository;

        this.db2DataSharingGroupMetaRepository = db2DataSharingGroupMetaRepository;
        this.db2SubsystemMetaRepository = db2SubsystemMetaRepository;
        this.db2DatabaseMetaRepository = db2DatabaseMetaRepository;
        this.db2TableSpaceMetaRepository = db2TableSpaceMetaRepository;
        this.db2TableMetaRepository = db2TableMetaRepository;
        this.db2BufferPoolMetaRepository = db2BufferPoolMetaRepository;
        this.db2StoredProcedureMetaRepository = db2StoredProcedureMetaRepository;
        this.commonUtils = new CommonUtils();
    }

    public Object loadDB2DataSharingGroup(SysZOSDB2DataSharingGroup sysZOSDB2DataSharingGroup, String prefixId,
            String scanDate, boolean isMeta) {
        if (isMeta) {
            DB2DataSharingGroupMeta dataSharingGroup = new DB2DataSharingGroupMeta();
            dataSharingGroup.setPrefixId(prefixId);
            dataSharingGroup.setScanDate(scanDate);
            dataSharingGroup.setId(stringUtils.composeUniqueId(sysZOSDB2DataSharingGroup.getId(), prefixId));
            dataSharingGroup.setDlaId(sysZOSDB2DataSharingGroup.getId());
            return saveDataSharingGroupMeta(dataSharingGroup);
        } else {
            DB2DataSharingGroup dataSharingGroup = new DB2DataSharingGroup();
            vmProcessor.mapFilter(sysZOSDB2DataSharingGroup, dataSharingGroup);
            dataSharingGroup.setPrefixId(prefixId);
            dataSharingGroup.setScanDate(scanDate);
            dataSharingGroup.setId(stringUtils.composeUniqueId(sysZOSDB2DataSharingGroup.getId(), prefixId));
            dataSharingGroup.setKafkaSendDate(Instant.now());
            return saveDataSharingGroup(dataSharingGroup, sysZOSDB2DataSharingGroup.getId());
        }
    }

    private Object saveDataSharingGroupMeta(DB2DataSharingGroupMeta dataSharingGroup) {
        try {
            return db2DataSharingGroupMetaRepository.save(dataSharingGroup);
        } catch (Exception e) {
            return logException("DB2DataSharingGroupMeta", dataSharingGroup.getDlaId(), e);
        }
    }

    private Object saveDataSharingGroup(DB2DataSharingGroup dataSharingGroup, String dlaId) {
        try {
            return db2DataSharingGroupRepository.save(dataSharingGroup);
        } catch (Exception e) {
            return logException("DB2DataSharingGroup", dlaId, e);
        }
    }

    public Object loadDB2Subsystem(SysZOSDB2Subsystem sysZOSDB2Subsystem, String prefixId, String scanDate,
            boolean isMeta, List<String> ciIdentifierList) {
        if (isMeta) {
            DB2SubsystemMeta db2Subsystem = new DB2SubsystemMeta();
            db2Subsystem.setPrefixId(prefixId);
            db2Subsystem.setScanDate(scanDate);
            db2Subsystem.setId(stringUtils.composeUniqueId(sysZOSDB2Subsystem.getId(), prefixId));
            db2Subsystem.setDlaId(sysZOSDB2Subsystem.getId());
            return saveDb2SubsystemMeta(db2Subsystem);
        } else {
            DB2Subsystem db2Subsystem = new DB2Subsystem();
            vmProcessor.mapFilter(sysZOSDB2Subsystem, db2Subsystem);
            db2Subsystem.setPrefixId(prefixId);
            // TODO: "modifier" and "release"
            db2Subsystem.setScanDate(scanDate);
            db2Subsystem.setId(stringUtils.composeUniqueId(sysZOSDB2Subsystem.getId(), prefixId));
            db2Subsystem.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 7, sysZOSDB2Subsystem.getSubsystemName(), sysZOSDB2Subsystem.getId());
            db2Subsystem.setCiIdentifier(String.join("|", ciIdentifierList));
            return saveDb2Subsystem(db2Subsystem, sysZOSDB2Subsystem.getId());
        }
    }

    private Object saveDb2SubsystemMeta(DB2SubsystemMeta db2Subsystem) {
        try {
            return db2SubsystemMetaRepository.save(db2Subsystem);
        } catch (Exception e) {
            return logException("DB2SubsystemMeta", db2Subsystem.getDlaId(), e);
        }
    }

    private Object saveDb2Subsystem(DB2Subsystem db2Subsystem, String dlaId) {
        try {
            return db2SubsystemRepository.save(db2Subsystem);
        } catch (Exception e) {
            return logException("DB2Subsystem", dlaId, e);
        }
    }

    public List<String> loadDB2DatabaseList(List<Object> appDB2DBElementList, String prefixId, String scanDate,
            boolean isMeta, List<String> ciIdentifierList,String db2SubSystemName, String db2SubSystemId) {
        if (isMeta) {
            return saveDB2DatabaseMetaList(createDB2DatabaseMetaList(appDB2DBElementList, prefixId, scanDate));
        } else {
            return saveDB2DatabaseList(createDB2DatabaseList(appDB2DBElementList, prefixId, scanDate, ciIdentifierList, db2SubSystemName, db2SubSystemId));
        }
    }

    private List<DB2DatabaseMeta> createDB2DatabaseMetaList(List<Object> appDB2DBElementList, String prefixId,
            String scanDate) {
        List<DB2DatabaseMeta> db2DatabaseList = new ArrayList<>();
        for (Object appDB2DBElement : appDB2DBElementList) {
            AppDbDb2Db2Database appDbDb2Db2Database = (AppDbDb2Db2Database) appDB2DBElement;
            DB2DatabaseMeta db2Database = new DB2DatabaseMeta();
            db2Database.setPrefixId(prefixId);
            db2Database.setScanDate(scanDate);
            db2Database.setId(stringUtils.composeUniqueId(appDbDb2Db2Database.getId(), prefixId));
            db2Database.setDlaId(appDbDb2Db2Database.getId());
            db2DatabaseList.add(db2Database);
        }
        return db2DatabaseList;
    }

    private List<String> saveDB2DatabaseMetaList(List<DB2DatabaseMeta> db2DatabaseList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            db2DatabaseMetaRepository.saveAll(db2DatabaseList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveDB2DatabaseMetaList",
                    "Failed to saveAll db2DatabaseList, try to save them one-by-one");
            for (DB2DatabaseMeta db2db : db2DatabaseList) {
                try {
                    db2DatabaseMetaRepository.save(db2db);
                } catch (Exception ie) {
                    catchException("DB2DatabaseMeta", failedDlaIds, db2db.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<DB2Database> createDB2DatabaseList(List<Object> appDB2DBElementList, String prefixId,
            String scanDate, List<String> ciIdentifierList, String db2SubSystemName, String db2SubSystemId) {
        List<DB2Database> db2DatabaseList = new ArrayList<>();
        for (Object appDB2DBElement : appDB2DBElementList) {
            AppDbDb2Db2Database appDbDb2Db2Database = (AppDbDb2Db2Database) appDB2DBElement;
            DB2Database db2Database = new DB2Database();
            vmProcessor.mapFilter(appDbDb2Db2Database, db2Database);
            db2Database.setPrefixId(prefixId);
            db2Database.setScanDate(scanDate);
            db2Database.setId(stringUtils.composeUniqueId(appDbDb2Db2Database.getId(), prefixId));
            db2Database.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 7, db2SubSystemName, db2SubSystemId);
            db2Database.setCiIdentifier(String.join("|", ciIdentifierList));
            db2DatabaseList.add(db2Database);
        }
        return db2DatabaseList;
    }

    private List<String> saveDB2DatabaseList(List<DB2Database> db2DatabaseList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            db2DatabaseRepository.saveAll(db2DatabaseList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveDB2DatabaseList",
                    "Failed to saveAll db2DatabaseList, try to save them one-by-one");
            for (DB2Database db2db : db2DatabaseList) {
                try {
                    db2DatabaseRepository.save(db2db);
                } catch (Exception ie) {
                    catchException("DB2Database", failedDlaIds, db2db.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadDB2TableSpaceList(List<Object> appDB2TBElementList, String prefixId, String scanDate,
            boolean isMeta) {
        if (isMeta) {
            return saveDB2TableSpaceMetaList(createAppDB2TBElementMetaList(appDB2TBElementList, prefixId, scanDate));
        } else {
            return saveDB2TableSpaceList(createAppDB2TBElementList(appDB2TBElementList, prefixId, scanDate));
        }
    }

    private List<DB2TableSpaceMeta> createAppDB2TBElementMetaList(List<Object> appDB2TBElementList, String prefixId,
            String scanDate) {
        List<DB2TableSpaceMeta> db2TableSpaceList = new ArrayList<>();

        for (Object appDB2TBElement : appDB2TBElementList) {
            AppDbDb2Db2TableSpace appDbDb2Db2TableSpace = (AppDbDb2Db2TableSpace) appDB2TBElement;
            DB2TableSpaceMeta db2TableSpace = new DB2TableSpaceMeta();
            db2TableSpace.setPrefixId(prefixId);
            db2TableSpace.setScanDate(scanDate);
            db2TableSpace.setId(stringUtils.composeUniqueId(appDbDb2Db2TableSpace.getId(), prefixId));
            db2TableSpace.setDlaId(appDbDb2Db2TableSpace.getId());
            db2TableSpaceList.add(db2TableSpace);
        }
        return db2TableSpaceList;
    }

    private List<String> saveDB2TableSpaceMetaList(List<DB2TableSpaceMeta> db2TableSpaceList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            db2TableSpaceMetaRepository.saveAll(db2TableSpaceList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveDB2TableSpaceMetaList",
                    "Failed to saveAll db2TableSpaceList, try to save them one-by-one");
            for (DB2TableSpaceMeta db2ts : db2TableSpaceList) {
                try {
                    db2TableSpaceMetaRepository.save(db2ts);
                } catch (Exception ie) {
                    catchException("DB2TableSpaceMeta", failedDlaIds, db2ts.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<DB2TableSpace> createAppDB2TBElementList(List<Object> appDB2TBElementList, String prefixId,
            String scanDate) {
        List<DB2TableSpace> db2TableSpaceList = new ArrayList<>();

        for (Object appDB2TBElement : appDB2TBElementList) {
            AppDbDb2Db2TableSpace appDbDb2Db2TableSpace = (AppDbDb2Db2TableSpace) appDB2TBElement;
            DB2TableSpace db2TableSpace = new DB2TableSpace();
            vmProcessor.mapFilter(appDbDb2Db2TableSpace, db2TableSpace);
            db2TableSpace.setPrefixId(prefixId);
            db2TableSpace.setScanDate(scanDate);
            db2TableSpace.setId(stringUtils.composeUniqueId(appDbDb2Db2TableSpace.getId(), prefixId));
            db2TableSpace.setKafkaSendDate(Instant.now());
            db2TableSpaceList.add(db2TableSpace);
        }
        return db2TableSpaceList;
    }

    private List<String> saveDB2TableSpaceList(List<DB2TableSpace> db2TableSpaceList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            db2TableSpaceRepository.saveAll(db2TableSpaceList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveDB2TableSpaceList",
                    "Failed to saveAll db2TableSpaceList, try to save them one-by-one");
            for (DB2TableSpace db2ts : db2TableSpaceList) {
                try {
                    db2TableSpaceRepository.save(db2ts);
                } catch (Exception ie) {
                    catchException("DB2TableSpace", failedDlaIds, db2ts.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadDB2TableList(List<Object> appDB2TableElementList, String prefixId, String scanDate,
            boolean isMeta) {
        if (isMeta) {
            return saveDB2TableMetaList(createAppDB2TableElementMetaList(appDB2TableElementList, prefixId, scanDate));
        } else {
            return saveDB2TableList(createAppDB2TableElementList(appDB2TableElementList, prefixId, scanDate));
        }
    }

    private List<DB2TableMeta> createAppDB2TableElementMetaList(List<Object> appDB2TableElementList, String prefixId,
            String scanDate) {
        List<DB2TableMeta> db2TableList = new ArrayList<>();

        for (Object appDB2TableElement : appDB2TableElementList) {
            AppDbDb2Db2Table appDbDb2Db2Table = (AppDbDb2Db2Table) appDB2TableElement;
            DB2TableMeta db2Table = new DB2TableMeta();
            db2Table.setPrefixId(prefixId);
            db2Table.setScanDate(scanDate);
            db2Table.setId(stringUtils.composeUniqueId(appDbDb2Db2Table.getId(), prefixId));
            db2Table.setDlaId(appDbDb2Db2Table.getId());
            db2TableList.add(db2Table);
        }
        return db2TableList;
    }

    private List<String> saveDB2TableMetaList(List<DB2TableMeta> db2TableList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            db2TableMetaRepository.saveAll(db2TableList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveDB2TableMetaList",
                    "Failed to saveAll db2TableList, try to save them one-by-one");
            for (DB2TableMeta db2t : db2TableList) {
                try {
                    db2TableMetaRepository.save(db2t);
                } catch (Exception ie) {
                    catchException("DB2TableMeta", failedDlaIds, db2t.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<DB2Table> createAppDB2TableElementList(List<Object> appDB2TableElementList, String prefixId,
            String scanDate) {
        List<DB2Table> db2TableList = new ArrayList<>();
        for (Object appDB2TableElement : appDB2TableElementList) {
            AppDbDb2Db2Table appDbDb2Db2Table = (AppDbDb2Db2Table) appDB2TableElement;
            DB2Table db2Table = new DB2Table();
            vmProcessor.mapFilter(appDbDb2Db2Table, db2Table);
            db2Table.setPrefixId(prefixId);
            db2Table.setScanDate(scanDate);
            db2Table.setId(stringUtils.composeUniqueId(appDbDb2Db2Table.getId(), prefixId));
            db2Table.setKafkaSendDate(Instant.now());
            db2TableList.add(db2Table);
        }
        return db2TableList;
    }

    private List<String> saveDB2TableList(List<DB2Table> db2TableList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            db2TableRepository.saveAll(db2TableList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveDB2TableList",
                    "Failed to saveAll db2TableList, try to save them one-by-one");
            for (DB2Table db2t : db2TableList) {
                try {
                    db2TableRepository.save(db2t);
                } catch (Exception ie) {
                    catchException("DB2Table", failedDlaIds, db2t.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadDB2BufferPoolList(List<Object> db2BufferPoolElementList, String prefixId, String scanDate,
            boolean isMeta) {
        if (isMeta) {
            return saveDB2BufferPoolMetaList(
                    createDB2BufferPoolMetaElementList(db2BufferPoolElementList, prefixId, scanDate));
        } else {
            return saveDB2BufferPoolList(createDB2BufferPoolElementList(db2BufferPoolElementList, prefixId, scanDate));
        }
    }

    private List<DB2BufferPoolMeta> createDB2BufferPoolMetaElementList(List<Object> db2BufferPoolElementList,
            String prefixId, String scanDate) {
        List<DB2BufferPoolMeta> db2BufferPoolList = new ArrayList<>();

        for (Object db2BufferPoolElement : db2BufferPoolElementList) {
            AppDbDb2Db2BufferPool appDbDb2Db2BufferPool = (AppDbDb2Db2BufferPool) db2BufferPoolElement;
            DB2BufferPoolMeta db2BufferPool = new DB2BufferPoolMeta();
            db2BufferPool.setPrefixId(prefixId);
            db2BufferPool.setScanDate(scanDate);
            db2BufferPool.setId(stringUtils.composeUniqueId(appDbDb2Db2BufferPool.getId(), prefixId));
            db2BufferPool.setDlaId(appDbDb2Db2BufferPool.getId());
            db2BufferPoolList.add(db2BufferPool);
        }
        return db2BufferPoolList;
    }

    private List<String> saveDB2BufferPoolMetaList(List<DB2BufferPoolMeta> db2BufferPoolList) {
        List<String> failedDlaIds = new ArrayList<>();
        try {
            db2BufferPoolMetaRepository.saveAll(db2BufferPoolList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveDB2BufferPoolMetaList",
                    "Failed to saveAll db2BufferPoolList, try to save them one-by-one");
            for (DB2BufferPoolMeta db2bp : db2BufferPoolList) {
                try {
                    db2BufferPoolMetaRepository.save(db2bp);
                } catch (Exception ie) {
                    catchException("DB2BufferPoolMeta", failedDlaIds, db2bp.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<DB2BufferPool> createDB2BufferPoolElementList(List<Object> db2BufferPoolElementList, String prefixId,
            String scanDate) {
        List<DB2BufferPool> db2BufferPoolList = new ArrayList<>();

        for (Object db2BufferPoolElement : db2BufferPoolElementList) {
            AppDbDb2Db2BufferPool appDbDb2Db2BufferPool = (AppDbDb2Db2BufferPool) db2BufferPoolElement;
            DB2BufferPool db2BufferPool = new DB2BufferPool();
            vmProcessor.mapFilter(appDbDb2Db2BufferPool, db2BufferPool);
            db2BufferPool.setPrefixId(prefixId);
            db2BufferPool.setScanDate(scanDate);
            db2BufferPool.setId(stringUtils.composeUniqueId(appDbDb2Db2BufferPool.getId(), prefixId));
            db2BufferPool.setKafkaSendDate(Instant.now());
            db2BufferPoolList.add(db2BufferPool);
        }
        return db2BufferPoolList;
    }

    private List<String> saveDB2BufferPoolList(List<DB2BufferPool> db2BufferPoolList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            db2BufferPoolRepository.saveAll(db2BufferPoolList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveDB2BufferPoolList",
                    "Failed to saveAll db2BufferPoolList, try to save them one-by-one");
            for (DB2BufferPool db2bp : db2BufferPoolList) {
                // saveEntityAndTrackFailures(db2bp, db2BufferPoolRepository, failedDlaIds,
                // "DB2BufferPool", db2bp.getDlaId());
                try {
                    db2BufferPoolRepository.save(db2bp);
                } catch (Exception ie) {
                    catchException("DB2BufferPool", failedDlaIds, db2bp.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public HashMap<String, List<String>> loadDB2StoredProcedureList(List<Object> db2StoredProcedureElementList,
            String prefixId, String scanDate, boolean isMeta, List<String> ciIdentifierList, String db2SubSystemName,
            String db2SubSystemId) {
        List<String> successDlaIds = new ArrayList<>();
        List<String> failedDlaIds = new ArrayList<>();

        if (isMeta) {
            failedDlaIds = saveDB2StoredProcedureMetaList(successDlaIds, failedDlaIds,
                    createDB2StoredProcedureMetaList(db2StoredProcedureElementList, prefixId, scanDate, successDlaIds));
        } else {
            failedDlaIds = saveDB2StoredProcedureList(successDlaIds, failedDlaIds, createDB2StoredProcedureElementList(
                    db2StoredProcedureElementList, prefixId, scanDate, successDlaIds, ciIdentifierList,db2SubSystemName, db2SubSystemId));
        }
        return constructDlaIdsMap(successDlaIds, failedDlaIds);
    }

    private List<DB2StoredProcedureMeta> createDB2StoredProcedureMetaList(List<Object> db2StoredProcedureElementList,
            String prefixId,
            String scanDate, List<String> successIds) {
        List<DB2StoredProcedureMeta> db2StoredProcedureList = new ArrayList<>();

        for (Object db2StoredProcedureElement : db2StoredProcedureElementList) {
            AppDbDb2Db2StoredProcedure appDbDb2Db2StoredProcedure = (AppDbDb2Db2StoredProcedure) db2StoredProcedureElement;
            DB2StoredProcedureMeta db2StoredProcedure = new DB2StoredProcedureMeta();
            db2StoredProcedure.setPrefixId(prefixId);
            db2StoredProcedure.setScanDate(scanDate);
            db2StoredProcedure.setId(stringUtils.composeUniqueId(appDbDb2Db2StoredProcedure.getId(), prefixId));
            db2StoredProcedure.setDlaId(appDbDb2Db2StoredProcedure.getId());
            db2StoredProcedureList.add(db2StoredProcedure);
            successIds.add(appDbDb2Db2StoredProcedure.getId());
        }
        return db2StoredProcedureList;
    }

    private List<String> saveDB2StoredProcedureMetaList(List<String> successDlaIds, List<String> failedDlaIds,
            List<DB2StoredProcedureMeta> db2StoredProcedureList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        try {
            db2StoredProcedureMetaRepository.saveAll(db2StoredProcedureList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveDB2StoredProcedureMetaList",
                    "Failed to saveAll db2StoredProcedureList, try to save them one-by-one");
            for (DB2StoredProcedureMeta db2sp : db2StoredProcedureList) {
                try {
                    db2StoredProcedureMetaRepository.save(db2sp);
                } catch (Exception ie) {
                    logSaveFailure("DB2StoredProcedureMeta", failedDlaIds, successDlaIds, db2sp.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<DB2StoredProcedure> createDB2StoredProcedureElementList(List<Object> db2StoredProcedureElementList,
            String prefixId,
            String scanDate, List<String> successIds, List<String> ciIdentifierList, String db2SubSystemName,
            String db2SubSystemId) {
        List<DB2StoredProcedure> db2StoredProcedureList = new ArrayList<>();

        for (Object db2StoredProcedureElement : db2StoredProcedureElementList) {
            AppDbDb2Db2StoredProcedure appDbDb2Db2StoredProcedure = (AppDbDb2Db2StoredProcedure) db2StoredProcedureElement;
            DB2StoredProcedure db2StoredProcedure = new DB2StoredProcedure();
            vmProcessor.mapFilter(appDbDb2Db2StoredProcedure, db2StoredProcedure);
            db2StoredProcedure.setPrefixId(prefixId);
            db2StoredProcedure.setScanDate(scanDate);
            db2StoredProcedure.setId(stringUtils.composeUniqueId(appDbDb2Db2StoredProcedure.getId(), prefixId));
            db2StoredProcedure.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 7, db2SubSystemName, db2SubSystemId);
            db2StoredProcedure.setCiIdentifier(String.join("|", ciIdentifierList));
            db2StoredProcedureList.add(db2StoredProcedure);
            successIds.add(appDbDb2Db2StoredProcedure.getId());
        }
        return db2StoredProcedureList;
    }

    private List<String> saveDB2StoredProcedureList(List<String> successDlaIds, List<String> failedDlaIds,
            List<DB2StoredProcedure> db2StoredProcedureList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        try {
            db2StoredProcedureRepository.saveAll(db2StoredProcedureList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveDB2StoredProcedureList",
                    "Failed to saveAll db2StoredProcedureList, try to save them one-by-one");
            for (DB2StoredProcedure db2sp : db2StoredProcedureList) {
                try {
                    db2StoredProcedureRepository.save(db2sp);
                } catch (Exception ie) {
                    logSaveFailure("DB2StoredProcedure", failedDlaIds, successDlaIds, db2sp.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private HashMap<String, List<String>> constructDlaIdsMap(List<String> successIds, List<String> failedDlaIds) {
        HashMap res = new HashMap();
        res.put("successIds", successIds);
        res.put("failedIds", failedDlaIds);
        return res;
    }

    private void catchException(String entityName, List<String> failedDlaIds, String dlaId, Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "catchException", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "catchException", ie.toString());
        failedDlaIds.add(dlaId);
    }

    private Object logException(String entityName, String dlaId, Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "logException", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "logException", ie.toString());
        return null;
    }

    // Method to log failure and update success/failure lists
    private void logSaveFailure(String entityName, List<String> failedDlaIds, List<String> successDlaIds, String dlaId,
            Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "logSaveFailure", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "logSaveFailure", ie.toString());
        successDlaIds.remove(dlaId);
        failedDlaIds.add(dlaId);
    }

}
