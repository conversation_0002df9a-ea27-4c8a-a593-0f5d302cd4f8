{"ZSeriesComputer": {"dlaId": "id", "label": "label", "name": "Name", "model": "Model", "modelId": "ModelID", "make": "Manufacturer", "manufacturer": "Manufacturer", "processingCapacity": "ProcessingCapacity", "processCapacityUnits": "ProcessCapacityUnits", "memorySize": "MemorySize", "numCPUs": "NumCPUs", "serialNumber": "SerialNumber"}, "CFComputerSystem": {"dlaId": "id", "manufacturer": "Manufacturer", "model": "Model", "serialNumber": "SerialNumber"}, "LPAR": {"dlaId": "id", "label": "label", "name": "Name", "lparName": "LPARName", "vmId": "VMID", "numCPUs": "NumCPUs", "numDedicatedCPs": "NumDedicatedCPs", "numSharedCPs": "NumSharedCPs", "model": "Model", "modelId": "ModelID", "memorySize": "MemorySize"}, "CFLPAR": {"dlaId": "id", "name": "Name", "vmId": "VMID"}, "PRSMLPAR": {"dlaId": "id", "label": "label", "name": "Name", "lparName": "LPARName", "vmId": "VMID", "numCPUs": "NumCPUs", "numDedicatedCPs": "NumDedicatedCPs", "numSharedCPs": "NumSharedCPs", "model": "Model", "modelId": "ModelID", "memorySize": "MemorySize"}, "Sysplex": {"dlaId": "id", "name": "Name", "label": "Label"}, "ZOS": {"dlaId": "id", "label": "label", "name": "Name", "smfId": "SMFID", "netID": "NetID", "sscp": "SSCP", "fqdn": "FQDN", "osName": "OSName", "osVersion": "VersionString", "sysResVolume": "SysResVolume", "iplParmDataset": "IPLParmDataset", "iplParmMember": "IPLParmMember", "iplParmDevice": "IPLParmDevice", "iplParmVolume": "IPLParmVolume", "osFriendlyName": "DynamicLNKLSTName"}, "CICSRegion": {"dlaId": "id", "label": "label", "regionName": "JobName", "applicationId": "ApplID", "netId": "NetID", "jobName": "JobName", "cicsVersion": "VersionString"}, "CICSPlex": {"dlaId": "id", "name": "CICSPlexName", "mvsSysId": "CICSPlexmvssysid"}, "CICSProgram": {"dlaId": "id", "label": "label", "programName": "Name", "programLanguage": "LanguageDefined", "programDataLocation": "DataLoc", "programExecutionKey": "ExecKey"}, "CICSTransaction": {"dlaId": "id", "label": "label", "transactionName": "Name", "dataKey": "DataKey", "dataLocation": "DataLocation", "initialProgram": "InitialProgram"}, "CICSFile": {"dlaId": "id", "label": "label", "fileName": "DDName", "datasetName": "Datasets"}, "DB2DataSharingGroup": {"dlaId": "id", "label": "Label", "name": "Name", "groupAttachName": "GroupAttachName", "groupFunction": "GroupFunction", "sysDatabaseMaxAlteredTs": "SysDatabaseMaxAlteredTs", "sysTableSpaceMaxAlteredTs": "SysTableSpaceMaxAlteredTs", "sysTableMaxAlteredTs": "SysTableMaxAlteredTs", "sysIndexesMaxAlteredTs": "SysIndexesMaxAlteredTs", "sysColumnsMaxAlteredTs": "SysColumnsMaxAlteredTs"}, "DB2Subsystem": {"dlaId": "id", "label": "Label", "keyName": "KeyName", "subSystemName": "SubsystemName", "commandPrefixName": "CommandPrefixName", "controllingAddressSpace": "ControllingAddressSpace", "DDFLocation": "DDFLocation", "versionString": "versionString"}, "DB2Database": {"dlaId": "id", "label": "Label", "name": "Name"}, "DB2TableSpace": {"dlaId": "id", "label": "Label", "name": "Name", "size": "Size", "pageSize": "PageSize", "spaceId": "SpaceId", "contentType": "ContentType", "comments": "Comments"}, "DB2Table": {"dlaId": "id", "label": "Label", "name": "Name", "tableSpace": "TableSpace", "dataBase": "DataBase"}, "DB2BufferPool": {"dlaId": "id", "label": "Label", "name": "Name", "numPages": "NumPages", "pageSize": "PageSize", "poolId": "PoolId"}, "DB2StoredProcedure": {"dlaId": "id", "label": "Label", "name": "Name", "routineType": "RoutineType", "origin": "Origin", "specificName": "SpecificName", "externalName": "ExternalName", "collId": "Collid", "language": "Language"}, "AddressSpace": {"dlaId": "id", "label": "Label", "jobName": "JobName", "jobType": "JobType", "stepName": "<PERSON><PERSON><PERSON>"}, "BindAddress": {"dlaId": "id", "path": "Path", "portNumber": "PortNumber"}, "Fqdn": {"dlaId": "id", "fqdn": "Fqdn"}, "IpAddress": {"dlaId": "id", "label": "Label", "stringNotation": "StringNotation"}, "IpInterface": {"dlaId": "id"}, "ProcessPool": {"dlaId": "id", "label": "Label", "name": "Name", "cmdLine": "CmdLine"}, "TcpPort": {"dlaId": "id", "label": "Label", "portNumber": "PortNumber"}, "UdpPort": {"dlaId": "id", "label": "Label", "portNumber": "PortNumber"}, "MQQueueSharingGroup": {"dlaId": "id", "label": "Label", "name": "Name", "groupFunction": "GroupFunction"}, "MQSubsystem": {"dlaId": "id", "label": "Label", "subsystemName": "SubsystemName", "commandPrefixName": "CommandPrefixName", "controllingAddressSpace": "ControllingAddressSpace", "versionString": "VersionString"}, "MQAliasQueue": {"dlaId": "id", "label": "Label", "name": "Name", "description": "Description", "defaultPersistence": "DefaultPersistence", "get": "Get", "put": "Put", "qsgdisp": "QSGDISP", "targetQueue": "TargetQueue"}, "MQAuthInfo": {"dlaId": "id", "name": "Name", "label": "Label", "type": "Type", "userName": "UserName", "ldapServerName": "LDAPServerName", "queueManager": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "MQBufferPool": {"dlaId": "id", "label": "Label", "idSequence": "IdSequence", "number": "Number"}, "MQClientConnectionChannel": {"dlaId": "id", "label": "Label", "name": "Name", "queueSharingGroupDisposition": "QueueSharingGroupDisposition", "headerCompression": "HeaderCompression", "messageCompression": "MessageCompression", "heartbeatInterval": "HeartbeatInterval", "keepAliveInterval": "KeepAliveInterval", "maxMessageLength": "MaxMessageLength"}, "MQClusterReceiverChannel": {"dlaId": "id", "label": "Label", "name": "Name", "queueSharingGroupDisposition": "QueueSharingGroupDisposition", "connectionName": "ConnectionName", "clwlChannelWeight": "CLWLChannelWeight", "clwlChannelPriority": "CLWLChannelPriority", "clwlChannelRank": "CLWLChannelRank", "dataConversion": "DataConversion", "longRetryTimer": "LongRetryTimer", "longRetryCount": "LongRetryCount", "messageRetryCount": "MessageRetryCount", "messageRetryInterval": "MessageRetryInterval", "putAuthority": "PutAuthority", "shortRetryTimer": "ShortRetryTimer", "shortRetryCount": "ShortRetryCount", "batchSize": "BatchSize", "batchInterval": "BatchInterval", "batchHeartbeatInterval": "BatchHeartbeatInterval", "headerCompression": "HeaderCompression", "messageCompression": "MessageCompression", "disconnectInterval": "DisconnectInterval", "heartbeatInterval": "HeartbeatInterval", "keepAliveInterval": "KeepAliveInterval", "maxMessageLength": "MaxMessageLength", "mcaType": "MCAType", "nonPersistentMessageSpeed": "NonPersistentMessageSpeed", "sslClientAuthentication": "SSLClientAuthentication"}, "MQClusterSenderChannel": {"dlaId": "id", "label": "Label", "name": "Name", "queueSharingGroupDisposition": "QueueSharingGroupDisposition", "connectionName": "ConnectionName", "clwlChannelWeight": "CLWLChannelWeight", "clwlChannelPriority": "CLWLChannelPriority", "clwlChannelRank": "CLWLChannelRank", "dataConversion": "DataConversion", "longRetryTimer": "LongRetryTimer", "longRetryCount": "LongRetryCount", "shortRetryTimer": "ShortRetryTimer", "shortRetryCount": "ShortRetryCount", "batchSize": "BatchSize", "batchInterval": "BatchInterval", "batchHeartbeatInterval": "BatchHeartbeatInterval", "headerCompression": "HeaderCompression", "messageCompression": "MessageCompression", "disconnectInterval": "DisconnectInterval", "heartbeatInterval": "HeartbeatInterval", "keepAliveInterval": "KeepAliveInterval", "maxMessageLength": "MaxMessageLength", "nonPersistentMessageSpeed": "NonPersistentMessageSpeed"}, "MQLocalQueue": {"dlaId": "id", "label": "Label", "name": "Name", "get": "Get", "put": "Put", "definitionType": "DefinitionType", "transmissionUsage": "TransmissionUsage", "maxMessageLength": "MaxMessageLength", "maxQueueDepth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "triggerControl": "TriggerControl", "triggerData": "TriggerData", "triggerDepth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "triggerType": "TriggerType", "defaultPersistence": "DefaultPersistence", "qsgdisp": "QSGDISP"}, "MQModelQueue": {"dlaId": "id", "label": "Label", "name": "Name", "initiationQueue": "InitiationQueue", "description": "Description", "get": "Get", "put": "Put", "definitionType": "DefinitionType", "transmissionUsage": "TransmissionUsage", "triggerControl": "TriggerControl", "triggerData": "TriggerData", "triggerDepth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "triggerType": "TriggerType", "defaultPersistence": "DefaultPersistence", "qsgdisp": "QSGDISP"}, "MQReceiverChannel": {"dlaId": "id", "label": "Label", "name": "Name", "queueSharingGroupDisposition": "QueueSharingGroupDisposition", "messageRetryCount": "MessageRetryCount", "messageRetryInterval": "MessageRetryInterval", "putAuthority": "PutAuthority", "batchSize": "BatchSize", "headerCompression": "HeaderCompression", "messageCompression": "MessageCompression", "heartbeatInterval": "HeartbeatInterval", "keepAliveInterval": "KeepAliveInterval", "maxMessageLength": "MaxMessageLength", "nonPersistentMessageSpeed": "NonPersistentMessageSpeed", "sslClientAuthentication": "SSLClientAuthentication", "sslCipherSpecification": "SSLCipherSpecification"}, "MQRemoteQueue": {"dlaId": "id", "label": "Label", "name": "Name", "put": "Put", "qsgdisp": "QSGDISP", "remoteName": "RemoteName", "remoteQueueMgrName": "RemoteQueueMgrName", "defaultPersistence": "DefaultPersistence"}, "MQSenderChannel": {"dlaId": "id", "label": "Label", "name": "Name", "queueSharingGroupDisposition": "QueueSharingGroupDisposition", "connectionName": "ConnectionName", "dataConversion": "DataConversion", "longRetryTimer": "LongRetryTimer", "longRetryCount": "LongRetryCount", "shortRetryTimer": "ShortRetryTimer", "shortRetryCount": "ShortRetryCount", "batchSize": "BatchSize", "batchInterval": "BatchInterval", "batchHeartbeatInterval": "BatchHeartbeatInterval", "headerCompression": "HeaderCompression", "messageCompression": "MessageCompression", "disconnectInterval": "DisconnectInterval", "heartbeatInterval": "HeartbeatInterval", "keepAliveInterval": "KeepAliveInterval", "maxMessageLength": "MaxMessageLength", "nonPersistentMessageSpeed": "NonPersistentMessageSpeed", "transmissionQueue": "TransmissionQueue"}, "MQServerConnectionChannel": {"dlaId": "id", "label": "Label", "name": "Name", "queueSharingGroupDisposition": "QueueSharingGroupDisposition", "headerCompression": "HeaderCompression", "messageCompression": "MessageCompression", "disconnectInterval": "DisconnectInterval", "heartbeatInterval": "HeartbeatInterval", "keepAliveInterval": "KeepAliveInterval", "maxMessageLength": "MaxMessageLength", "sslClientAuthentication": "SSLClientAuthentication", "description": "Description"}, "IMSSubsystem": {"dlaId": "id", "label": "Label", "keyName": "KeyName", "subsystemName": "SubsystemName", "versionString": "VersionString", "commandPrefixName": "CommandPrefixName", "controllingAddressSpace": "ControllingAddressSpace", "IMSSubsysType": "IMSSubsysType", "IMSPlexGroupName": "IMSPlexGroupName", "IRLMGroupName": "IRLMGroupName", "CQSGroupName": "CQSGroupName", "databasesChecksum": "DatabasesChecksum", "programsChecksum": "ProgramsChecksum", "transactionsChecksum": "TransactionsChecksum"}, "IMSDatabase": {"dlaId": "id", "label": "Label", "name": "Name", "IMSDatabaseType": "IMSDatabaseType"}, "IMSProgram": {"dlaId": "id", "label": "Label", "name": "Name"}, "IMSTransaction": {"dlaId": "id", "label": "Label", "name": "Name"}, "IMSSysplexGroup": {"dlaId": "id", "label": "Label", "name": "Name", "groupFunction": "GroupFunction"}}