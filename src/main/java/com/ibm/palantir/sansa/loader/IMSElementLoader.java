/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.loader;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import com.ibm.palantir.catelyn.jaxb.SysZOSIMSDatabase;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSProgram;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSSubsystem;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSSysplexGroup;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSTransaction;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.IMSDatabaseMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.IMSProgramMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.IMSSubsystemMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.IMSSysplexGroupMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.IMSTransactionMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSDatabase;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSSubsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSSysplexGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSTransaction;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSDatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSSysplexGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.IMSDatabaseMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.IMSProgramMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.IMSSubsystemMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.IMSSysplexGroupMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.IMSTransactionMetaRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.utils.CommonUtils;
import com.ibm.palantir.sansa.utils.StringUtils;

public class IMSElementLoader {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = IMSElementLoader.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private final VertexMapperProcessor vmProcessor = new VertexMapperProcessor();
    private final StringUtils stringUtils = new StringUtils();

    private final IMSSubsystemRepository imsSubsystemRepository;
    private final IMSDatabaseRepository imsDatabaseRepository;
    private final IMSProgramRepository imsProgramRepository;
    private final IMSTransactionRepository imsTransactionRepository;
    private final IMSSysplexGroupRepository imsSysplexGroupRepository;

    private final IMSSubsystemMetaRepository imsSubsystemMetaRepository;
    private final IMSDatabaseMetaRepository imsDatabaseMetaRepository;
    private final IMSProgramMetaRepository imsProgramMetaRepository;
    private final IMSTransactionMetaRepository imsTransactionMetaRepository;
    private final IMSSysplexGroupMetaRepository imsSysplexGroupMetaRepository;
    private CommonUtils commonUtils;

    public IMSElementLoader(IMSSubsystemRepository imsSubsystemRepository, IMSDatabaseRepository imsDatabaseRepository,
            IMSProgramRepository imsProgramRepository, IMSTransactionRepository imsTransactionRepository,
            IMSSysplexGroupRepository imsSysplexGroupRepository, IMSSubsystemMetaRepository imsSubsystemMetaRepository,
            IMSDatabaseMetaRepository imsDatabaseMetaRepository,
            IMSProgramMetaRepository imsProgramMetaRepository,
            IMSTransactionMetaRepository imsTransactionMetaRepository,
            IMSSysplexGroupMetaRepository imsSysplexGroupMetaRepository) {
        this.imsSubsystemRepository = imsSubsystemRepository;
        this.imsDatabaseRepository = imsDatabaseRepository;
        this.imsProgramRepository = imsProgramRepository;
        this.imsTransactionRepository = imsTransactionRepository;
        this.imsSysplexGroupRepository = imsSysplexGroupRepository;

        this.imsSubsystemMetaRepository = imsSubsystemMetaRepository;
        this.imsDatabaseMetaRepository = imsDatabaseMetaRepository;
        this.imsProgramMetaRepository = imsProgramMetaRepository;
        this.imsTransactionMetaRepository = imsTransactionMetaRepository;
        this.imsSysplexGroupMetaRepository = imsSysplexGroupMetaRepository;
        this.commonUtils = new CommonUtils();
    }

    public Object loadIMSSysplexGroup(SysZOSIMSSysplexGroup sysZOSIMSSysplexGroup, String prefixId, String scanDate,
            boolean isMeta) {
        if (isMeta) {
            IMSSysplexGroupMeta imsSysplexGroup = new IMSSysplexGroupMeta();
            imsSysplexGroup.setPrefixId(prefixId);
            imsSysplexGroup.setScanDate(scanDate);
            imsSysplexGroup.setId(stringUtils.composeUniqueId(sysZOSIMSSysplexGroup.getId(), prefixId));
            imsSysplexGroup.setDlaId(sysZOSIMSSysplexGroup.getId());
            return saveIMSSysplexGroupMeta(imsSysplexGroup, sysZOSIMSSysplexGroup);
        } else {
            IMSSysplexGroup imsSysplexGroup = new IMSSysplexGroup();
            vmProcessor.mapFilter(sysZOSIMSSysplexGroup, imsSysplexGroup);
            imsSysplexGroup.setPrefixId(prefixId);
            imsSysplexGroup.setScanDate(scanDate);
            imsSysplexGroup.setId(stringUtils.composeUniqueId(sysZOSIMSSysplexGroup.getId(), prefixId));
            imsSysplexGroup.setKafkaSendDate(Instant.now());
            return saveIMSSysplexGroup(imsSysplexGroup, sysZOSIMSSysplexGroup);
        }
    }

    private Object saveIMSSysplexGroupMeta(IMSSysplexGroupMeta imsSysplexGroup,
            SysZOSIMSSysplexGroup sysZOSIMSSysplexGroup) {
        try {
            return imsSysplexGroupMetaRepository.save(imsSysplexGroup);
        } catch (Exception e) {
            return logException("IMSSysplexGroupMeta", imsSysplexGroup.getDlaId(), e);
        }
    }

    private Object saveIMSSysplexGroup(IMSSysplexGroup imsSysplexGroup, SysZOSIMSSysplexGroup sysZOSIMSSysplexGroup) {
        try {
            return imsSysplexGroupRepository.save(imsSysplexGroup);
        } catch (Exception e) {
            return logException("IMSSysplexGroup", imsSysplexGroup.getDlaId(), e);
        }
    }

    public Object loadIMSSubsystem(SysZOSIMSSubsystem sysZOSIMSSubsystem, String prefixId, String scanDate,
            boolean isMeta, List<String> ciIdentifierList) {
        if (isMeta) {
            IMSSubsystemMeta imsSubsystem = new IMSSubsystemMeta();
            imsSubsystem.setPrefixId(prefixId);
            imsSubsystem.setScanDate(scanDate);
            imsSubsystem.setId(stringUtils.composeUniqueId(sysZOSIMSSubsystem.getId(), prefixId));
            imsSubsystem.setDlaId(sysZOSIMSSubsystem.getId());
            return saveIMSSubsystemMeta(imsSubsystem, sysZOSIMSSubsystem);
        } else {
            IMSSubsystem imsSubsystem = new IMSSubsystem();
            vmProcessor.mapFilter(sysZOSIMSSubsystem, imsSubsystem);
            imsSubsystem.setPrefixId(prefixId);
            imsSubsystem.setScanDate(scanDate);
            imsSubsystem.setId(stringUtils.composeUniqueId(sysZOSIMSSubsystem.getId(), prefixId));
            imsSubsystem.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 7, sysZOSIMSSubsystem.getSubsystemName(),
                    sysZOSIMSSubsystem.getId());
            imsSubsystem.setCiIdentifier(String.join("|", ciIdentifierList));
            return saveIMSSubsystem(imsSubsystem, sysZOSIMSSubsystem);
        }
    }

    private Object saveIMSSubsystemMeta(IMSSubsystemMeta imsSubsystem, SysZOSIMSSubsystem sysZOSIMSSubsystem) {
        try {
            return imsSubsystemMetaRepository.save(imsSubsystem);
        } catch (Exception e) {
            return logException("IMSSubsystemMeta", imsSubsystem.getDlaId(), e);
        }
    }

    private Object saveIMSSubsystem(IMSSubsystem imsSubsystem, SysZOSIMSSubsystem sysZOSIMSSubsystem) {
        try {
            return imsSubsystemRepository.save(imsSubsystem);
        } catch (Exception e) {
            return logException("IMSSubsystem", imsSubsystem.getDlaId(), e);
        }
    }

    public List<String> loadIMSDatabaseList(List<Object> sysZOSIMSDatabaseElementList, String prefixId, String scanDate,
            boolean isMeta, List<String> ciIdentifierList, String imsSubSystemName, String imsSubSystemId) {
        if (isMeta) {
            return saveImsDatabaseMetaList(createImsDatabaseMetaList(sysZOSIMSDatabaseElementList, prefixId,
                    scanDate));
        } else {
            return saveImsDatabaseList(createImsDatabaseList(sysZOSIMSDatabaseElementList, prefixId, scanDate,
                    ciIdentifierList, imsSubSystemName, imsSubSystemId));
        }
    }

    private List<IMSDatabaseMeta> createImsDatabaseMetaList(List<Object> sysZOSIMSDatabaseElementList, String prefixId,
            String scanDate) {
        List<IMSDatabaseMeta> imsDatabaseList = new ArrayList<>();

        for (Object sysZOSIMSDatabaseElement : sysZOSIMSDatabaseElementList) {
            SysZOSIMSDatabase sysZOSIMSDatabase = (SysZOSIMSDatabase) sysZOSIMSDatabaseElement;
            IMSDatabaseMeta imsDatabase = new IMSDatabaseMeta();
            imsDatabase.setPrefixId(prefixId);
            imsDatabase.setScanDate(scanDate);
            imsDatabase.setId(stringUtils.composeUniqueId(sysZOSIMSDatabase.getId(), prefixId));
            imsDatabase.setDlaId(sysZOSIMSDatabase.getId());
            imsDatabaseList.add(imsDatabase);
        }
        return imsDatabaseList;
    }

    private List<String> saveImsDatabaseMetaList(List<IMSDatabaseMeta> imsDatabaseList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            imsDatabaseMetaRepository.saveAll(imsDatabaseList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveImsDatabaseMetaList",
                    "Failed to saveAll imsDatabaseList, try to save them one-by-one");
            for (IMSDatabaseMeta imsdb : imsDatabaseList) {
                try {
                    imsDatabaseMetaRepository.save(imsdb);
                } catch (Exception ie) {
                    catchException("IMSDatabaseMeta", failedDlaIds, imsdb.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<IMSDatabase> createImsDatabaseList(List<Object> sysZOSIMSDatabaseElementList, String prefixId,
            String scanDate, List<String> ciIdentifierList, String imsSubSystemName, String imsSubSystemId) {
        List<IMSDatabase> imsDatabaseList = new ArrayList<>();

        for (Object sysZOSIMSDatabaseElement : sysZOSIMSDatabaseElementList) {
            SysZOSIMSDatabase sysZOSIMSDatabase = (SysZOSIMSDatabase) sysZOSIMSDatabaseElement;

            IMSDatabase imsDatabase = new IMSDatabase();
            vmProcessor.mapFilter(sysZOSIMSDatabase, imsDatabase);
            imsDatabase.setPrefixId(prefixId);
            imsDatabase.setScanDate(scanDate);
            imsDatabase.setId(stringUtils.composeUniqueId(sysZOSIMSDatabase.getId(), prefixId));
            imsDatabase.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 7, imsSubSystemName, imsSubSystemId);
            imsDatabase.setCiIdentifier(String.join("|", ciIdentifierList));
            imsDatabaseList.add(imsDatabase);
        }
        return imsDatabaseList;
    }

    private List<String> saveImsDatabaseList(List<IMSDatabase> imsDatabaseList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            imsDatabaseRepository.saveAll(imsDatabaseList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveImsDatabaseList",
                    "Failed to saveAll imsDatabaseList, try to save them one-by-one");
            for (IMSDatabase imsdb : imsDatabaseList) {
                try {
                    imsDatabaseRepository.save(imsdb);
                } catch (Exception ie) {
                    catchException("IMSDatabase", failedDlaIds, imsdb.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadIMSProgramList(List<Object> sysZOSIMSProgramElementList, String prefixId, String scanDate,
            boolean isMeta) {
        if (isMeta) {
            return saveIMSProgramMetaList(createIMSProgramMetaList(sysZOSIMSProgramElementList, prefixId, scanDate));
        } else {
            return saveIMSProgramList(createIMSProgramList(sysZOSIMSProgramElementList, prefixId, scanDate));
        }
    }

    private List<IMSProgramMeta> createIMSProgramMetaList(List<Object> sysZOSIMSProgramElementList, String prefixId,
            String scanDate) {
        List<IMSProgramMeta> imsProgramList = new ArrayList<>();

        for (Object sysZOSIMSProgramElement : sysZOSIMSProgramElementList) {
            SysZOSIMSProgram sysZOSIMSProgram = (SysZOSIMSProgram) sysZOSIMSProgramElement;

            IMSProgramMeta imsProgram = new IMSProgramMeta();
            imsProgram.setPrefixId(prefixId);
            imsProgram.setScanDate(scanDate);
            imsProgram.setId(stringUtils.composeUniqueId(sysZOSIMSProgram.getId(), prefixId));
            imsProgram.setDlaId(sysZOSIMSProgram.getId());
            imsProgramList.add(imsProgram);
        }
        return imsProgramList;
    }

    private List<IMSProgram> createIMSProgramList(List<Object> sysZOSIMSProgramElementList, String prefixId,
            String scanDate) {
        List<IMSProgram> imsProgramList = new ArrayList<>();

        for (Object sysZOSIMSProgramElement : sysZOSIMSProgramElementList) {
            SysZOSIMSProgram sysZOSIMSProgram = (SysZOSIMSProgram) sysZOSIMSProgramElement;

            IMSProgram imsProgram = new IMSProgram();
            vmProcessor.mapFilter(sysZOSIMSProgram, imsProgram);
            imsProgram.setPrefixId(prefixId);
            imsProgram.setScanDate(scanDate);
            imsProgram.setId(stringUtils.composeUniqueId(sysZOSIMSProgram.getId(), prefixId));
            imsProgram.setKafkaSendDate(Instant.now());
            imsProgramList.add(imsProgram);
        }
        return imsProgramList;
    }

    private List<String> saveIMSProgramMetaList(List<IMSProgramMeta> imsProgramList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            imsProgramMetaRepository.saveAll(imsProgramList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveIMSProgramMetaList",
                    "Failed to saveAll imsProgramList, try to save them one-by-one");
            for (IMSProgramMeta imsp : imsProgramList) {
                try {
                    imsProgramMetaRepository.save(imsp);
                } catch (Exception ie) {
                    catchException("IMSProgramMeta", failedDlaIds, imsp.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveIMSProgramList(List<IMSProgram> imsProgramList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            imsProgramRepository.saveAll(imsProgramList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveIMSProgramList",
                    "Failed to saveAll imsProgramList, try to save them one-by-one");
            for (IMSProgram imsp : imsProgramList) {
                try {
                    imsProgramRepository.save(imsp);
                } catch (Exception ie) {
                    catchException("IMSProgram", failedDlaIds, imsp.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadIMSTransactionList(List<Object> sysZOSIMSTransactionElementList, String prefixId,
            String scanDate, boolean isMeta, List<String> ciIdentifierList, String imsSubSystemName,
            String imsSubSystemId) {
        if (isMeta) {
            List<IMSTransactionMeta> imsTransactionList = createIMSTransactionMetaList(sysZOSIMSTransactionElementList,
                    prefixId, scanDate);

            return saveIMSTransactionMetaList(imsTransactionList);
        } else {
            List<IMSTransaction> imsTransactionList = createIMSTransactionList(sysZOSIMSTransactionElementList,
                    prefixId,
                    scanDate, ciIdentifierList, imsSubSystemName, imsSubSystemId);

            return saveIMSTransactionList(imsTransactionList);
        }
    }

    private List<IMSTransactionMeta> createIMSTransactionMetaList(List<Object> sysZOSIMSTransactionElementList,
            String prefixId, String scanDate) {
        List<IMSTransactionMeta> imsTransactionList = new ArrayList<>();

        for (Object sysZOSIMSTransactionElement : sysZOSIMSTransactionElementList) {
            SysZOSIMSTransaction sysZOSIMSTransaction = (SysZOSIMSTransaction) sysZOSIMSTransactionElement;

            IMSTransactionMeta imsTransaction = new IMSTransactionMeta();
            imsTransaction.setPrefixId(prefixId);
            imsTransaction.setScanDate(scanDate);
            imsTransaction.setId(stringUtils.composeUniqueId(sysZOSIMSTransaction.getId(), prefixId));
            imsTransaction.setDlaId(sysZOSIMSTransaction.getId());
            imsTransactionList.add(imsTransaction);
        }
        return imsTransactionList;
    }

    private List<String> saveIMSTransactionMetaList(List<IMSTransactionMeta> imsTransactionList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            imsTransactionMetaRepository.saveAll(imsTransactionList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveIMSTransactionMetaList",
                    "Failed to saveAll imsTransactionList, try to save them one-by-one");
            for (IMSTransactionMeta imst : imsTransactionList) {
                try {
                    imsTransactionMetaRepository.save(imst);
                } catch (Exception ie) {
                    catchException("IMSTransactionMeta", failedDlaIds, imst.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<IMSTransaction> createIMSTransactionList(List<Object> sysZOSIMSTransactionElementList, String prefixId,
            String scanDate, List<String> ciIdentifierList, String imsSubSystemName, String imsSubSystemId) {
        List<IMSTransaction> imsTransactionList = new ArrayList<>();

        for (Object sysZOSIMSTransactionElement : sysZOSIMSTransactionElementList) {
            SysZOSIMSTransaction sysZOSIMSTransaction = (SysZOSIMSTransaction) sysZOSIMSTransactionElement;

            IMSTransaction imsTransaction = new IMSTransaction();
            vmProcessor.mapFilter(sysZOSIMSTransaction, imsTransaction);
            imsTransaction.setPrefixId(prefixId);
            imsTransaction.setScanDate(scanDate);
            imsTransaction.setId(stringUtils.composeUniqueId(sysZOSIMSTransaction.getId(), prefixId));
            imsTransaction.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 7, imsSubSystemName, imsSubSystemId);
            imsTransaction.setCiIdentifier(String.join("|", ciIdentifierList));
            imsTransactionList.add(imsTransaction);
        }
        return imsTransactionList;
    }

    private List<String> saveIMSTransactionList(List<IMSTransaction> imsTransactionList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            imsTransactionRepository.saveAll(imsTransactionList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveIMSTransactionList",
                    "Failed to saveAll imsTransactionList, try to save them one-by-one");
            for (IMSTransaction imst : imsTransactionList) {
                try {
                    imsTransactionRepository.save(imst);
                } catch (Exception ie) {
                    catchException("IMSTransaction", failedDlaIds, imst.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private Object logException(String entityName, String dlaId, Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "logException", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "logException", ie.toString());
        return null;
    }

    private void catchException(String entityName, List<String> failedDlaIds, String dlaId, Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "catchException", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "catchException", ie.toString());
        failedDlaIds.add(dlaId);
    }

}
