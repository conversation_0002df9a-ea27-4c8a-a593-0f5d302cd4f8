/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.loader;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import com.ibm.palantir.catelyn.jaxb.SysZOSCICSFile;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSProgram;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSTransaction;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2Conn;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CICSDB2ConnMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CICSFileMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CICSProgramMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CICSRegionMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CICSTransactionMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.DB2SubsystemMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSDB2Conn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSFile;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSTransaction;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Subsystem;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSDB2ConnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSFileRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSRegionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2SubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSDB2ConnMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSFileMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSProgramMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSRegionMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSTransactionMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2SubsystemMetaRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.utils.CommonUtils;
import com.ibm.palantir.sansa.utils.StringUtils;

public class Layer4CICSLoader {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = Layer4CICSLoader.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private final VertexMapperProcessor vmProcessor = new VertexMapperProcessor();
    private final StringUtils stringUtils = new StringUtils();

    private final CICSProgramRepository cicsProgramRepository;
    private final CICSTransactionRepository cicsTransactionRepository;
    private final CICSFileRepository cicsFileRepository;
    private final CICSDB2ConnRepository cicsdb2ConnRepository;
    private final CICSRegionRepository cicsRegionRepository;
    private final DB2SubsystemRepository db2SubsystemRepository;

    private final CICSProgramMetaRepository cicsProgramMetaRepository;
    private final CICSTransactionMetaRepository cicsTransactionMetaRepository;
    private final CICSFileMetaRepository cicsFileMetaRepository;
    private final CICSDB2ConnMetaRepository cicsdb2ConnMetaRepository;
    private final CICSRegionMetaRepository cicsRegionMetaRepository;
    private final DB2SubsystemMetaRepository db2SubsystemMetaRepository;
    private CommonUtils commonUtils;

    public Layer4CICSLoader(CICSProgramRepository cicsProgramRepository,
            CICSTransactionRepository cicsTransactionRepository, CICSFileRepository cicsFileRepository,
            CICSDB2ConnRepository cicsdb2ConnRepository, CICSRegionRepository cicsRegionRepository,
            DB2SubsystemRepository db2SubsystemRepository,
            CICSProgramMetaRepository cicsProgramMetaRepository,
            CICSTransactionMetaRepository cicsTransactionMetaRepository, CICSFileMetaRepository cicsFileMetaRepository,
            CICSDB2ConnMetaRepository cicsdb2ConnMetaRepository, CICSRegionMetaRepository cicsRegionMetaRepository,
            DB2SubsystemMetaRepository db2SubsystemMetaRepository) {
        this.cicsProgramRepository = cicsProgramRepository;
        this.cicsTransactionRepository = cicsTransactionRepository;
        this.cicsFileRepository = cicsFileRepository;
        this.cicsdb2ConnRepository = cicsdb2ConnRepository;
        this.cicsRegionRepository = cicsRegionRepository;
        this.db2SubsystemRepository = db2SubsystemRepository;

        this.cicsProgramMetaRepository = cicsProgramMetaRepository;
        this.cicsTransactionMetaRepository = cicsTransactionMetaRepository;
        this.cicsFileMetaRepository = cicsFileMetaRepository;
        this.cicsdb2ConnMetaRepository = cicsdb2ConnMetaRepository;
        this.cicsRegionMetaRepository = cicsRegionMetaRepository;
        this.db2SubsystemMetaRepository = db2SubsystemMetaRepository;
        this.commonUtils = new CommonUtils();
    }

    public List<String> loadCICSProgramList(List<Object> CICSProgramElmentList, String prefixId, String scanDate,
            boolean isMeta) {
        if (isMeta) {
            return saveCICSProgramMetaList(createCICSProgramMetaList(CICSProgramElmentList, prefixId, scanDate));
        } else {
            return saveCICSProgramList(createCICSProgramList(CICSProgramElmentList, prefixId, scanDate));
        }
    }

    private List<CICSProgramMeta> createCICSProgramMetaList(List<Object> CICSProgramElmentList, String prefixId,
            String scanDate) {
        List<CICSProgramMeta> programList = new ArrayList<>();

        for (Object CICSProgramElement : CICSProgramElmentList) {
            SysZOSCICSProgram sysZOSCICSProgram = (SysZOSCICSProgram) CICSProgramElement;
            CICSProgramMeta program = new CICSProgramMeta();
            program.setPrefixId(prefixId);
            program.setScanDate(scanDate);
            program.setId(stringUtils.composeUniqueId(sysZOSCICSProgram.getId(), prefixId));
            program.setDlaId(sysZOSCICSProgram.getId());
            programList.add(program);
        }
        return programList;
    }

    private List<CICSProgram> createCICSProgramList(List<Object> CICSProgramElmentList, String prefixId,
            String scanDate) {
        List<CICSProgram> programList = new ArrayList<>();

        for (Object CICSProgramElement : CICSProgramElmentList) {
            SysZOSCICSProgram sysZOSCICSProgram = (SysZOSCICSProgram) CICSProgramElement;
            CICSProgram program = new CICSProgram();
            vmProcessor.mapFilter(sysZOSCICSProgram, program);
            program.setPrefixId(prefixId);
            program.setScanDate(scanDate);
            // TODO: still miss programDescription, programGroup
            program.setId(stringUtils.composeUniqueId(sysZOSCICSProgram.getId(), prefixId));
            program.setKafkaSendDate(Instant.now());
            programList.add(program);
        }
        return programList;
    }

    private List<String> saveCICSProgramMetaList(List<CICSProgramMeta> programList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            cicsProgramMetaRepository.saveAll(programList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveCICSProgramMetaList",
                    "Failed to saveAll CICSProgramList, try to save them one-by-one");
            for (CICSProgramMeta cicspg : programList) {
                try {
                    cicsProgramMetaRepository.save(cicspg);
                } catch (Exception ie) {
                    catchException("CICSProgramMeta", failedDlaIds, cicspg.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveCICSProgramList(List<CICSProgram> programList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            cicsProgramRepository.saveAll(programList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveCICSProgramList",
                    "Failed to saveAll CICSProgramList, try to save them one-by-one");
            for (CICSProgram cicspg : programList) {
                try {
                    cicsProgramRepository.save(cicspg);
                } catch (Exception ie) {
                    catchException("CICSProgram", failedDlaIds, cicspg.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadCICSTransactionList(List<Object> CICSTransactionElementList, String prefixId,
            String scanDate, boolean isMeta, List<String> ciIdentifierList, String cicsSubSystemName,
            String cicsSubSystemId) {
        if (isMeta) {
            return saveCICSTransactionMetaList(createCICSTransactionMetaList(CICSTransactionElementList,
                    prefixId,
                    scanDate));
        } else {
            return saveCICSTransactionList(createCICSTransactionList(CICSTransactionElementList, prefixId, scanDate,
                    ciIdentifierList, cicsSubSystemName, cicsSubSystemId));
        }
    }

    private List<CICSTransaction> createCICSTransactionList(List<Object> CICSTransactionElementList, String prefixId,
            String scanDate, List<String> ciIdentifierList, String cicsSubSystemName, String cicsSubSystemId) {
        List<CICSTransaction> transactionList = new ArrayList<>();

        for (Object CICSTransactionElement : CICSTransactionElementList) {
            SysZOSCICSTransaction sysZOSCICSTransaction = (SysZOSCICSTransaction) CICSTransactionElement;
            CICSTransaction transaction = new CICSTransaction();
            vmProcessor.mapFilter(sysZOSCICSTransaction, transaction);
            transaction.setPrefixId(prefixId);
            transaction.setScanDate(scanDate);
            transaction.setId(stringUtils.composeUniqueId(sysZOSCICSTransaction.getId(), prefixId));
            transaction.setKafkaSendDate(Instant.now());
            transaction.setInitialProgram(sysZOSCICSTransaction.getInitialProgram());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 6, null, cicsSubSystemName, cicsSubSystemId);
            transaction.setCiIdentifier(String.join("|", ciIdentifierList));
            transactionList.add(transaction);
        }
        return transactionList;
    }

    private List<CICSTransactionMeta> createCICSTransactionMetaList(List<Object> CICSTransactionElementList,
            String prefixId,
            String scanDate) {
        List<CICSTransactionMeta> transactionList = new ArrayList<>();

        for (Object CICSTransactionElement : CICSTransactionElementList) {
            SysZOSCICSTransaction sysZOSCICSTransaction = (SysZOSCICSTransaction) CICSTransactionElement;
            CICSTransactionMeta transaction = new CICSTransactionMeta();
            transaction.setPrefixId(prefixId);
            transaction.setScanDate(scanDate);
            transaction.setId(stringUtils.composeUniqueId(sysZOSCICSTransaction.getId(), prefixId));
            transaction.setDlaId(sysZOSCICSTransaction.getId());
            transaction.setInitialProgram(sysZOSCICSTransaction.getInitialProgram());
            transactionList.add(transaction);
        }
        return transactionList;
    }

    private List<String> saveCICSTransactionMetaList(List<CICSTransactionMeta> transactionList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            cicsTransactionMetaRepository.saveAll(transactionList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveCICSTransactionMetaList",
                    "Failed to saveAll CICSTransactionList, try to save them one-by-one");
            for (CICSTransactionMeta cicsta : transactionList) {
                try {
                    cicsTransactionMetaRepository.save(cicsta);
                } catch (Exception ie) {
                    catchException("CICSTransactionMeta", failedDlaIds, cicsta.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveCICSTransactionList(List<CICSTransaction> transactionList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            cicsTransactionRepository.saveAll(transactionList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveCICSTransactionList",
                    "Failed to saveAll CICSTransactionList, try to save them one-by-one");
            for (CICSTransaction cicsta : transactionList) {
                try {
                    cicsTransactionRepository.save(cicsta);
                } catch (Exception ie) {
                    catchException("CICSTransaction", failedDlaIds, cicsta.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadCICSFileList(List<Object> CICSFileElementList, String prefixId, String scanDate,
            boolean isMeta) {
        if (isMeta) {
            return saveCICSFileMetaList(createCICSFileMetaList(CICSFileElementList, prefixId, scanDate));
        } else {
            return saveCICSFileList(createCICSFileList(CICSFileElementList, prefixId, scanDate));
        }
    }

    private List<CICSFileMeta> createCICSFileMetaList(List<Object> CICSFileElementList, String prefixId,
            String scanDate) {
        List<CICSFileMeta> fileList = new ArrayList<>();

        for (Object CICSFileElement : CICSFileElementList) {
            SysZOSCICSFile sysZOSCICSFile = (SysZOSCICSFile) CICSFileElement;
            CICSFileMeta cicsFile = new CICSFileMeta();
            cicsFile.setPrefixId(prefixId);
            cicsFile.setScanDate(scanDate);
            cicsFile.setId(stringUtils.composeUniqueId(sysZOSCICSFile.getId(), prefixId));
            cicsFile.setDlaId(sysZOSCICSFile.getId());
            fileList.add(cicsFile);
        }
        return fileList;
    }

    private List<CICSFile> createCICSFileList(List<Object> CICSFileElementList, String prefixId, String scanDate) {
        List<CICSFile> fileList = new ArrayList<>();

        for (Object CICSFileElement : CICSFileElementList) {
            SysZOSCICSFile sysZOSCICSFile = (SysZOSCICSFile) CICSFileElement;
            CICSFile cicsFile = new CICSFile();
            vmProcessor.mapFilter(sysZOSCICSFile, cicsFile);
            cicsFile.setPrefixId(prefixId);
            cicsFile.setScanDate(scanDate);
            cicsFile.setId(stringUtils.composeUniqueId(sysZOSCICSFile.getId(), prefixId));
            cicsFile.setKafkaSendDate(Instant.now());
            fileList.add(cicsFile);
        }
        return fileList;
    }

    private List<String> saveCICSFileMetaList(List<CICSFileMeta> fileList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            cicsFileMetaRepository.saveAll(fileList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveCICSFileMetaList",
                    "Failed to saveAll CICSFileList, try to save them one-by-one");
            for (CICSFileMeta cicsFile : fileList) {
                try {
                    cicsFileMetaRepository.save(cicsFile);
                } catch (Exception ie) {
                    catchException("CICSFileMeta", failedDlaIds, cicsFile.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveCICSFileList(List<CICSFile> fileList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            cicsFileRepository.saveAll(fileList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveCICSFileList",
                    "Failed to saveAll CICSFileList, try to save them one-by-one");
            for (CICSFile cicsFile : fileList) {
                try {
                    cicsFileRepository.save(cicsFile);
                } catch (Exception ie) {
                    catchException("CICSFile", failedDlaIds, cicsFile.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public Object loadCICSDB2Conn(SysZOSDB2Conn sysZOSDB2Conn, String prefixId, String scanDate, boolean isMeta) {
        if (isMeta) {
            String cicsdb2ConnDlaId = sysZOSDB2Conn.getId();
            CICSDB2ConnMeta cicsdb2Conn = new CICSDB2ConnMeta();
            cicsdb2Conn.setDlaId(cicsdb2ConnDlaId);
            cicsdb2Conn.setPrefixId(prefixId);
            cicsdb2Conn.setScanDate(scanDate);
            cicsdb2Conn.setId(stringUtils.composeUniqueId(sysZOSDB2Conn.getId(), prefixId));
            return saveCICSDB2ConnMetaList(cicsdb2Conn);
        } else {
            String cicsdb2ConnDlaId = sysZOSDB2Conn.getId();
            CICSDB2Conn cicsdb2Conn = new CICSDB2Conn();
            cicsdb2Conn.setDlaId(cicsdb2ConnDlaId);
            cicsdb2Conn.setPrefixId(prefixId);
            cicsdb2Conn.setScanDate(scanDate);
            cicsdb2Conn.setId(stringUtils.composeUniqueId(sysZOSDB2Conn.getId(), prefixId));
            cicsdb2Conn.setKafkaSendDate(Instant.now());
            return saveCICSDB2ConnList(cicsdb2Conn);
        }
    }

    private Object saveCICSDB2ConnMetaList(CICSDB2ConnMeta cicsdb2Conn) {
        try {
            return cicsdb2ConnMetaRepository.save(cicsdb2Conn);
        } catch (Exception e) {
            return logException("CICSDB2ConnMeta", cicsdb2Conn.getDlaId(), e);
        }
    }

    private Object saveCICSDB2ConnList(CICSDB2Conn cicsdb2Conn) {
        try {
            return cicsdb2ConnRepository.save(cicsdb2Conn);
        } catch (Exception e) {
            return logException("CICSDB2Conn", cicsdb2Conn.getDlaId(), e);
        }
    }

    public Object checkDb2Subsystem4Conn(SysZOSDB2Conn sysZOSDB2Conn, String prefixId, String scanDate,
            boolean isMeta) {
        String cicsdb2ConnDlaId = sysZOSDB2Conn.getId();
        String db2DlaId = String.format("%s-%s-DB2Subsystem", sysZOSDB2Conn.getDB2Id(), sysZOSDB2Conn.getSMFID());

        if (isMeta) {
            return createAndSaveDB2SubsystemMeta(prefixId, scanDate, cicsdb2ConnDlaId, db2DlaId);
        } else {
            return createAndSaveDB2Subsystem(prefixId, scanDate, cicsdb2ConnDlaId, db2DlaId);
        }
    }

    private Object createAndSaveDB2Subsystem(String prefixId, String scanDate, String cicsdb2ConnDlaId,
            String db2DlaId) {
        DB2Subsystem db2Subsystem = db2SubsystemRepository.findByDlaIdAndPrefixId(db2DlaId, prefixId);
        if (db2Subsystem == null) {
            db2Subsystem = new DB2Subsystem();
            db2Subsystem.setDlaId(db2DlaId);
            db2Subsystem.setPrefixId(prefixId);
            db2Subsystem.setScanDate(scanDate);
            db2Subsystem.setId(stringUtils.composeUniqueId(db2DlaId, prefixId));
            db2Subsystem.setKafkaSendDate(Instant.now());
            return saveDB2Subsystem(db2Subsystem);

        } else {
            LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "saveCICSFileList",
                    "DB2Subsystem {} for CICSDB2Conn is existing.", db2DlaId, cicsdb2ConnDlaId);
            return db2Subsystem;
        }
    }

    private Object createAndSaveDB2SubsystemMeta(String prefixId, String scanDate, String cicsdb2ConnDlaId,
            String db2DlaId) {
        DB2SubsystemMeta db2Subsystem = db2SubsystemMetaRepository.findByDlaIdAndPrefixId(db2DlaId, prefixId);
        if (db2Subsystem == null) {
            db2Subsystem = new DB2SubsystemMeta();
            db2Subsystem.setDlaId(db2DlaId);
            db2Subsystem.setPrefixId(prefixId);
            db2Subsystem.setScanDate(scanDate);
            db2Subsystem.setId(stringUtils.composeUniqueId(db2DlaId, prefixId));
            return saveDB2SubsystemMeta(db2Subsystem);
        } else {
            LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "createAndSaveDB2SubsystemMeta",
                    "DB2SubsystemMeta {} for CICSDB2Conn {} is existing.", db2DlaId, cicsdb2ConnDlaId);
            return db2Subsystem;
        }
    }

    private Object saveDB2Subsystem(DB2Subsystem db2Subsystem) {
        try {
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "saveDB2Subsystem", "Create DB2Subsystem {} for DB2Subsystem",
                    db2Subsystem.getDlaId());
            return db2SubsystemRepository.save(db2Subsystem);
        } catch (Exception e) {
            return logException("DB2Subsystem", db2Subsystem.getDlaId(), e);
        }
    }

    private Object saveDB2SubsystemMeta(DB2SubsystemMeta db2Subsystem) {
        try {
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "saveDB2SubsystemMeta",
                    "Create DB2Subsystem {} for DB2SubsystemMeta", db2Subsystem.getDlaId());
            return db2SubsystemMetaRepository.save(db2Subsystem);
        } catch (Exception e) {
            return logException("DB2SubsystemMeta", db2Subsystem.getDlaId(), e);

        }
    }

    public Object checkCicsRegion4Conn(SysZOSDB2Conn sysZOSDB2Conn, String prefixId, String scanDate, boolean isMeta) {
        String cicsdb2ConnDlaId = sysZOSDB2Conn.getId();
        String cicsRegionJN = sysZOSDB2Conn.getCICSRegionJN();
        String smfId = sysZOSDB2Conn.getSMFID();
        String cicsDlaId = String.format("%s-%s-CICSRegion", cicsRegionJN, smfId);

        if (isMeta) {
            return createAndSaveCICSRegionMeta(prefixId, scanDate, cicsdb2ConnDlaId, cicsRegionJN, cicsDlaId);
        } else {
            return createAndSaveCICSRegion(prefixId, scanDate, cicsdb2ConnDlaId, cicsRegionJN, cicsDlaId);

        }
    }

    private Object createAndSaveCICSRegion(String prefixId, String scanDate, String cicsdb2ConnDlaId,
            String cicsRegionJN,
            String cicsDlaId) {
        CICSRegion cicsRegion = cicsRegionRepository.findByDlaIdAndPrefixId(cicsDlaId, prefixId);
        if (cicsRegion == null) {
            cicsRegion = new CICSRegion();
            cicsRegion.setDlaId(cicsDlaId);
            cicsRegion.setPrefixId(prefixId);
            cicsRegion.setScanDate(scanDate);
            cicsRegion.setId(stringUtils.composeUniqueId(cicsDlaId, prefixId));
            cicsRegion.setKafkaSendDate(Instant.now());
            return saveCICSRegion(cicsRegion);
        } else {
            LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "createAndSaveCICSRegion",
                    "CICSRegion {} for CICSDB2Conn {} is existing.", cicsRegionJN, cicsdb2ConnDlaId);
            return cicsRegion;
        }
    }

    private Object createAndSaveCICSRegionMeta(String prefixId, String scanDate, String cicsdb2ConnDlaId,
            String cicsRegionJN,
            String cicsDlaId) {
        CICSRegionMeta cicsRegion = cicsRegionMetaRepository.findByDlaIdAndPrefixId(cicsDlaId, prefixId);
        if (cicsRegion == null) {
            cicsRegion = new CICSRegionMeta();
            cicsRegion.setDlaId(cicsDlaId);
            cicsRegion.setPrefixId(prefixId);
            cicsRegion.setScanDate(scanDate);
            cicsRegion.setId(stringUtils.composeUniqueId(cicsDlaId, prefixId));
            return saveCICSRegionMeta(cicsRegion);
        } else {
            LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "createAndSaveCICSRegionMeta",
                    "CICSRegion {} for CICSDB2Conn {} is existing.", cicsRegionJN, cicsdb2ConnDlaId);
            return cicsRegion;
        }
    }

    private Object saveCICSRegion(CICSRegion cicsRegion) {
        try {
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "saveCICSRegion", "Create CICSRegion {}",
                    cicsRegion.getDlaId());
            return cicsRegionRepository.save(cicsRegion);
        } catch (Exception e) {
            return logException("CICSRegion", cicsRegion.getDlaId(), e);
        }
    }

    private Object saveCICSRegionMeta(CICSRegionMeta cicsRegion) {
        try {
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "saveCICSRegionMeta", "Create CICSRegionMeta {}",
                    cicsRegion.getDlaId());
            return cicsRegionMetaRepository.save(cicsRegion);
        } catch (Exception e) {
            return logException("CICSRegionMeta", cicsRegion.getDlaId(), e);

        }
    }

    public Object checkCICSProgram4Conn(SysZOSDB2Conn sysZOSDB2Conn, String prefixId, String scanDate, boolean isMeta) {
        String cicsdb2ConnDlaId = sysZOSDB2Conn.getId();
        String prgramName = sysZOSDB2Conn.getCICSProgramName();
        String programeDlaId = String.format("%s-%s-%s-CICSProgram", prgramName, sysZOSDB2Conn.getCICSRegionJN(),
                sysZOSDB2Conn.getSMFID());

        if (isMeta) {
            return createAndSaveCICSProgramMeta(prefixId, scanDate, cicsdb2ConnDlaId, programeDlaId);
        } else {
            return createAndSaveCICSProgram(prefixId, scanDate, cicsdb2ConnDlaId, prgramName, programeDlaId);
        }
    }

    private Object createAndSaveCICSProgram(String prefixId, String scanDate, String cicsdb2ConnDlaId,
            String prgramName,
            String programeDlaId) {
        CICSProgram cicsProgram = cicsProgramRepository.findByDlaIdAndPrefixId(programeDlaId, prefixId);
        if (cicsProgram == null) {
            cicsProgram = new CICSProgram();
            cicsProgram.setDlaId(programeDlaId);
            cicsProgram.setPrefixId(prefixId);
            cicsProgram.setProgramName(prgramName);
            cicsProgram.setScanDate(scanDate);
            cicsProgram.setId(stringUtils.composeUniqueId(programeDlaId, prefixId));
            cicsProgram.setKafkaSendDate(Instant.now());
            return saveCICSProgram(cicsProgram);
        } else {
            LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "createAndSaveCICSProgram",
                    "CICSProgram {} for CICSDB2Conn {} is existing.", programeDlaId, cicsdb2ConnDlaId);
            return cicsProgram;
        }
    }

    private Object createAndSaveCICSProgramMeta(String prefixId, String scanDate, String cicsdb2ConnDlaId,
            String programeDlaId) {
        CICSProgramMeta cicsProgram = cicsProgramMetaRepository.findByDlaIdAndPrefixId(programeDlaId, prefixId);
        if (cicsProgram == null) {
            cicsProgram = new CICSProgramMeta();
            cicsProgram.setDlaId(programeDlaId);
            cicsProgram.setPrefixId(prefixId);
            cicsProgram.setScanDate(scanDate);
            cicsProgram.setId(stringUtils.composeUniqueId(programeDlaId, prefixId));
            return saveCICSProgramMeta(cicsProgram);
        } else {
            LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "createAndSaveCICSProgramMeta",
                    "CICSProgramMeta {} for CICSDB2Conn {} is existing.", programeDlaId, cicsdb2ConnDlaId);
            return cicsProgram;
        }
    }

    private Object saveCICSProgram(CICSProgram cicsProgram) {
        try {
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "createAndSaveCICSProgramMeta", "Create CICSProgram {}",
                    cicsProgram.getDlaId());
            return cicsProgramRepository.save(cicsProgram);
        } catch (Exception e) {
            return logException("CICSProgram", cicsProgram.getDlaId(), e);
        }
    }

    private Object saveCICSProgramMeta(CICSProgramMeta cicsProgram) {
        try {
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "saveCICSProgramMeta", "Create CICSProgramMeta {}",
                    cicsProgram.getDlaId());
            return cicsProgramMetaRepository.save(cicsProgram);
        } catch (Exception e) {
            return logException("CICSProgramMeta", cicsProgram.getDlaId(), e);

        }
    }

    private Object logException(String entityName, String dlaId, Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "logException", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "logException", ie.toString());
        return null;
    }

    private void catchException(String entityName, List<String> failedDlaIds, String dlaId, Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "catchException", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "catchException", ie.toString());
        failedDlaIds.add(dlaId);
    }

}
