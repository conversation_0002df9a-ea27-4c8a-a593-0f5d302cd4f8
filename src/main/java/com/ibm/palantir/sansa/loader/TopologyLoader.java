package com.ibm.palantir.sansa.loader;

import java.util.HashMap;
import java.util.Map;

import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.ibm.palantir.catelyn.util.HttpUtils;

public class TopologyLoader {


    private static final String resourcePath = "/resources";

    private static final String rulePath = "/rules";


    public void postRule(String url, Map<String, String> headers) throws Exception {
        String address = url + rulePath;
        Map<String, String> parameters = new HashMap<>();
        parameters.put("ruleType", "mergeRule");
        parameters.put("_include_count", "true");
        String result = HttpUtils.get(address, headers, parameters, true);
        JsonElement jsonElement = JsonParser.parseString(result);
        if(jsonElement.isJsonObject() && jsonElement.getAsJsonObject().has("_count")) {
            int number = jsonElement.getAsJsonObject().get("_count").getAsInt();
            if(number == 0) {
                String rule = "{\"name\": \"mergeTokensMatching\",\n\"ruleType\": \"mergeRule\",\n\"tokens\": [\"mergeTokens\"],\n\"ruleStatus\": \"enabled\",\n\"customField\": \"string\"\n}";
                HttpUtils.post(address, headers, null, rule, true);
            }
        }
    }

    public String postResource(String url, Map<String, String> headers, String id, String value) throws Exception {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("_follow_composites", "false");
        parameters.put("_deleted", "false");
        parameters.put("_include_status", "false");
        parameters.put("_include_status_severity", "false");
        parameters.put("_include_metadata", "false");
        String address = url + resourcePath + "/" + id;
        String result = HttpUtils.post(address, headers, null,  value, true);
        return result;
    }

}
