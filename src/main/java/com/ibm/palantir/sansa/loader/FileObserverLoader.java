/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.loader;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.io.FilenameUtils;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ibm.palantir.catelyn.util.HttpUtils;
import com.ibm.palantir.catelyn.util.KeyUtil;

public class FileObserverLoader {

    private static final String topologyEdgePath = "/types/edge";
    private static final String fileObserverFilePath = "/files";
    private static final String fileObserverJobPath = "/jobs/load";
    private static final String authorizePath = "/authorize";

    public void loadData(String topologyUrl, String fileObserverUrl, String tenantID, ArrayList<String> fileList,
            String author) throws Exception {

        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json");
        headers.put("Content-Type", "application/json");
        headers.put("X-TenantID", tenantID);
        headers.put("Authorization", author);

        Map<String, String> textMap = new HashMap<>();
        Map<String, String> fileMap = new HashMap<>();

        createType(topologyUrl, headers);
        for (String file : fileList) {
            fileMap.put("job_file", file);
            uploadFile(fileObserverUrl, headers, textMap, fileMap);
            createJob(fileObserverUrl, headers, FilenameUtils.getName(file));
        }
    }

    public String getToken(String url, String username, String api_key) throws Exception {
        String address = url + authorizePath;
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json");
        headers.put("Content-Type", "application/json");
        String jsonObject = "{\"username\": \"" + username + "\", \"api_key\": \"" + api_key + "\"}";
        String result = HttpUtils.post(address, headers, null, jsonObject, true);
        return "Bearer " + JsonParser.parseString(result).getAsJsonObject().get("token").getAsString();
    }

    public String getBasic(String username, String password) throws Exception {
        String authString = username + ":" + password;
        byte[] authEncBytes = Base64.getEncoder().encode(authString.getBytes(StandardCharsets.UTF_8));

        String authStringEnc = new String(authEncBytes);
        return "Basic " + authStringEnc;

    }

    public void createType(String url, Map<String, String> headers) throws Exception {
        Gson gson = new Gson();
        String address = url + topologyEdgePath;
        String[] relationships = new String[] {
                "contains", "uses", "hasMember", "federates", "transactionalDependency", "connects",
                "memberOf", "runsOn", "virtualizes", "hostedDependency", "appliesTo", "runs"
        };
        for (String relationship : relationships) {
            String getUrl = address + "?_filter=edgeType%3DzDiscovery_" + relationship
                    + "&_include_count=false&_include_metadata=false";
            String result;
            try {
                result = HttpUtils.get(getUrl, headers, null, true);
            } catch (Exception e) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("edgeType", "zDiscovery_" + relationship);
                jsonObject.addProperty("edgeLabel", "association");
                jsonObject.addProperty("description", relationship + " edge type in zDiscovery");
                HttpUtils.post(address, headers, null, jsonObject.toString(), true);
                return;
            }
            JsonObject resultObject = gson.fromJson(result, JsonObject.class);
            if (resultObject.has("_items") && resultObject.get("_items").isJsonArray()) {
                JsonArray items = resultObject.getAsJsonArray("_items");
                if (items.size() == 0) {
                    JsonObject jsonObject = new JsonObject();
                    jsonObject.addProperty("edgeType", "zDiscovery_" + relationship);
                    jsonObject.addProperty("edgeLabel", "association");
                    jsonObject.addProperty("description", relationship + " edge type in zDiscovery");
                    HttpUtils.post(address, headers, null, jsonObject.toString(), true);
                }
            }
        }
    }

    public void uploadFile(String url, Map<String, String> headers, Map<String, String> textMap,
            Map<String, String> fileMap) throws Exception {
        String address = url + fileObserverFilePath;
        try {
            HttpUtils.post(address, headers, null, textMap, fileMap);
        } catch (Exception e) {
            HttpUtils.put(address, headers, null, textMap, fileMap);
        }
    }

    public void createJob(String url, Map<String, String> headers, String fileName) throws Exception {
        String address = url + fileObserverJobPath;
        String jon_name = FilenameUtils.getBaseName(fileName) + "_file_job_" + KeyUtil.genUniqueKey();
        String jobObject = "{\"unique_id\": \"" + jon_name
                + "\",\"type\": \"load\",\"description\": \"job description\",\"parameters\": {\"provider\": \"FILE.file\",\"file\": \""
                + fileName + "\"}}";
        HttpUtils.post(address, headers, null, jobObject, true);
    }

}
