/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.loader;

import java.lang.reflect.Field;
import java.text.MessageFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.RelationshipMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Relationship;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.RelationshipMetaRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.utils.StringUtils;

public class RelationshipLoader {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = RelationshipLoader.class.getSimpleName();
    private static final String REPONAME = "Sansa";
    private final List<String> cpcPrefixItems = Arrays.asList("ZSeriesComputerSystem", "LPAR");
    private final List<String> emptyPrefixItems = Arrays.asList("Sysplex", "DB2DataSharingGroup", "IMSSysplexGroup",
            "MQQueueSharingGroup");
    private final RelationshipRepository relationshipRepository;
    private final RelationshipMetaRepository relationshipMetaRepository;
    private final StringUtils stringUtils = new StringUtils();

    public RelationshipLoader(RelationshipRepository relationshipRepository,
            RelationshipMetaRepository relationshipMetaRepository) {
        this.relationshipRepository = relationshipRepository;
        this.relationshipMetaRepository = relationshipMetaRepository;
    }

    String[][] EdgeMapperArray = new String[][] {
            { "virtualizes", "LPAR", "ZSeriesComputerSystem", "hasMember" },
            { "runsOn", "ZOS", "LPAR", "runs" },
            { "memberOf", "ZOS", "Sysplex", "hasMember" },
            { "hasMember", "ZSubSystem", "CICSRegion", "" },
            { "runsOn", "CICSRegion", "ZOS", "contains" },
            { "runsOn", "CICSRegion", "LPAR", "runs" },
            { "memberOf", "CICSRegion", "CICSPlex", "hasMember" },
            { "memberOf", "Db2Table", "TableSpace", "hasMember" },
            { "appliesTo", "CICS_SIT-ZReportFile", "CICSRegion", "contains" },
            { "appliesTo", "CICS_SIT_Overrides-ZReportFile", "CICSRegion", "contains" },
            { "contains", "CICSRegion", "CICSTransaction", "" },
            { "uses", "CICSTransaction", "CICSProgram", "" },
            { "contains", "CICSRegion", "CICSFile", "" },
            { "contains", "CICSRegion", "CICSProgram", "" },
            { "contains", "CICSRegion", "DB2Conn", "" },
            { "uses", "CICSProgram", "DB2Conn", "" },
            { "connects", "DB2Conn", "DB2Subsystem", "" },
            { "contains", "Sysplex", "DB2DataSharingGroup", "" },
            { "hasMember", "ZSubSystem", "DB2Subsystem", "" },
            { "runsOn", "DB2Subsystem", "LPAR", "runs" },
            { "hostedDependency", "DB2Subsystem", "ZOS", "contains" },
            { "uses", "DB2Subsystem", "DB2DataSharingGroup", "" },
            { "federates", "DB2DataSharingGroup", "DB2Subsystem", "" },
            { "contains", "DB2DataSharingGroup", "Db2Database", "" },
            { "contains", "DB2Subsystem", "Db2Database", "" },
            { "contains", "Db2Database", "Db2TableSpace", "" },
            { "contains", "Db2Database", "Db2BufferPool", "" },
            { "contains", "DB2Subsystem", "Db2StoredProcedure", "" },
            { "contains", "DB2DataSharingGroup", "Db2StoredProcedure", "" },
            { "transactionalDependency", "CICSRegion", "DB2Subsystem", "" },
            // {"uses", "CICSRegion", "DB2Subsystem", ""},
            { "transactionalDependency", "CICSRegion", "MQSubsystem", "" },
            // {"uses", "CICSRegion", "MQSubsystem", ""},
            { "transactionalDependency", "CICSRegion", "CICSRegion", "" },
            // {"uses", "CICSRegion", "CICSRegion", ""},
            { "contains", "ZOS", "IpInterface", "" },
            { "contains", "LPAR", "IpInterface", "" },
            { "bindsTo", "IpInterface", "IpAddress", "" },
            { "assignedTo", "Fqdn", "IpAddress", "" },
            { "bindsTo", "TcpPort", "IpInterface", "" },
            { "bindsTo", "UdpPort", "IpInterface", "" },
            { "runsOn", "AddressSpace", "ZOS", "" },
            { "accessedVia", "AddressSpace", "TcpPort", "" },
            { "accessedVia", "AddressSpace", "UdpPort", "" },
            { "uses", "CICSRegion", "AddressSpace", "" },
            { "bindsAsPrimary", "BindAddress", "IpAddress", "" },
            { "bindsTo", "BindAddress", "IpAddress", "" },
            { "bindsTo", "BindAddress", "Fqdn", "" },
            { "uses", "CICSRegion", "ProcessPool", "" },
            { "memberOf", "AddressSpace", "ProcessPool", "" },
            { "uses", "AddressSpace", "BindAddress", "" },
            { "accessedVia", "AddressSpace", "BindAddress", "" },
            { "federates", "DB2Subsystem", "AddressSpace", "" },
            { "accessedVia", "MQSubsystem", "BindAddress", "" },
            { "hostedDependency", "MQSubsystem", "ZOS", "contains" },
            { "runsOn", "MQSubsystem", "LPAR", "runs" },
            { "manages", "MQSubsystem", "MQLocalQueue", "" },
            { "manages", "MQSubsystem", "MQAliasQueue", "" },
            { "manages", "MQSubsystem", "MQRemoteQueue", "" },
            { "manages", "MQSubsystem", "MQModelQueue", "" },
            { "federates", "MQSubsystem", "MQSenderChannel", "" },
            { "federates", "MQSubsystem", "MQReceiverChannel", "" },
            { "federates", "MQSubsystem", "MQServerConnectionChannel", "" },
            { "federates", "MQSubsystem", "MQClientConnectionChannel", "" },
            { "federates", "MQSubsystem", "MQClusterReceiverChannel", "" },
            { "federates", "MQSubsystem", "MQClusterSenderChannel", "" },
            { "authorizes", "MQAuthInfo", "MQSubsystem", "" },
            { "uses", "MQSubsystem", "MQBufferPool", "" },
            { "hostedDependency", "IMSSubsystem", "ZOS", "contains" },
            { "runsOn", "IMSSubsystem", "LPAR", "runs" },
            { "contains", "IMSSubsystem", "IMSDatabase", "" },
            { "contains", "IMSSubsystem", "IMSProgram", "" },
            { "contains", "IMSSubsystem", "IMSTransaction", "" },
            { "uses", "IMSTransaction", "IMSProgram", "" },
            { "federates", "IMSSubsystem", "AddressSpace", "" },
            { "contains", "Sysplex", "IMSSysplexGroup", "" },
            { "federates", "IMSSysplexGroup", "IMSSubsystem", "" },
            { "uses", "IMSSubsystem", "IMSSysplexGroup", "" },
            { "contains", "Sysplex", "MQQueueSharingGroup", "" },
            { "federates", "MQQueueSharingGroup", "MQSubsystem", "" },
            { "uses", "MQSubsystem", "MQQueueSharingGroup", "" }
    };

    public void loadRelationshipList(List<Object> currentElementList, List<String> filterIds,
            String cpcPrefixId, String zosPrefixId, String scanDate, boolean isMeta) {
        if (isMeta) {
            List<RelationshipMeta> relationshipList = createRelationshipMetaList(currentElementList, filterIds,
                    cpcPrefixId,
                    zosPrefixId, isMeta, scanDate);
            saveRelationshipsMeta(relationshipList);
        } else {
            List<Relationship> currentRelationshipList = createRelationshipList(currentElementList, filterIds,
                    cpcPrefixId,
                    zosPrefixId, isMeta, scanDate);
            saveRelationships(currentRelationshipList);
        }

    }

    public void addOrUpdateRelationship(String relationName, String source, String target, String prefixId,
            boolean isMeta, String scanDate) {
        if (isMeta) {
            createAndSaveRelationshipMetaObject(relationName, source, target, prefixId, scanDate);
        } else {
            createAndSaveRelationshipObject(relationName, source, target, prefixId, scanDate);
        }
    }

    private List<RelationshipMeta> createRelationshipMetaList(List<Object> elementList, List<String> filterIds,
            String cpcPrefixId, String zosPrefixId, boolean isMeta, String scanDate) {
        List<RelationshipMeta> relationshipList = new ArrayList<>();

        for (Object element : elementList) {
            String className = element.getClass().getName();
            String relationName = stringUtils.getRelationFromClassName(className);
            try {
                Class clazz = Class.forName(className);
                String source = "";
                String sourcePrefix = "";
                String target = "";
                String targetPrefix = "";
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    String fieldName = field.getName();
                    field.setAccessible(true);
                    if (fieldName.equals("source")) {
                        source = (String) field.get(element);
                        sourcePrefix = selectPrefix4DlaId(source, cpcPrefixId, zosPrefixId);
                    } else if (fieldName.equals("target")) {
                        target = (String) field.get(element);
                        targetPrefix = selectPrefix4DlaId(target, cpcPrefixId, zosPrefixId);
                    }
                }

                // Commenting this part of the code after discussion with Patrick | Failed id's
                // should be handled from SNOW side
                // Skip those filtered Items
                // if (filterIds.size() > 0 && (filterIds.contains(source) ||
                // filterIds.contains(target))) {
                // continue;
                // }

                Object result = createRelationship(relationName, source, target, sourcePrefix, targetPrefix, isMeta,
                        scanDate);
                RelationshipMeta r = (RelationshipMeta) result;
                if (r != null) {
                    relationshipList.add(r);
                }
            } catch (ClassNotFoundException e) {
                logError(e, ErrorCode.PluginClassNotFoundError.getCodeStr(), "createRelationshipMetaList", element);
            } catch (IllegalAccessException e) {
                logError(e, ErrorCode.PluginIllegalAccessError.getCodeStr(), "createRelationshipMetaList", element);
            } catch (Exception e) {
                logError(e, ErrorCode.PluginError.getCodeStr(), "createRelationshipMetaList", element);
            }
        }
        return relationshipList;
    }

    private List<Relationship> createRelationshipList(List<Object> elementList, List<String> filterIds,
            String cpcPrefixId,
            String zosPrefixId, boolean isMeta, String scanDate) {

        List<Relationship> relationshipList = new ArrayList<>();

        for (Object element : elementList) {
            String className = element.getClass().getName();
            String relationName = stringUtils.getRelationFromClassName(className);

            try {
                Class clazz = Class.forName(className);
                String source = "";
                String sourcePrefix = "";
                String target = "";
                String targetPrefix = "";
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    String fieldName = field.getName();
                    field.setAccessible(true);
                    if (fieldName.equals("source")) {
                        source = (String) field.get(element);
                        sourcePrefix = selectPrefix4DlaId(source, cpcPrefixId, zosPrefixId);
                    } else if (fieldName.equals("target")) {
                        target = (String) field.get(element);
                        targetPrefix = selectPrefix4DlaId(target, cpcPrefixId, zosPrefixId);
                    }
                }

                // Commenting this part of the code after discussion with Patrick | Failed id's
                // should be handled from SNOW side
                // Skip those filtered Items
                // if (!filterIds.isEmpty() && (filterIds.contains(source) ||
                // filterIds.contains(target))) {
                // continue;
                // }

                Object result = createRelationship(relationName, source, target, sourcePrefix, targetPrefix,
                        isMeta,
                        scanDate);
                Relationship r = (Relationship) result;
                if (r != null) {
                    relationshipList.add(r);
                }
            } catch (ClassNotFoundException e) {
                logError(e, ErrorCode.PluginClassNotFoundError.getCodeStr(), "createRelationshipList", element);
            } catch (IllegalAccessException e) {
                logError(e, ErrorCode.PluginIllegalAccessError.getCodeStr(), "createRelationshipList", element);
            } catch (Exception e) {
                logError(e, ErrorCode.PluginError.getCodeStr(), "createRelationshipList", element);
            }
        }
        return relationshipList;
    }

    public void saveRelationships(List<Relationship> relationshipList) {
        if (!relationshipList.isEmpty()) {
            relationshipRepository.saveAll(relationshipList);
        }
    }

    private void saveRelationshipsMeta(List<RelationshipMeta> relationshipList) {
        if (!relationshipList.isEmpty()) {
            relationshipMetaRepository.saveAll(relationshipList);
        }
    }

    private void createAndSaveRelationshipMetaObject(String relationName, String source, String target,
            String prefixId, String scanDate) {
        for (String[] mapperArray : EdgeMapperArray) {
            List<String> mapper = Arrays.asList(mapperArray);
            if (relationName.equals(mapper.get(0)) && source.contains(mapper.get(1))
                    && target.contains(mapper.get(2))) {
                RelationshipMeta relationship = new RelationshipMeta();
                relationship.setName(relationName);
                relationship.setSourceId(stringUtils.composeUniqueId(source, prefixId));
                relationship.setTargetId(stringUtils.composeUniqueId(target, prefixId));
                relationship.setScanDate(scanDate);
                relationshipMetaRepository.save(relationship);
            }
        }
    }

    private void createAndSaveRelationshipObject(String relationName, String source, String target, String prefixId,
            String scanDate) {
        // filter the relationships by EdgeMapper
        for (String[] mapperArray : EdgeMapperArray) {
            List<String> mapper = Arrays.asList(mapperArray);
            if (relationName.equals(mapper.get(0)) && source.contains(mapper.get(1))
                    && target.contains(mapper.get(2))) {

                // only the relationship is included in the EdgeMapper, it can be stored.
                Relationship relationship = new Relationship();
                relationship.setName(relationName);
                relationship.setSourceId(stringUtils.composeUniqueId(source, prefixId));
                relationship.setTargetId(stringUtils.composeUniqueId(target, prefixId));
                relationship.setSource(source);
                relationship.setTarget(target);
                relationship.setSourcePrefix(prefixId);
                relationship.setTargetPrefix(prefixId);
                relationship.setReverseName(mapper.get(3));
                relationship.setScanDate(scanDate);
                relationship.setKafkaSendDate(Instant.now());
                relationshipRepository.save(relationship);
            }
        }
    }

    private Object createRelationship(String relationName, String source, String target, String sourcePrefix,
            String targetPrefix, boolean isMeta, String scanDate) {
        if (isMeta) {
            for (String[] mapperArray : EdgeMapperArray) {
                List<String> mapper = Arrays.asList(mapperArray);
                if (relationName.equals(mapper.get(0)) && source.contains(mapper.get(1))
                        && target.contains(mapper.get(2))) {
                    return createRelationshipMetaObject(relationName, source, target, sourcePrefix, targetPrefix,
                            scanDate);
                }
            }
        } else {
            // filter the relationships by EdgeMapper
            for (String[] mapperArray : EdgeMapperArray) {
                List<String> mapper = Arrays.asList(mapperArray);
                if (relationName.equals(mapper.get(0)) && source.contains(mapper.get(1))
                        && target.contains(mapper.get(2))) {

                    return createRelationshipObject(relationName, source, target, sourcePrefix, targetPrefix, mapper,
                            scanDate);
                }
            }

        }
        return null;
    }

    private Object createRelationshipMetaObject(String relationName, String source, String target, String sourcePrefix,
            String targetPrefix, String scanDate) {
        RelationshipMeta relationship = new RelationshipMeta();
        relationship.setName(relationName);
        relationship.setSourceId(stringUtils.composeUniqueId(source, sourcePrefix));
        relationship.setTargetId(stringUtils.composeUniqueId(target, targetPrefix));
        relationship.setScanDate(scanDate);
        return relationship;
    }

    private Object createRelationshipObject(String relationName, String source, String target, String sourcePrefix,
            String targetPrefix, List<String> mapper, String scanDate) {
        // only the relationship is included in the EdgeMapper, it can be stored.
        Relationship relationship = new Relationship();
        relationship.setName(relationName);
        relationship.setSourceId(stringUtils.composeUniqueId(source, sourcePrefix));
        relationship.setTargetId(stringUtils.composeUniqueId(target, targetPrefix));
        relationship.setSource(source);
        relationship.setTarget(target);
        relationship.setSourcePrefix(sourcePrefix);
        relationship.setTargetPrefix(targetPrefix);
        relationship.setReverseName(mapper.get(3));
        relationship.setKafkaSendDate(Instant.now());
        relationship.setScanDate(scanDate);
        return relationship;
    }

    private String selectPrefix4DlaId(String dlaId, String cpcPrefixId, String zosPrefixId) {
       String clazz = stringUtils.getClassTypeByDlaIdIgnoreProgram(dlaId);

        if (emptyPrefixItems.contains(clazz)) {
            return "";
        } else if (cpcPrefixItems.contains(clazz)) {
            return cpcPrefixId;
        } else {
            return zosPrefixId;
        }
    }

    // Generic method to log the exception
    private void logError(Exception e, String errCode, String methodName, Object element, Object... args) {
        LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "logError", "Exception: {}", e.toString());
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "logError", element.toString());
        String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", methodName);
        LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "logError", "Error in method {0}: {1}", methodName, msg);
    }
}
