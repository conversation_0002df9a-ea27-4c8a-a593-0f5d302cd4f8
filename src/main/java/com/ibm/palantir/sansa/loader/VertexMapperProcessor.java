/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.loader;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.utils.ReflectUtil;

public class VertexMapperProcessor {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = VertexMapperProcessor.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private JsonObject vmJson;

    private String content = "{\n" +
            "  \"ZSeriesComputer\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"model\": \"Model\",\n" +
            "    \"modelId\": \"ModelID\",\n" +
            "    \"make\": \"Manufacturer\",\n" +
            "    \"manufacturer\": \"Manufacturer\",\n" +
            "    \"processingCapacity\": \"ProcessingCapacity\",\n" +
            "    \"processCapacityUnits\": \"ProcessCapacityUnits\",\n" +
            "    \"memorySize\": \"MemorySize\",\n" +
            "    \"numCPUs\": \"NumCPUs\",\n" +
            "    \"serialNumber\": \"SerialNumber\"\n" +
            "  },\n" +
            "  \"CFComputerSystem\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"manufacturer\": \"Manufacturer\",\n" +
            "    \"model\": \"Model\",\n" +
            "    \"serialNumber\": \"SerialNumber\"\n" +
            "  },\n" +
            "  \"LPAR\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"lparName\": \"LPARName\",\n" +
            "    \"vmId\": \"VMID\",\n" +
            "    \"numCPUs\": \"NumCPUs\",\n" +
            "    \"numDedicatedCPs\": \"NumDedicatedCPs\",\n" +
            "    \"numSharedCPs\": \"NumSharedCPs\",\n" +
            "    \"model\": \"Model\",\n" +
            "    \"modelId\": \"ModelID\",\n" +
            "    \"memorySize\": \"MemorySize\"\n" +
            "  },\n" +
            "  \"CFLPAR\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"vmId\": \"VMID\"\n" +
            "  },\n" +
            "  \"PRSMLPAR\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"lparName\": \"LPARName\",\n" +
            "    \"vmId\": \"VMID\",\n" +
            "    \"numCPUs\": \"NumCPUs\",\n" +
            "    \"numDedicatedCPs\": \"NumDedicatedCPs\",\n" +
            "    \"numSharedCPs\": \"NumSharedCPs\",\n" +
            "    \"model\": \"Model\",\n" +
            "    \"modelId\": \"ModelID\",\n" +
            "    \"memorySize\": \"MemorySize\"\n" +
            "  },\n" +
            "  \"Sysplex\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"label\": \"Label\"\n" +
            "  },\n" +
            "  \"ZOS\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"smfId\": \"SMFID\",\n" +
            "    \"netID\": \"NetID\",\n" +
            "    \"sscp\": \"SSCP\",\n" +
            "    \"fqdn\": \"FQDN\",\n" +
            "    \"osName\": \"OSName\",\n" +
            "    \"osVersion\": \"VersionString\",\n" +
            "    \"sysResVolume\": \"SysResVolume\",\n" +
            "    \"iplParmDataset\": \"IPLParmDataset\",\n" +
            "    \"iplParmMember\": \"IPLParmMember\",\n" +
            "    \"iplParmDevice\": \"IPLParmDevice\",\n" +
            "    \"iplParmVolume\": \"IPLParmVolume\",\n" +
            "    \"osFriendlyName\": \"DynamicLNKLSTName\"\n" +
            "  },\n" +
            "  \"CICSRegion\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"label\",\n" +
            "    \"regionName\": \"JobName\",\n" +
            "    \"applicationId\": \"ApplID\",\n" +
            "    \"netId\": \"NetID\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"jobName\": \"JobName\",\n" +
            "    \"cicsVersion\": \"VersionString\"\n" +
            "  },\n" +
            "  \"CICSPlex\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"name\": \"CICSPlexName\",\n" +
            "    \"mvsSysId\": \"CICSPlexmvssysid\"\n" +
            "  },\n" +
            "  \"CICSProgram\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"label\",\n" +
            "    \"programName\": \"Name\",\n" +
            "    \"programLanguage\": \"LanguageDefined\",\n" +
            "    \"programDataLocation\": \"DataLoc\",\n" +
            "    \"programExecutionKey\": \"ExecKey\"\n" +
            "  },\n" +
            "  \"CICSTransaction\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"label\",\n" +
            "    \"transactionName\": \"Name\",\n" +
            "    \"dataKey\": \"DataKey\",\n" +
            "    \"dataLocation\": \"DataLocation\",\n" +
            "    \"initialProgram\": \"InitialProgram\"\n" +
            "  },\n" +
            "  \"CICSFile\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"label\",\n" +
            "    \"fileName\": \"DDName\",\n" +
            "    \"datasetName\": \"Datasets\"\n" +
            "  },\n" +
            "  \"DB2DataSharingGroup\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"groupAttachName\": \"GroupAttachName\",\n" +
            "    \"groupFunction\": \"GroupFunction\",\n" +
            "    \"sysDatabaseMaxAlteredTs\": \"SysDatabaseMaxAlteredTs\",\n" +
            "    \"sysTableSpaceMaxAlteredTs\": \"SysTableSpaceMaxAlteredTs\",\n" +
            "    \"sysTableMaxAlteredTs\": \"SysTableMaxAlteredTs\",\n" +
            "    \"sysIndexesMaxAlteredTs\": \"SysIndexesMaxAlteredTs\",\n" +
            "    \"sysColumnsMaxAlteredTs\": \"SysColumnsMaxAlteredTs\"\n" +
            "  },\n" +
            "  \"DB2Subsystem\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"keyName\": \"KeyName\",\n" +
            "    \"subSystemName\": \"SubsystemName\",\n" +
            "    \"commandPrefixName\": \"CommandPrefixName\",\n" +
            "    \"controllingAddressSpace\": \"ControllingAddressSpace\",\n" +
            "    \"DDFLocation\": \"DDFLocation\",\n" +
            "    \"versionString\": \"versionString\"\n" +
            "  },\n" +
            "  \"DB2Database\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\"\n" +
            "  },\n" +
            "  \"DB2TableSpace\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"size\": \"Size\",\n" +
            "    \"pageSize\": \"PageSize\",\n" +
            "    \"spaceId\": \"SpaceId\",\n" +
            "    \"contentType\": \"ContentType\",\n" +
            "    \"comments\": \"Comments\"\n" +
            "  },\n" +
            "  \"DB2Table\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"tableSpace\": \"TableSpace\",\n" +
            "    \"dataBase\": \"DataBase\"\n" +
            "  },\n" +
            "  \"DB2BufferPool\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"numPages\": \"NumPages\",\n" +
            "    \"pageSize\": \"PageSize\",\n" +
            "    \"poolId\": \"PoolId\"\n" +
            "  },\n" +
            "  \"DB2StoredProcedure\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"routineType\": \"RoutineType\",\n" +
            "    \"origin\": \"Origin\",\n" +
            "    \"specificName\": \"SpecificName\",\n" +
            "    \"externalName\": \"ExternalName\",\n" +
            "    \"collId\": \"Collid\",\n" +
            "    \"language\": \"Language\"\n" +
            "  },\n" +
            "  \"AddressSpace\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"jobName\": \"JobName\",\n" +
            "    \"jobType\": \"JobType\",\n" +
            "    \"stepName\": \"StepName\"\n" +
            "  },\n" +
            "  \"BindAddress\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"path\": \"Path\",\n" +
            "    \"portNumber\": \"PortNumber\"\n" +
            "  },\n" +
            "  \"Fqdn\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"fqdn\": \"Fqdn\"\n" +
            "  },\n" +
            "  \"IpAddress\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"stringNotation\": \"StringNotation\"\n" +
            "  },\n" +
            "  \"IpInterface\": {\n" +
            "    \"dlaId\": \"id\"\n" +
            "  },\n" +
            "  \"ProcessPool\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"cmdLine\": \"CmdLine\"\n" +
            "  },\n" +
            "  \"TcpPort\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"portNumber\": \"PortNumber\"\n" +
            "  },\n" +
            "  \"UdpPort\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"portNumber\": \"PortNumber\"\n" +
            "  },\n" +
            " \"MQQueueSharingGroup\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"groupFunction\": \"GroupFunction\"\n" +
            "  },\n" +
            "  \"MQSubsystem\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"subsystemName\": \"SubsystemName\",\n" +
            "    \"commandPrefixName\": \"CommandPrefixName\",\n" +
            "    \"controllingAddressSpace\": \"ControllingAddressSpace\",\n" +
            "    \"versionString\": \"VersionString\"\n" +
            "  },\n" +
            "  \"MQAliasQueue\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"description\": \"Description\",\n" +
            "    \"defaultPersistence\": \"DefaultPersistence\",\n" +
            "    \"get\": \"Get\",\n" +
            "    \"put\": \"Put\",\n" +
            "    \"qsgdisp\": \"QSGDISP\",\n" +
            "    \"targetQueue\": \"TargetQueue\"\n" +
            "  },\n" +
            "  \"MQAuthInfo\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"type\": \"Type\",\n" +
            "    \"userName\": \"UserName\",\n" +
            "    \"ldapServerName\": \"LDAPServerName\",\n" +
            "    \"queueManager\": \"QueueManager\"\n" +
            "  },\n" +
            "  \"MQBufferPool\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"idSequence\": \"IdSequence\",\n" +
            "    \"number\": \"Number\"\n" +
            "  },\n" +
            "  \"MQClientConnectionChannel\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"queueSharingGroupDisposition\": \"QueueSharingGroupDisposition\",\n" +
            "    \"headerCompression\": \"HeaderCompression\",\n" +
            "    \"messageCompression\": \"MessageCompression\",\n" +
            "    \"heartbeatInterval\": \"HeartbeatInterval\",\n" +
            "    \"keepAliveInterval\": \"KeepAliveInterval\",\n" +
            "    \"maxMessageLength\": \"MaxMessageLength\"\n" +
            "  },\n" +
            "  \"MQClusterReceiverChannel\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"queueSharingGroupDisposition\": \"QueueSharingGroupDisposition\",\n" +
            "    \"connectionName\": \"ConnectionName\",\n" +
            "    \"clwlChannelWeight\": \"CLWLChannelWeight\",\n" +
            "    \"clwlChannelPriority\": \"CLWLChannelPriority\",\n" +
            "    \"clwlChannelRank\": \"CLWLChannelRank\",\n" +
            "    \"dataConversion\": \"DataConversion\",\n" +
            "    \"longRetryTimer\": \"LongRetryTimer\",\n" +
            "    \"longRetryCount\": \"LongRetryCount\",\n" +
            "    \"messageRetryCount\": \"MessageRetryCount\",\n" +
            "    \"messageRetryInterval\": \"MessageRetryInterval\",\n" +
            "    \"putAuthority\": \"PutAuthority\",\n" +
            "    \"shortRetryTimer\": \"ShortRetryTimer\",\n" +
            "    \"shortRetryCount\": \"ShortRetryCount\",\n" +
            "    \"batchSize\": \"BatchSize\",\n" +
            "    \"batchInterval\": \"BatchInterval\",\n" +
            "    \"batchHeartbeatInterval\": \"BatchHeartbeatInterval\",\n" +
            "    \"headerCompression\": \"HeaderCompression\",\n" +
            "    \"messageCompression\": \"MessageCompression\",\n" +
            "    \"disconnectInterval\": \"DisconnectInterval\",\n" +
            "    \"heartbeatInterval\": \"HeartbeatInterval\",\n" +
            "    \"keepAliveInterval\": \"KeepAliveInterval\",\n" +
            "    \"maxMessageLength\": \"MaxMessageLength\",\n" +
            "    \"mcaType\": \"MCAType\",\n" +
            "    \"nonPersistentMessageSpeed\": \"NonPersistentMessageSpeed\",\n" +
            "    \"sslClientAuthentication\": \"SSLClientAuthentication\"\n" +
            "  },\n" +
            "  \"MQClusterSenderChannel\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"queueSharingGroupDisposition\": \"QueueSharingGroupDisposition\",\n" +
            "    \"connectionName\": \"ConnectionName\",\n" +
            "    \"clwlChannelWeight\": \"CLWLChannelWeight\",\n" +
            "    \"clwlChannelPriority\": \"CLWLChannelPriority\",\n" +
            "    \"clwlChannelRank\": \"CLWLChannelRank\",\n" +
            "    \"dataConversion\": \"DataConversion\",\n" +
            "    \"longRetryTimer\": \"LongRetryTimer\",\n" +
            "    \"longRetryCount\": \"LongRetryCount\",\n" +
            "    \"shortRetryTimer\": \"ShortRetryTimer\",\n" +
            "    \"shortRetryCount\": \"ShortRetryCount\",\n" +
            "    \"batchSize\": \"BatchSize\",\n" +
            "    \"batchInterval\": \"BatchInterval\",\n" +
            "    \"batchHeartbeatInterval\": \"BatchHeartbeatInterval\",\n" +
            "    \"headerCompression\": \"HeaderCompression\",\n" +
            "    \"messageCompression\": \"MessageCompression\",\n" +
            "    \"disconnectInterval\": \"DisconnectInterval\",\n" +
            "    \"heartbeatInterval\": \"HeartbeatInterval\",\n" +
            "    \"keepAliveInterval\": \"KeepAliveInterval\",\n" +
            "    \"maxMessageLength\": \"MaxMessageLength\",\n" +
            "    \"nonPersistentMessageSpeed\": \"NonPersistentMessageSpeed\"\n" +
            "  },\n" +
            "  \"MQLocalQueue\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"get\": \"Get\",\n" +
            "    \"put\": \"Put\",\n" +
            "    \"definitionType\": \"DefinitionType\",\n" +
            "    \"transmissionUsage\": \"TransmissionUsage\",\n" +
            "    \"maxMessageLength\": \"MaxMessageLength\",\n" +
            "    \"maxQueueDepth\": \"MaxQueueDepth\",\n" +
            "    \"triggerControl\": \"TriggerControl\",\n" +
            "    \"triggerData\": \"TriggerData\",\n" +
            "    \"triggerDepth\": \"TriggerDepth\",\n" +
            "    \"triggerType\": \"TriggerType\",\n" +
            "    \"defaultPersistence\": \"DefaultPersistence\",\n" +
            "    \"qsgdisp\": \"QSGDISP\"\n" +
            "  },\n" +
            "  \"MQModelQueue\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"initiationQueue\": \"InitiationQueue\",\n" +
            "    \"description\": \"Description\",\n" +
            "    \"get\": \"Get\",\n" +
            "    \"put\": \"Put\",\n" +
            "    \"definitionType\": \"DefinitionType\",\n" +
            "    \"transmissionUsage\": \"TransmissionUsage\",\n" +
            "    \"triggerControl\": \"TriggerControl\",\n" +
            "    \"triggerData\": \"TriggerData\",\n" +
            "    \"triggerDepth\": \"TriggerDepth\",\n" +
            "    \"triggerType\": \"TriggerType\",\n" +
            "    \"defaultPersistence\": \"DefaultPersistence\",\n" +
            "    \"qsgdisp\": \"QSGDISP\"\n" +
            "  },\n" +
            "  \"MQReceiverChannel\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"queueSharingGroupDisposition\": \"QueueSharingGroupDisposition\",\n" +
            "    \"messageRetryCount\": \"MessageRetryCount\",\n" +
            "    \"messageRetryInterval\": \"MessageRetryInterval\",\n" +
            "    \"putAuthority\": \"PutAuthority\",\n" +
            "    \"batchSize\": \"BatchSize\",\n" +
            "    \"headerCompression\": \"HeaderCompression\",\n" +
            "    \"messageCompression\": \"MessageCompression\",\n" +
            "    \"heartbeatInterval\": \"HeartbeatInterval\",\n" +
            "    \"keepAliveInterval\": \"KeepAliveInterval\",\n" +
            "    \"maxMessageLength\": \"MaxMessageLength\",\n" +
            "    \"nonPersistentMessageSpeed\": \"NonPersistentMessageSpeed\",\n" +
            "    \"sslClientAuthentication\": \"SSLClientAuthentication\",\n" +
            "    \"sslCipherSpecification\": \"SSLCipherSpecification\"\n" +
            "  },\n" +
            "  \"MQRemoteQueue\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"put\": \"Put\",\n" +
            "    \"qsgdisp\": \"QSGDISP\",\n" +
            "    \"remoteName\": \"RemoteName\",\n" +
            "    \"remoteQueueMgrName\": \"RemoteQueueMgrName\",\n" +
            "    \"defaultPersistence\": \"DefaultPersistence\"\n" +
            "  },\n" +
            "  \"MQSenderChannel\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"queueSharingGroupDisposition\": \"QueueSharingGroupDisposition\",\n" +
            "    \"connectionName\": \"ConnectionName\",\n" +
            "    \"dataConversion\": \"DataConversion\",\n" +
            "    \"longRetryTimer\": \"LongRetryTimer\",\n" +
            "    \"longRetryCount\": \"LongRetryCount\",\n" +
            "    \"shortRetryTimer\": \"ShortRetryTimer\",\n" +
            "    \"shortRetryCount\": \"ShortRetryCount\",\n" +
            "    \"batchSize\": \"BatchSize\",\n" +
            "    \"batchInterval\": \"BatchInterval\",\n" +
            "    \"batchHeartbeatInterval\": \"BatchHeartbeatInterval\",\n" +
            "    \"headerCompression\": \"HeaderCompression\",\n" +
            "    \"messageCompression\": \"MessageCompression\",\n" +
            "    \"disconnectInterval\": \"DisconnectInterval\",\n" +
            "    \"heartbeatInterval\": \"HeartbeatInterval\",\n" +
            "    \"keepAliveInterval\": \"KeepAliveInterval\",\n" +
            "    \"maxMessageLength\": \"MaxMessageLength\",\n" +
            "    \"nonPersistentMessageSpeed\": \"NonPersistentMessageSpeed\",\n" +
            "    \"transmissionQueue\": \"TransmissionQueue\"\n" +
            "  },\n" +
            "  \"MQServerConnectionChannel\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"queueSharingGroupDisposition\": \"QueueSharingGroupDisposition\",\n" +
            "    \"headerCompression\": \"HeaderCompression\",\n" +
            "    \"messageCompression\": \"MessageCompression\",\n" +
            "    \"disconnectInterval\": \"DisconnectInterval\",\n" +
            "    \"heartbeatInterval\": \"HeartbeatInterval\",\n" +
            "    \"keepAliveInterval\": \"KeepAliveInterval\",\n" +
            "    \"maxMessageLength\": \"MaxMessageLength\",\n" +
            "    \"sslClientAuthentication\": \"SSLClientAuthentication\",\n" +
            "    \"description\": \"Description\"\n" +
            "  },\n" +
            "  \"IMSSubsystem\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"keyName\": \"KeyName\",\n" +
            "    \"subsystemName\": \"SubsystemName\",\n" +
            "    \"versionString\": \"VersionString\",\n" +
            "    \"commandPrefixName\": \"CommandPrefixName\",\n" +
            "    \"controllingAddressSpace\": \"ControllingAddressSpace\",\n" +
            "    \"IMSSubsysType\": \"IMSSubsysType\",\n" +
            "    \"IMSPlexGroupName\": \"IMSPlexGroupName\",\n" +
            "    \"IRLMGroupName\": \"IRLMGroupName\",\n" +
            "    \"CQSGroupName\": \"CQSGroupName\",\n" +
            "    \"databasesChecksum\": \"DatabasesChecksum\",\n" +
            "    \"programsChecksum\": \"ProgramsChecksum\",\n" +
            "    \"transactionsChecksum\": \"TransactionsChecksum\"\n" +
            "  },\n" +
            "  \"IMSDatabase\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"IMSDatabaseType\": \"IMSDatabaseType\"\n" +
            "  },\n" +
            "  \"IMSProgram\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\"\n" +
            "  },\n" +
            "  \"IMSTransaction\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\"\n" +
            "  },\n" +
            "  \"IMSSysplexGroup\": {\n" +
            "    \"dlaId\": \"id\",\n" +
            "    \"label\": \"Label\",\n" +
            "    \"name\": \"Name\",\n" +
            "    \"groupFunction\": \"GroupFunction\"\n" +
            "  }" +
            "}";

    public VertexMapperProcessor() {
//        File file = new File("./VertexMapperJson.json");
//        if (file.canRead()) {
        try {
//            String content = FileUtil.readEntirely(file);
            JsonElement jsonElement = JsonParser.parseString(content);
            this.vmJson = jsonElement.getAsJsonObject();
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "VertexMapperProcessor", e.toString());
        }
//        }
    }

    /**
     * get property from source object and set it as target's property
     * the association of two side properties is defined by VertexMapper
     * @param source
     * @param target
     * @return
     */
    public Object mapFilter(Object source, Object target) {
        String className = target.getClass().getName();
        String[] strings = className.split("\\.");
        String keyName = strings[strings.length - 1];
        JsonObject mapper = vmJson.get(keyName).getAsJsonObject();
        for (String targetProperty : mapper.keySet()) {
            String sourceProperty = mapper.get(targetProperty).getAsString();
            try {
                Object value = ReflectUtil.getObjectValueByMethod(source, sourceProperty);
                // Only set the Nonempty value
                if (value != null) {
                    ReflectUtil.setObjectValueByMethod(target, targetProperty, value);
                }
            } catch (Exception e) {
                String  str = String.format(
                        "Attribute mapping failed: source - %, sourceProperty - %s, target - %s, targetProperty - %s.",
                        source.toString(), sourceProperty, target.toString(), targetProperty);
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "mapFilter", str);
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "mapFilter", e.toString());
            }
        }
        return target;
    }

    public static void main(String[] args) {
        VertexMapperProcessor vmProcessor = new VertexMapperProcessor();
        if (vmProcessor.vmJson != null) {
            System.out.println(vmProcessor.vmJson.toString());
        } else {
            System.out.println("vmJson is null!");
        }
    }
}
