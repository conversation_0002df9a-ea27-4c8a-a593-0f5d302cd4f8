/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.loader;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQAliasQueue;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQAuthInfo;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQBufferPool;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQClientConnectionChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQClusterReceiverChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQClusterSenderChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQLocalQueue;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQModelQueue;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQReceiverChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQRemoteQueue;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQSenderChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQServerConnectionChannel;
import com.ibm.palantir.catelyn.jaxb.SysZOSMQQueueSharingGroup;
import com.ibm.palantir.catelyn.jaxb.SysZOSMQSubsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQAliasQueueMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQAuthInfoMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQBufferPoolMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQClientConnectionChannelMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQClusterReceiverChannelMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQClusterSenderChannelMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQLocalQueueMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQModelQueueMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQQueueSharingGroupMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQReceiverChannelMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQRemoteQueueMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQSenderChannelMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQServerConnectionChannelMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQSubsystemMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQAliasQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQAuthInfo;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQBufferPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQClientConnectionChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQClusterReceiverChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQClusterSenderChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQLocalQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQModelQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQQueueSharingGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQReceiverChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQRemoteQueue;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQSenderChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQServerConnectionChannel;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQSubsystem;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAliasQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAuthInfoRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQBufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClientConnectionChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClusterReceiverChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClusterSenderChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQLocalQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQModelQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQQueueSharingGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQReceiverChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQRemoteQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSenderChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQServerConnectionChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQAliasQueueMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQAuthInfoMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQBufferPoolMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQClientConnectionChannelMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQClusterReceiverChannelMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQClusterSenderChannelMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQLocalQueueMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQModelQueueMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQQueueSharingGroupMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQReceiverChannelMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQRemoteQueueMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQSenderChannelMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQServerConnectionChannelMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQSubsystemMetaRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.utils.CommonUtils;
import com.ibm.palantir.sansa.utils.StringUtils;

public class MQElementLoader {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = MQElementLoader.class.getSimpleName();
    private static final String REPONAME = "Sansa";
    private final VertexMapperProcessor vmProcessor = new VertexMapperProcessor();
    private final StringUtils stringUtils = new StringUtils();

    private final MQSubsystemRepository mqSubsystemRepository;
    private final MQAliasQueueRepository mqAliasQueueRepository;
    private final MQAuthInfoRepository mqAuthInfoRepository;
    private final MQBufferPoolRepository mqBufferPoolRepository;
    private final MQLocalQueueRepository mqLocalQueueRepository;
    private final MQModelQueueRepository mqModelQueueRepository;
    private final MQRemoteQueueRepository mqRemoteQueueRepository;
    private final MQClientConnectionChannelRepository mqClientConnectionChannelRepository;
    private final MQClusterReceiverChannelRepository mqClusterReceiverChannelRepository;
    private final MQClusterSenderChannelRepository mqClusterSenderChannelRepository;
    private final MQReceiverChannelRepository mqReceiverChannelRepository;
    private final MQSenderChannelRepository mqSenderChannelRepository;
    private final MQServerConnectionChannelRepository mqServerConnectionChannelRepository;
    private final MQQueueSharingGroupRepository mqQueueSharingGroupRepository;

    private final MQSubsystemMetaRepository mqSubsystemMetaRepository;
    private final MQAliasQueueMetaRepository mqAliasQueueMetaRepository;
    private final MQAuthInfoMetaRepository mqAuthInfoMetaRepository;
    private final MQBufferPoolMetaRepository mqBufferPoolMetaRepository;
    private final MQLocalQueueMetaRepository mqLocalQueueMetaRepository;
    private final MQModelQueueMetaRepository mqModelQueueMetaRepository;
    private final MQRemoteQueueMetaRepository mqRemoteQueueMetaRepository;
    private final MQClientConnectionChannelMetaRepository mqClientConnectionChannelMetaRepository;
    private final MQClusterReceiverChannelMetaRepository mqClusterReceiverChannelMetaRepository;
    private final MQClusterSenderChannelMetaRepository mqClusterSenderChannelMetaRepository;
    private final MQReceiverChannelMetaRepository mqReceiverChannelMetaRepository;
    private final MQSenderChannelMetaRepository mqSenderChannelMetaRepository;
    private final MQServerConnectionChannelMetaRepository mqServerConnectionChannelMetaRepository;
    private final MQQueueSharingGroupMetaRepository mqQueueSharingGroupMetaRepository;
    private CommonUtils commonUtils;

    public MQElementLoader(MQSubsystemRepository mqSubsystemRepository, MQAliasQueueRepository mqAliasQueueRepository,
            MQAuthInfoRepository mqAuthInfoRepository,
            MQBufferPoolRepository mqBufferPoolRepository, MQLocalQueueRepository mqLocalQueueRepository,
            MQModelQueueRepository mqModelQueueRepository,
            MQRemoteQueueRepository mqRemoteQueueRepository,
            MQClientConnectionChannelRepository mqClientConnectionChannelRepository,
            MQClusterReceiverChannelRepository mqClusterReceiverChannelRepository,
            MQClusterSenderChannelRepository mqClusterSenderChannelRepository,
            MQReceiverChannelRepository mqReceiverChannelRepository,
            MQSenderChannelRepository mqSenderChannelRepository,
            MQServerConnectionChannelRepository mqServerConnectionChannelRepository,
            MQQueueSharingGroupRepository mqQueueSharingGroupRepository,
            MQSubsystemMetaRepository mqSubsystemMetaRepository, MQAliasQueueMetaRepository mqAliasQueueMetaRepository,
            MQAuthInfoMetaRepository mqAuthInfoMetaRepository,
            MQBufferPoolMetaRepository mqBufferPoolMetaRepository,
            MQLocalQueueMetaRepository mqLocalQueueMetaRepository,
            MQModelQueueMetaRepository mqModelQueueMetaRepository,
            MQRemoteQueueMetaRepository mqRemoteQueueMetaRepository,
            MQClientConnectionChannelMetaRepository mqClientConnectionChannelMetaRepository,
            MQClusterReceiverChannelMetaRepository mqClusterReceiverChannelMetaRepository,
            MQClusterSenderChannelMetaRepository mqClusterSenderChannelMetaRepository,
            MQReceiverChannelMetaRepository mqReceiverChannelMetaRepository,
            MQSenderChannelMetaRepository mqSenderChannelMetaRepository,
            MQServerConnectionChannelMetaRepository mqServerConnectionChannelMetaRepository,
            MQQueueSharingGroupMetaRepository mqQueueSharingGroupMetaRepository) {
        this.mqSubsystemRepository = mqSubsystemRepository;
        this.mqAliasQueueRepository = mqAliasQueueRepository;
        this.mqAuthInfoRepository = mqAuthInfoRepository;
        this.mqBufferPoolRepository = mqBufferPoolRepository;
        this.mqLocalQueueRepository = mqLocalQueueRepository;
        this.mqModelQueueRepository = mqModelQueueRepository;
        this.mqRemoteQueueRepository = mqRemoteQueueRepository;
        this.mqClientConnectionChannelRepository = mqClientConnectionChannelRepository;
        this.mqClusterReceiverChannelRepository = mqClusterReceiverChannelRepository;
        this.mqClusterSenderChannelRepository = mqClusterSenderChannelRepository;
        this.mqReceiverChannelRepository = mqReceiverChannelRepository;
        this.mqSenderChannelRepository = mqSenderChannelRepository;
        this.mqServerConnectionChannelRepository = mqServerConnectionChannelRepository;
        this.mqQueueSharingGroupRepository = mqQueueSharingGroupRepository;

        this.mqSubsystemMetaRepository = mqSubsystemMetaRepository;
        this.mqAliasQueueMetaRepository = mqAliasQueueMetaRepository;
        this.mqAuthInfoMetaRepository = mqAuthInfoMetaRepository;
        this.mqBufferPoolMetaRepository = mqBufferPoolMetaRepository;
        this.mqLocalQueueMetaRepository = mqLocalQueueMetaRepository;
        this.mqModelQueueMetaRepository = mqModelQueueMetaRepository;
        this.mqRemoteQueueMetaRepository = mqRemoteQueueMetaRepository;
        this.mqClientConnectionChannelMetaRepository = mqClientConnectionChannelMetaRepository;
        this.mqClusterReceiverChannelMetaRepository = mqClusterReceiverChannelMetaRepository;
        this.mqClusterSenderChannelMetaRepository = mqClusterSenderChannelMetaRepository;
        this.mqReceiverChannelMetaRepository = mqReceiverChannelMetaRepository;
        this.mqSenderChannelMetaRepository = mqSenderChannelMetaRepository;
        this.mqServerConnectionChannelMetaRepository = mqServerConnectionChannelMetaRepository;
        this.mqQueueSharingGroupMetaRepository = mqQueueSharingGroupMetaRepository;
        this.commonUtils = new CommonUtils();
    }

    public Object loadMQQueueSharingGroup(SysZOSMQQueueSharingGroup sysZOSMQQSGElement, String prefixId,
            String scanDate, boolean isMeta) {
        if (isMeta) {
            SysZOSMQQueueSharingGroup sysZOSMQQueueSharingGroup = (SysZOSMQQueueSharingGroup) sysZOSMQQSGElement;
            MQQueueSharingGroupMeta mqQueueSharingGroup = new MQQueueSharingGroupMeta();
            mqQueueSharingGroup.setPrefixId(prefixId);
            mqQueueSharingGroup.setScanDate(scanDate);
            mqQueueSharingGroup.setId(stringUtils.composeUniqueId(sysZOSMQQueueSharingGroup.getId(), prefixId));
            mqQueueSharingGroup.setDlaId(sysZOSMQQueueSharingGroup.getId());
            try {
                return mqQueueSharingGroupMetaRepository.save(mqQueueSharingGroup);
            } catch (Exception e) {
                return logException("MQQueueSharingGroupMeta", mqQueueSharingGroup.getDlaId(), e);
            }
        } else {
            SysZOSMQQueueSharingGroup sysZOSMQQueueSharingGroup = (SysZOSMQQueueSharingGroup) sysZOSMQQSGElement;
            MQQueueSharingGroup mqQueueSharingGroup = new MQQueueSharingGroup();
            vmProcessor.mapFilter(sysZOSMQQueueSharingGroup, mqQueueSharingGroup);
            mqQueueSharingGroup.setPrefixId(prefixId);
            mqQueueSharingGroup.setScanDate(scanDate);
            mqQueueSharingGroup.setId(stringUtils.composeUniqueId(sysZOSMQQueueSharingGroup.getId(), prefixId));
            mqQueueSharingGroup.setKafkaSendDate(Instant.now());
            try {
                return mqQueueSharingGroupRepository.save(mqQueueSharingGroup);
            } catch (Exception e) {
                return logException("DB2DataSharingGroup", mqQueueSharingGroup.getDlaId(), e);
            }
        }
    }

    public Object loadMQSubsystem(SysZOSMQSubsystem sysZOSMQSubsystem, String prefixId, String scanDate,
            boolean isMeta, List<String> ciIdentifierList) {
        if (isMeta) {
            MQSubsystemMeta mqSubsystem = new MQSubsystemMeta();
            mqSubsystem.setPrefixId(prefixId);
            mqSubsystem.setScanDate(scanDate);
            mqSubsystem.setId(stringUtils.composeUniqueId(sysZOSMQSubsystem.getId(), prefixId));
            mqSubsystem.setDlaId(sysZOSMQSubsystem.getId());
            try {
                return mqSubsystemMetaRepository.save(mqSubsystem);
            } catch (Exception e) {
                return logException("MQSubsystemMeta", mqSubsystem.getDlaId(), e);
            }
        } else {
            MQSubsystem mqSubsystem = new MQSubsystem();
            vmProcessor.mapFilter(sysZOSMQSubsystem, mqSubsystem);
            mqSubsystem.setPrefixId(prefixId);
            mqSubsystem.setScanDate(scanDate);
            mqSubsystem.setId(stringUtils.composeUniqueId(sysZOSMQSubsystem.getId(), prefixId));
            mqSubsystem.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 7, sysZOSMQSubsystem.getSubsystemName(),
                    sysZOSMQSubsystem.getId());
            mqSubsystem.setCiIdentifier(String.join("|", ciIdentifierList));
            try {
                return mqSubsystemRepository.save(mqSubsystem);
            } catch (Exception e) {
                return logException("MQSubsystem", mqSubsystem.getDlaId(), e);
            }
        }
    }

    public List<String> loadMQAliasQueueList(List<Object> appMQAQElementList, String prefixId, String scanDate,
            boolean isMeta, List<String> ciIdentifierList, String mqSubSystemName, String mqSubSystemId) {
        if (isMeta) {
            return saveMQAliasQueueMetaList(createMQAliasQueueMetaList(appMQAQElementList, prefixId, scanDate));
        } else {
            return saveMQAliasQueueList(createMQAliasQueueList(appMQAQElementList, prefixId, scanDate, ciIdentifierList,
                    mqSubSystemName, mqSubSystemId));
        }
    }

    private List<MQAliasQueue> createMQAliasQueueList(List<Object> appMQAQElementList, String prefixId,
            String scanDate, List<String> ciIdentifierList, String mqSubSystemName, String mqSubSystemId) {
        List<MQAliasQueue> mqAliasQueueList = new ArrayList<>();

        for (Object appMQAQElement : appMQAQElementList) {
            AppMessagingMqMQAliasQueue messagingMqMQAliasQueue = (AppMessagingMqMQAliasQueue) appMQAQElement;
            MQAliasQueue mqAliasQueue = new MQAliasQueue();
            vmProcessor.mapFilter(messagingMqMQAliasQueue, mqAliasQueue);
            mqAliasQueue.setPrefixId(prefixId);
            mqAliasQueue.setScanDate(scanDate);
            mqAliasQueue.setId(stringUtils.composeUniqueId(messagingMqMQAliasQueue.getId(), prefixId));
            mqAliasQueue.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 7, mqSubSystemName, mqSubSystemId);
            mqAliasQueue.setCiIdentifier(String.join("|", ciIdentifierList));
            mqAliasQueueList.add(mqAliasQueue);
        }
        return mqAliasQueueList;
    }

    private List<MQAliasQueueMeta> createMQAliasQueueMetaList(List<Object> appMQAQElementList, String prefixId,
            String scanDate) {
        List<MQAliasQueueMeta> mqAliasQueueList = new ArrayList<>();

        for (Object appMQAQElement : appMQAQElementList) {
            AppMessagingMqMQAliasQueue messagingMqMQAliasQueue = (AppMessagingMqMQAliasQueue) appMQAQElement;
            MQAliasQueueMeta mqAliasQueue = new MQAliasQueueMeta();
            mqAliasQueue.setPrefixId(prefixId);
            mqAliasQueue.setScanDate(scanDate);
            mqAliasQueue.setId(stringUtils.composeUniqueId(messagingMqMQAliasQueue.getId(), prefixId));
            mqAliasQueue.setDlaId(messagingMqMQAliasQueue.getId());
            mqAliasQueueList.add(mqAliasQueue);
        }
        return mqAliasQueueList;
    }

    private List<String> saveMQAliasQueueList(List<MQAliasQueue> mqAliasQueueList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqAliasQueueRepository.saveAll(mqAliasQueueList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQAliasQueueList",
                    "Failed to saveAll mqAliasQueueList, try to save them one-by-one");
            for (MQAliasQueue mqaq : mqAliasQueueList) {
                try {
                    mqAliasQueueRepository.save(mqaq);
                } catch (Exception ie) {
                    catchException("MQAliasQueue", failedDlaIds, mqaq.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveMQAliasQueueMetaList(List<MQAliasQueueMeta> mqAliasQueueList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqAliasQueueMetaRepository.saveAll(mqAliasQueueList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQAliasQueueMetaList",
                    "Failed to saveAll mqAliasQueueList, try to save them one-by-one");
            for (MQAliasQueueMeta mqaq : mqAliasQueueList) {
                try {
                    mqAliasQueueMetaRepository.save(mqaq);
                } catch (Exception ie) {
                    catchException("MQAliasQueueMeta", failedDlaIds, mqaq.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadMQAuthInfoList(List<Object> appMQAIElementList, String prefixId, String scanDate,
            boolean isMeta) {
        if (isMeta) {

            return saveMQAuthInfoMetaList(createMQAuthInfoMetaList(appMQAIElementList, prefixId, scanDate));
        } else {

            return saveMQAuthInfoList(createMQAuthInfoList(appMQAIElementList, prefixId, scanDate));
        }
    }

    private List<MQAuthInfo> createMQAuthInfoList(List<Object> appMQAIElementList, String prefixId, String scanDate) {
        List<MQAuthInfo> mqAuthInfoList = new ArrayList<>();

        for (Object appMQAIElement : appMQAIElementList) {
            AppMessagingMqMQAuthInfo messagingMqMQAuthInfo = (AppMessagingMqMQAuthInfo) appMQAIElement;
            MQAuthInfo mqAuthInfo = new MQAuthInfo();
            vmProcessor.mapFilter(messagingMqMQAuthInfo, mqAuthInfo);
            mqAuthInfo.setPrefixId(prefixId);
            mqAuthInfo.setScanDate(scanDate);
            mqAuthInfo.setId(stringUtils.composeUniqueId(messagingMqMQAuthInfo.getId(), prefixId));
            mqAuthInfo.setKafkaSendDate(Instant.now());
            mqAuthInfoList.add(mqAuthInfo);
        }
        return mqAuthInfoList;
    }

    private List<MQAuthInfoMeta> createMQAuthInfoMetaList(List<Object> appMQAIElementList, String prefixId,
            String scanDate) {
        List<MQAuthInfoMeta> mqAuthInfoList = new ArrayList<>();

        for (Object appMQAIElement : appMQAIElementList) {
            AppMessagingMqMQAuthInfo messagingMqMQAuthInfo = (AppMessagingMqMQAuthInfo) appMQAIElement;
            MQAuthInfoMeta mqAuthInfo = new MQAuthInfoMeta();
            mqAuthInfo.setPrefixId(prefixId);
            mqAuthInfo.setScanDate(scanDate);
            mqAuthInfo.setId(stringUtils.composeUniqueId(messagingMqMQAuthInfo.getId(), prefixId));
            mqAuthInfo.setDlaId(messagingMqMQAuthInfo.getId());
            mqAuthInfoList.add(mqAuthInfo);
        }
        return mqAuthInfoList;
    }

    private List<String> saveMQAuthInfoList(List<MQAuthInfo> mqAuthInfoList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqAuthInfoRepository.saveAll(mqAuthInfoList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQAuthInfoList",
                    "Failed to saveAll mqAuthInfoList, try to save them one-by-one");
            for (MQAuthInfo mqai : mqAuthInfoList) {
                try {
                    mqAuthInfoRepository.save(mqai);
                } catch (Exception ie) {
                    catchException("MQAuthInfo", failedDlaIds, mqai.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveMQAuthInfoMetaList(List<MQAuthInfoMeta> mqAuthInfoList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqAuthInfoMetaRepository.saveAll(mqAuthInfoList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQAuthInfoMetaList",
                    "Failed to saveAll mqAuthInfoList, try to save them one-by-one");
            for (MQAuthInfoMeta mqai : mqAuthInfoList) {
                try {
                    mqAuthInfoMetaRepository.save(mqai);
                } catch (Exception ie) {
                    catchException("MQAuthInfoMeta", failedDlaIds, mqai.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadMQBufferPoolList(List<Object> appMQBPElementList, String prefixId, String scanDate,
            boolean isMeta) {
        if (isMeta) {
            return saveMQBufferPoolMetaList(createMQBufferPoolMetaList(appMQBPElementList, prefixId, scanDate));
        } else {
            return saveMQBufferPoolList(createMQBufferPoolList(appMQBPElementList, prefixId, scanDate));
        }
    }

    private List<MQBufferPool> createMQBufferPoolList(List<Object> appMQBPElementList, String prefixId,
            String scanDate) {
        List<MQBufferPool> mqBufferPoolList = new ArrayList<>();

        for (Object appMQBPElement : appMQBPElementList) {
            AppMessagingMqMQBufferPool messagingMqMQBufferPool = (AppMessagingMqMQBufferPool) appMQBPElement;
            MQBufferPool mqBufferPool = new MQBufferPool();
            vmProcessor.mapFilter(messagingMqMQBufferPool, mqBufferPool);
            mqBufferPool.setPrefixId(prefixId);
            mqBufferPool.setScanDate(scanDate);
            mqBufferPool.setId(stringUtils.composeUniqueId(messagingMqMQBufferPool.getId(), prefixId));
            mqBufferPool.setKafkaSendDate(Instant.now());
            mqBufferPoolList.add(mqBufferPool);
        }
        return mqBufferPoolList;
    }

    private List<MQBufferPoolMeta> createMQBufferPoolMetaList(List<Object> appMQBPElementList, String prefixId,
            String scanDate) {
        List<MQBufferPoolMeta> mqBufferPoolList = new ArrayList<>();

        for (Object appMQBPElement : appMQBPElementList) {
            AppMessagingMqMQBufferPool messagingMqMQBufferPool = (AppMessagingMqMQBufferPool) appMQBPElement;
            MQBufferPoolMeta mqBufferPool = new MQBufferPoolMeta();
            mqBufferPool.setPrefixId(prefixId);
            mqBufferPool.setScanDate(scanDate);
            mqBufferPool.setId(stringUtils.composeUniqueId(messagingMqMQBufferPool.getId(), prefixId));
            mqBufferPool.setDlaId(messagingMqMQBufferPool.getId());
            mqBufferPoolList.add(mqBufferPool);
        }
        return mqBufferPoolList;
    }

    private List<String> saveMQBufferPoolList(List<MQBufferPool> mqBufferPoolList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqBufferPoolRepository.saveAll(mqBufferPoolList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQBufferPoolList",
                    "Failed to saveAll mqBufferPoolList, try to save them one-by-one");
            for (MQBufferPool mqbp : mqBufferPoolList) {
                try {
                    mqBufferPoolRepository.save(mqbp);
                } catch (Exception ie) {
                    catchException("MQBufferPool", failedDlaIds, mqbp.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveMQBufferPoolMetaList(List<MQBufferPoolMeta> mqBufferPoolList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqBufferPoolMetaRepository.saveAll(mqBufferPoolList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQBufferPoolMetaList",
                    "Failed to saveAll mqBufferPoolList, try to save them one-by-one");
            for (MQBufferPoolMeta mqbp : mqBufferPoolList) {
                try {
                    mqBufferPoolMetaRepository.save(mqbp);
                } catch (Exception ie) {
                    catchException("MQBufferPoolMeta", failedDlaIds, mqbp.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadMQClientConnectionChannelList(List<Object> appMQCCCElementList, String prefixId,
            String scanDate, boolean isMeta) {
        if (isMeta) {
            return saveMQClientConnectionChannelMetaList(createMQClientConnectionChannelMetaList(
                    appMQCCCElementList, prefixId, scanDate));
        } else {
            return saveMQClientConnectionChannelList(createMQClientConnectionChannelList(
                    appMQCCCElementList, prefixId, scanDate));
        }
    }

    private List<MQClientConnectionChannel> createMQClientConnectionChannelList(List<Object> appMQCCCElementList,
            String prefixId, String scanDate) {
        List<MQClientConnectionChannel> mqClientConnectionChannelList = new ArrayList<>();

        for (Object appMQCCCElement : appMQCCCElementList) {
            AppMessagingMqMQClientConnectionChannel messagingMqMQClientConnectionChannel = (AppMessagingMqMQClientConnectionChannel) appMQCCCElement;
            MQClientConnectionChannel mqClientConnectionChannel = new MQClientConnectionChannel();
            vmProcessor.mapFilter(messagingMqMQClientConnectionChannel, mqClientConnectionChannel);
            mqClientConnectionChannel.setPrefixId(prefixId);
            mqClientConnectionChannel.setScanDate(scanDate);
            mqClientConnectionChannel
                    .setId(stringUtils.composeUniqueId(messagingMqMQClientConnectionChannel.getId(), prefixId));
            mqClientConnectionChannel.setKafkaSendDate(Instant.now());
            mqClientConnectionChannelList.add(mqClientConnectionChannel);
        }
        return mqClientConnectionChannelList;
    }

    private List<MQClientConnectionChannelMeta> createMQClientConnectionChannelMetaList(
            List<Object> appMQCCCElementList,
            String prefixId, String scanDate) {
        List<MQClientConnectionChannelMeta> mqClientConnectionChannelList = new ArrayList<>();

        for (Object appMQCCCElement : appMQCCCElementList) {
            AppMessagingMqMQClientConnectionChannel messagingMqMQClientConnectionChannel = (AppMessagingMqMQClientConnectionChannel) appMQCCCElement;
            MQClientConnectionChannelMeta mqClientConnectionChannel = new MQClientConnectionChannelMeta();
            mqClientConnectionChannel.setPrefixId(prefixId);
            mqClientConnectionChannel.setScanDate(scanDate);
            mqClientConnectionChannel
                    .setId(stringUtils.composeUniqueId(messagingMqMQClientConnectionChannel.getId(), prefixId));
            mqClientConnectionChannel.setDlaId(messagingMqMQClientConnectionChannel.getId());
            mqClientConnectionChannelList.add(mqClientConnectionChannel);
        }
        return mqClientConnectionChannelList;
    }

    private List<String> saveMQClientConnectionChannelMetaList(
            List<MQClientConnectionChannelMeta> mqClientConnectionChannelList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqClientConnectionChannelMetaRepository.saveAll(mqClientConnectionChannelList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQClientConnectionChannelMetaList",
                    "Failed to saveAll mqClientConnectionChannelList, try to save them one-by-one");
            for (MQClientConnectionChannelMeta mqccc : mqClientConnectionChannelList) {
                try {
                    mqClientConnectionChannelMetaRepository.save(mqccc);
                } catch (Exception ie) {
                    catchException("MQClientConnectionChannelMeta", failedDlaIds, mqccc.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveMQClientConnectionChannelList(
            List<MQClientConnectionChannel> mqClientConnectionChannelList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqClientConnectionChannelRepository.saveAll(mqClientConnectionChannelList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQClientConnectionChannelList",
                    "Failed to saveAll mqClientConnectionChannelList, try to save them one-by-one");
            for (MQClientConnectionChannel mqccc : mqClientConnectionChannelList) {
                try {
                    mqClientConnectionChannelRepository.save(mqccc);
                } catch (Exception ie) {
                    catchException("MQClientConnectionChannel", failedDlaIds, mqccc.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadMQClusterReceiverChannelList(List<Object> appMQCRCElementList, String prefixId,
            String scanDate, boolean isMeta) {
        if (isMeta) {
            List<MQClusterReceiverChannelMeta> mqClusterReceiverChannelList = createAppMessagingMqMQClusterReceiverChannelMetaList(
                    appMQCRCElementList, prefixId, scanDate);

            return saveAppMessagingMqMQClusterReceiverChannelMetaList(mqClusterReceiverChannelList);
        } else {
            List<MQClusterReceiverChannel> mqClusterReceiverChannelList = createAppMessagingMqMQClusterReceiverChannelList(
                    appMQCRCElementList, prefixId, scanDate);

            return saveAppMessagingMqMQClusterReceiverChannelList(mqClusterReceiverChannelList);
        }
    }

    private List<MQClusterReceiverChannelMeta> createAppMessagingMqMQClusterReceiverChannelMetaList(
            List<Object> appMQCRCElementList,
            String prefixId, String scanDate) {
        List<MQClusterReceiverChannelMeta> mqClusterReceiverChannelList = new ArrayList<>();

        for (Object appMQCRCElement : appMQCRCElementList) {
            AppMessagingMqMQClusterReceiverChannel messagingMqMQClusterReceiverChannel = (AppMessagingMqMQClusterReceiverChannel) appMQCRCElement;
            MQClusterReceiverChannelMeta mqClusterReceiverChannel = new MQClusterReceiverChannelMeta();
            mqClusterReceiverChannel.setPrefixId(prefixId);
            mqClusterReceiverChannel.setScanDate(scanDate);
            mqClusterReceiverChannel
                    .setId(stringUtils.composeUniqueId(messagingMqMQClusterReceiverChannel.getId(), prefixId));
            mqClusterReceiverChannel.setDlaId(messagingMqMQClusterReceiverChannel.getId());
            mqClusterReceiverChannelList.add(mqClusterReceiverChannel);
        }
        return mqClusterReceiverChannelList;
    }

    private List<MQClusterReceiverChannel> createAppMessagingMqMQClusterReceiverChannelList(
            List<Object> appMQCRCElementList,
            String prefixId, String scanDate) {
        List<MQClusterReceiverChannel> mqClusterReceiverChannelList = new ArrayList<>();

        for (Object appMQCRCElement : appMQCRCElementList) {
            AppMessagingMqMQClusterReceiverChannel messagingMqMQClusterReceiverChannel = (AppMessagingMqMQClusterReceiverChannel) appMQCRCElement;
            MQClusterReceiverChannel mqClusterReceiverChannel = new MQClusterReceiverChannel();
            vmProcessor.mapFilter(messagingMqMQClusterReceiverChannel, mqClusterReceiverChannel);
            mqClusterReceiverChannel.setPrefixId(prefixId);
            mqClusterReceiverChannel.setScanDate(scanDate);
            mqClusterReceiverChannel
                    .setId(stringUtils.composeUniqueId(messagingMqMQClusterReceiverChannel.getId(), prefixId));
            mqClusterReceiverChannel.setKafkaSendDate(Instant.now());
            mqClusterReceiverChannelList.add(mqClusterReceiverChannel);
        }
        return mqClusterReceiverChannelList;
    }

    private List<String> saveAppMessagingMqMQClusterReceiverChannelMetaList(
            List<MQClusterReceiverChannelMeta> mqClusterReceiverChannelList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqClusterReceiverChannelMetaRepository.saveAll(mqClusterReceiverChannelList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveAppMessagingMqMQClusterReceiverChannelMetaList",
                    "Failed to saveAll mqClusterReceiverChannelList, try to save them one-by-one");
            for (MQClusterReceiverChannelMeta mqcrc : mqClusterReceiverChannelList) {
                try {
                    mqClusterReceiverChannelMetaRepository.save(mqcrc);
                } catch (Exception ie) {
                    catchException("MQClusterReceiverChannelMeta", failedDlaIds, mqcrc.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveAppMessagingMqMQClusterReceiverChannelList(
            List<MQClusterReceiverChannel> mqClusterReceiverChannelList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqClusterReceiverChannelRepository.saveAll(mqClusterReceiverChannelList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveAppMessagingMqMQClusterReceiverChannelList",
                    "Failed to saveAll mqClusterReceiverChannelList, try to save them one-by-one");
            for (MQClusterReceiverChannel mqcrc : mqClusterReceiverChannelList) {
                try {
                    mqClusterReceiverChannelRepository.save(mqcrc);
                } catch (Exception ie) {
                    catchException("MQClusterReceiverChannelMeta", failedDlaIds, mqcrc.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadMQClusterSenderChannelList(List<Object> appMQCSCElementList, String prefixId,
            String scanDate, boolean isMeta) {
        if (isMeta) {
            return saveMQClusterSenderChannelMetaList(createMQClusterSenderChannelMetaList(
                    appMQCSCElementList, prefixId, scanDate));
        } else {
            return saveMQClusterSenderChannelList(createMQClusterSenderChannelList(appMQCSCElementList,
                    prefixId, scanDate));
        }
    }

    private List<MQClusterSenderChannelMeta> createMQClusterSenderChannelMetaList(List<Object> appMQCSCElementList,
            String prefixId, String scanDate) {
        List<MQClusterSenderChannelMeta> mqClusterSenderChannelList = new ArrayList<>();

        for (Object appMQCSCElement : appMQCSCElementList) {
            AppMessagingMqMQClusterSenderChannel messagingMqMQClusterSenderChannel = (AppMessagingMqMQClusterSenderChannel) appMQCSCElement;
            MQClusterSenderChannelMeta mqClusterSenderChannel = new MQClusterSenderChannelMeta();
            mqClusterSenderChannel.setPrefixId(prefixId);
            mqClusterSenderChannel.setScanDate(scanDate);
            mqClusterSenderChannel
                    .setId(stringUtils.composeUniqueId(messagingMqMQClusterSenderChannel.getId(), prefixId));
            mqClusterSenderChannel.setDlaId(messagingMqMQClusterSenderChannel.getId());
            mqClusterSenderChannelList.add(mqClusterSenderChannel);
        }
        return mqClusterSenderChannelList;
    }

    private List<MQClusterSenderChannel> createMQClusterSenderChannelList(List<Object> appMQCSCElementList,
            String prefixId, String scanDate) {
        List<MQClusterSenderChannel> mqClusterSenderChannelList = new ArrayList<>();

        for (Object appMQCSCElement : appMQCSCElementList) {
            AppMessagingMqMQClusterSenderChannel messagingMqMQClusterSenderChannel = (AppMessagingMqMQClusterSenderChannel) appMQCSCElement;
            MQClusterSenderChannel mqClusterSenderChannel = new MQClusterSenderChannel();
            vmProcessor.mapFilter(messagingMqMQClusterSenderChannel, mqClusterSenderChannel);
            mqClusterSenderChannel.setPrefixId(prefixId);
            mqClusterSenderChannel.setScanDate(scanDate);
            mqClusterSenderChannel
                    .setId(stringUtils.composeUniqueId(messagingMqMQClusterSenderChannel.getId(), prefixId));
            mqClusterSenderChannel.setKafkaSendDate(Instant.now());
            mqClusterSenderChannelList.add(mqClusterSenderChannel);
        }
        return mqClusterSenderChannelList;
    }

    private List<String> saveMQClusterSenderChannelMetaList(
            List<MQClusterSenderChannelMeta> mqClusterSenderChannelList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqClusterSenderChannelMetaRepository.saveAll(mqClusterSenderChannelList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQClusterSenderChannelMetaList",
                    "Failed to saveAll mqClusterSenderChannelList, try to save them one-by-one");
            for (MQClusterSenderChannelMeta mqcsc : mqClusterSenderChannelList) {
                try {
                    mqClusterSenderChannelMetaRepository.save(mqcsc);
                } catch (Exception ie) {
                    catchException("MQClusterSenderChannelMeta", failedDlaIds, mqcsc.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveMQClusterSenderChannelList(List<MQClusterSenderChannel> mqClusterSenderChannelList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqClusterSenderChannelRepository.saveAll(mqClusterSenderChannelList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQClusterSenderChannelList",
                    "Failed to saveAll mqClusterSenderChannelList, try to save them one-by-one");
            for (MQClusterSenderChannel mqcsc : mqClusterSenderChannelList) {
                try {
                    mqClusterSenderChannelRepository.save(mqcsc);
                } catch (Exception ie) {
                    catchException("MQClusterSenderChannel", failedDlaIds, mqcsc.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadMQLocalQueueList(List<Object> appMQLQElementList, String prefixId, String scanDate,
            boolean isMeta, List<String> ciIdentifierList, String mqSubSystemName, String mqSubSystemId) {
        if (isMeta) {
            return saveMQLocalQueueMetaList(createMQLocalQueueMetaList(appMQLQElementList, prefixId, scanDate));
        } else {
            return saveMQLocalQueueList(
                    createMQLocalQueueList(appMQLQElementList, prefixId, scanDate, ciIdentifierList, mqSubSystemName,
                            mqSubSystemId));
        }
    }

    private List<MQLocalQueueMeta> createMQLocalQueueMetaList(List<Object> appMQLQElementList, String prefixId,
            String scanDate) {
        List<MQLocalQueueMeta> mqLocalQueueList = new ArrayList<>();

        for (Object appMQLQElement : appMQLQElementList) {
            AppMessagingMqMQLocalQueue messagingMqMQLocalQueue = (AppMessagingMqMQLocalQueue) appMQLQElement;
            MQLocalQueueMeta mqLocalQueue = new MQLocalQueueMeta();
            mqLocalQueue.setPrefixId(prefixId);
            mqLocalQueue.setScanDate(scanDate);
            mqLocalQueue.setId(stringUtils.composeUniqueId(messagingMqMQLocalQueue.getId(), prefixId));
            mqLocalQueue.setDlaId(messagingMqMQLocalQueue.getId());
            mqLocalQueueList.add(mqLocalQueue);
        }
        return mqLocalQueueList;
    }

    private List<MQLocalQueue> createMQLocalQueueList(List<Object> appMQLQElementList, String prefixId,
            String scanDate, List<String> ciIdentifierList, String mqSubSystemName, String mqSubSystemId) {
        List<MQLocalQueue> mqLocalQueueList = new ArrayList<>();

        for (Object appMQLQElement : appMQLQElementList) {
            AppMessagingMqMQLocalQueue messagingMqMQLocalQueue = (AppMessagingMqMQLocalQueue) appMQLQElement;
            MQLocalQueue mqLocalQueue = new MQLocalQueue();
            vmProcessor.mapFilter(messagingMqMQLocalQueue, mqLocalQueue);
            mqLocalQueue.setPrefixId(prefixId);
            mqLocalQueue.setScanDate(scanDate);
            mqLocalQueue.setId(stringUtils.composeUniqueId(messagingMqMQLocalQueue.getId(), prefixId));
            mqLocalQueue.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 7, mqSubSystemName, mqSubSystemId);
            mqLocalQueue.setCiIdentifier(String.join("|", ciIdentifierList));
            mqLocalQueueList.add(mqLocalQueue);
        }
        return mqLocalQueueList;
    }

    private List<String> saveMQLocalQueueMetaList(List<MQLocalQueueMeta> mqLocalQueueList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqLocalQueueMetaRepository.saveAll(mqLocalQueueList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQLocalQueueMetaList",
                    "Failed to saveAll mqLocalQueueList, try to save them one-by-one");
            for (MQLocalQueueMeta mqlq : mqLocalQueueList) {
                try {
                    mqLocalQueueMetaRepository.save(mqlq);
                } catch (Exception ie) {
                    catchException("MQLocalQueue", failedDlaIds, mqlq.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveMQLocalQueueList(List<MQLocalQueue> mqLocalQueueList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqLocalQueueRepository.saveAll(mqLocalQueueList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQLocalQueueList",
                    "Failed to saveAll mqLocalQueueList, try to save them one-by-one");
            for (MQLocalQueue mqlq : mqLocalQueueList) {
                try {
                    mqLocalQueueRepository.save(mqlq);
                } catch (Exception ie) {

                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadMQModelQueueList(List<Object> appMQMQElementList, String prefixId, String scanDate,
            boolean isMeta, List<String> ciIdentifierList, String mqSubSystemName, String mqSubSystemId) {
        if (isMeta) {
            return saveMQModelQueueMetaList(createMQModelQueueMetaList(appMQMQElementList, prefixId, scanDate));
        } else {
            return saveMQModelQueueList(
                    createMQModelQueueList(appMQMQElementList, prefixId, scanDate, ciIdentifierList, mqSubSystemName,
                            mqSubSystemId));
        }
    }

    private List<MQModelQueueMeta> createMQModelQueueMetaList(List<Object> appMQMQElementList, String prefixId,
            String scanDate) {
        List<MQModelQueueMeta> mqModelQueueList = new ArrayList<>();

        for (Object appMQMQElement : appMQMQElementList) {
            AppMessagingMqMQModelQueue messagingMqMQModelQueue = (AppMessagingMqMQModelQueue) appMQMQElement;
            MQModelQueueMeta mqModelQueue = new MQModelQueueMeta();
            mqModelQueue.setPrefixId(prefixId);
            mqModelQueue.setScanDate(scanDate);
            mqModelQueue.setId(stringUtils.composeUniqueId(messagingMqMQModelQueue.getId(), prefixId));
            mqModelQueue.setDlaId(messagingMqMQModelQueue.getId());
            mqModelQueueList.add(mqModelQueue);
        }
        return mqModelQueueList;
    }

    private List<MQModelQueue> createMQModelQueueList(List<Object> appMQMQElementList, String prefixId,
            String scanDate, List<String> ciIdentifierList, String mqSubSystemName, String mqSubSystemId) {
        List<MQModelQueue> mqModelQueueList = new ArrayList<>();

        for (Object appMQMQElement : appMQMQElementList) {
            AppMessagingMqMQModelQueue messagingMqMQModelQueue = (AppMessagingMqMQModelQueue) appMQMQElement;
            MQModelQueue mqModelQueue = new MQModelQueue();
            vmProcessor.mapFilter(messagingMqMQModelQueue, mqModelQueue);
            mqModelQueue.setPrefixId(prefixId);
            mqModelQueue.setScanDate(scanDate);
            mqModelQueue.setId(stringUtils.composeUniqueId(messagingMqMQModelQueue.getId(), prefixId));
            mqModelQueue.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 7, mqSubSystemName, mqSubSystemId);
            mqModelQueue.setCiIdentifier(String.join("|", ciIdentifierList));
            mqModelQueueList.add(mqModelQueue);
        }
        return mqModelQueueList;
    }

    private List<String> saveMQModelQueueMetaList(List<MQModelQueueMeta> mqModelQueueList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqModelQueueMetaRepository.saveAll(mqModelQueueList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQModelQueueMetaList",
                    "Failed to saveAll mqModelQueueList, try to save them one-by-one");
            for (MQModelQueueMeta mqmq : mqModelQueueList) {
                try {
                    mqModelQueueMetaRepository.save(mqmq);
                } catch (Exception ie) {
                    catchException("MQModelQueueMeta", failedDlaIds, mqmq.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveMQModelQueueList(List<MQModelQueue> mqModelQueueList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqModelQueueRepository.saveAll(mqModelQueueList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQModelQueueList",
                    "Failed to saveAll mqModelQueueList, try to save them one-by-one");
            for (MQModelQueue mqmq : mqModelQueueList) {
                try {
                    mqModelQueueRepository.save(mqmq);
                } catch (Exception ie) {
                    catchException("MQModelQueue", failedDlaIds, mqmq.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadMQReceiverChannelList(List<Object> appMQRCElementList, String prefixId, String scanDate,
            boolean isMeta) {
        if (isMeta) {
            return saveMQReceiverChannelMetaList(createMQReceiverChannelMetaList(appMQRCElementList, prefixId,
                    scanDate));
        } else {
            return saveMQReceiverChannelList(createMQReceiverChannelList(appMQRCElementList, prefixId,
                    scanDate));
        }
    }

    private List<MQReceiverChannelMeta> createMQReceiverChannelMetaList(List<Object> appMQRCElementList,
            String prefixId,
            String scanDate) {
        List<MQReceiverChannelMeta> mqReceiverChannelList = new ArrayList<>();

        for (Object appMQRCElement : appMQRCElementList) {
            AppMessagingMqMQReceiverChannel messagingMqMQReceiverChannel = (AppMessagingMqMQReceiverChannel) appMQRCElement;
            MQReceiverChannelMeta mqReceiverChannel = new MQReceiverChannelMeta();
            mqReceiverChannel.setPrefixId(prefixId);
            mqReceiverChannel.setScanDate(scanDate);
            mqReceiverChannel.setId(stringUtils.composeUniqueId(messagingMqMQReceiverChannel.getId(), prefixId));
            mqReceiverChannel.setDlaId(messagingMqMQReceiverChannel.getId());
            mqReceiverChannelList.add(mqReceiverChannel);
        }
        return mqReceiverChannelList;
    }

    private List<MQReceiverChannel> createMQReceiverChannelList(List<Object> appMQRCElementList, String prefixId,
            String scanDate) {
        List<MQReceiverChannel> mqReceiverChannelList = new ArrayList<>();

        for (Object appMQRCElement : appMQRCElementList) {
            AppMessagingMqMQReceiverChannel messagingMqMQReceiverChannel = (AppMessagingMqMQReceiverChannel) appMQRCElement;
            MQReceiverChannel mqReceiverChannel = new MQReceiverChannel();
            vmProcessor.mapFilter(messagingMqMQReceiverChannel, mqReceiverChannel);
            mqReceiverChannel.setPrefixId(prefixId);
            mqReceiverChannel.setScanDate(scanDate);
            mqReceiverChannel.setId(stringUtils.composeUniqueId(messagingMqMQReceiverChannel.getId(), prefixId));
            mqReceiverChannel.setKafkaSendDate(Instant.now());
            mqReceiverChannelList.add(mqReceiverChannel);
        }
        return mqReceiverChannelList;
    }

    private List<String> saveMQReceiverChannelMetaList(List<MQReceiverChannelMeta> mqReceiverChannelList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqReceiverChannelMetaRepository.saveAll(mqReceiverChannelList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQReceiverChannelMetaList",
                    "Failed to saveAll mqReceiverChannelList, try to save them one-by-one");
            for (MQReceiverChannelMeta mqrc : mqReceiverChannelList) {
                try {
                    mqReceiverChannelMetaRepository.save(mqrc);
                } catch (Exception ie) {
                    catchException("MQReceiverChannel", failedDlaIds, mqrc.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveMQReceiverChannelList(List<MQReceiverChannel> mqReceiverChannelList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqReceiverChannelRepository.saveAll(mqReceiverChannelList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQReceiverChannelList",
                    "Failed to saveAll mqReceiverChannelList, try to save them one-by-one");
            for (MQReceiverChannel mqrc : mqReceiverChannelList) {
                try {
                    mqReceiverChannelRepository.save(mqrc);
                } catch (Exception ie) {
                    catchException("MQReceiverChannel", failedDlaIds, mqrc.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadMQRemoteQueueList(List<Object> appMQRQElementList, String prefixId, String scanDate,
            boolean isMeta, List<String> ciIdentifierList, String mqSubSystemName, String mqSubSystemId) {
        if (isMeta) {
            return saveMQRemoteQueueMetaList(createMQRemoteQueueMetaList(appMQRQElementList, prefixId, scanDate));
        } else {
            return saveMQRemoteQueueList(
                    createMQRemoteQueueList(appMQRQElementList, prefixId, scanDate, ciIdentifierList, mqSubSystemName,
                            mqSubSystemId));
        }
    }

    private List<MQRemoteQueueMeta> createMQRemoteQueueMetaList(List<Object> appMQRQElementList, String prefixId,
            String scanDate) {
        List<MQRemoteQueueMeta> mqRemoteQueueList = new ArrayList<>();

        for (Object appMQRQElement : appMQRQElementList) {
            AppMessagingMqMQRemoteQueue messagingMqMQRemoteQueue = (AppMessagingMqMQRemoteQueue) appMQRQElement;
            MQRemoteQueueMeta mqRemoteQueue = new MQRemoteQueueMeta();
            mqRemoteQueue.setPrefixId(prefixId);
            mqRemoteQueue.setScanDate(scanDate);
            mqRemoteQueue.setId(stringUtils.composeUniqueId(messagingMqMQRemoteQueue.getId(), prefixId));
            mqRemoteQueue.setDlaId(messagingMqMQRemoteQueue.getId());
            mqRemoteQueueList.add(mqRemoteQueue);
        }
        return mqRemoteQueueList;
    }

    private List<MQRemoteQueue> createMQRemoteQueueList(List<Object> appMQRQElementList, String prefixId,
            String scanDate, List<String> ciIdentifierList, String mqSubSystemName, String mqSubSystemId) {
        List<MQRemoteQueue> mqRemoteQueueList = new ArrayList<>();

        for (Object appMQRQElement : appMQRQElementList) {
            AppMessagingMqMQRemoteQueue messagingMqMQRemoteQueue = (AppMessagingMqMQRemoteQueue) appMQRQElement;
            MQRemoteQueue mqRemoteQueue = new MQRemoteQueue();
            vmProcessor.mapFilter(messagingMqMQRemoteQueue, mqRemoteQueue);
            mqRemoteQueue.setPrefixId(prefixId);
            mqRemoteQueue.setScanDate(scanDate);
            mqRemoteQueue.setId(stringUtils.composeUniqueId(messagingMqMQRemoteQueue.getId(), prefixId));
            mqRemoteQueue.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 7, mqSubSystemName, mqSubSystemId);
            mqRemoteQueue.setCiIdentifier(String.join("|", ciIdentifierList));
            mqRemoteQueueList.add(mqRemoteQueue);
        }
        return mqRemoteQueueList;
    }

    private List<String> saveMQRemoteQueueList(List<MQRemoteQueue> mqRemoteQueueList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqRemoteQueueRepository.saveAll(mqRemoteQueueList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQRemoteQueueList",
                    "Failed to saveAll mqRemoteQueueList, try to save them one-by-one");
            for (MQRemoteQueue mqrq : mqRemoteQueueList) {
                try {
                    mqRemoteQueueRepository.save(mqrq);
                } catch (Exception ie) {
                    catchException("MQRemoteQueue", failedDlaIds, mqrq.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveMQRemoteQueueMetaList(List<MQRemoteQueueMeta> mqRemoteQueueList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqRemoteQueueMetaRepository.saveAll(mqRemoteQueueList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQRemoteQueueMetaList",
                    "Failed to saveAll mqRemoteQueueList, try to save them one-by-one");
            for (MQRemoteQueueMeta mqrq : mqRemoteQueueList) {
                try {
                    mqRemoteQueueMetaRepository.save(mqrq);
                } catch (Exception ie) {
                    catchException("MQRemoteQueueMeta", failedDlaIds, mqrq.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public List<String> loadMQSenderChannelList(List<Object> appMQSCElementList, String prefixId, String scanDate,
            boolean isMeta) {
        if (isMeta) {
            return saveMQSenderChannelMetaList(createMQSenderChannelMetaList(appMQSCElementList, prefixId,
                    scanDate));
        } else {
            return saveMQSenderChannelList(createMQSenderChannelList(appMQSCElementList, prefixId, scanDate));
        }
    }

    private List<MQSenderChannelMeta> createMQSenderChannelMetaList(List<Object> appMQSCElementList, String prefixId,
            String scanDate) {
        List<MQSenderChannelMeta> mqSenderChannelList = new ArrayList<>();

        for (Object appMQSCElement : appMQSCElementList) {
            AppMessagingMqMQSenderChannel messagingMqMQSenderChannel = (AppMessagingMqMQSenderChannel) appMQSCElement;
            MQSenderChannelMeta mqSenderChannel = new MQSenderChannelMeta();
            mqSenderChannel.setPrefixId(prefixId);
            mqSenderChannel.setScanDate(scanDate);
            mqSenderChannel.setId(stringUtils.composeUniqueId(messagingMqMQSenderChannel.getId(), prefixId));
            mqSenderChannel.setDlaId(messagingMqMQSenderChannel.getId());
            mqSenderChannelList.add(mqSenderChannel);
        }
        return mqSenderChannelList;
    }

    private List<MQSenderChannel> createMQSenderChannelList(List<Object> appMQSCElementList, String prefixId,
            String scanDate) {
        List<MQSenderChannel> mqSenderChannelList = new ArrayList<>();

        for (Object appMQSCElement : appMQSCElementList) {
            AppMessagingMqMQSenderChannel messagingMqMQSenderChannel = (AppMessagingMqMQSenderChannel) appMQSCElement;
            MQSenderChannel mqSenderChannel = new MQSenderChannel();
            vmProcessor.mapFilter(messagingMqMQSenderChannel, mqSenderChannel);
            mqSenderChannel.setPrefixId(prefixId);
            mqSenderChannel.setScanDate(scanDate);
            mqSenderChannel.setId(stringUtils.composeUniqueId(messagingMqMQSenderChannel.getId(), prefixId));
            mqSenderChannel.setKafkaSendDate(Instant.now());
            mqSenderChannelList.add(mqSenderChannel);
        }
        return mqSenderChannelList;
    }

    private List<String> saveMQSenderChannelMetaList(List<MQSenderChannelMeta> mqSenderChannelList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqSenderChannelMetaRepository.saveAll(mqSenderChannelList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQSenderChannelMetaList",
                    "Failed to saveAll mqSenderChannelList, try to save them one-by-one");
            for (MQSenderChannelMeta mqsc : mqSenderChannelList) {
                try {
                    mqSenderChannelMetaRepository.save(mqsc);
                } catch (Exception ie) {
                    catchException("MQSenderChannelMeta", failedDlaIds, mqsc.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveMQSenderChannelList(List<MQSenderChannel> mqSenderChannelList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqSenderChannelRepository.saveAll(mqSenderChannelList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQSenderChannelList",
                    "Failed to saveAll mqSenderChannelList, try to save them one-by-one");
            for (MQSenderChannel mqsc : mqSenderChannelList) {
                try {
                    mqSenderChannelRepository.save(mqsc);
                } catch (Exception ie) {
                    catchException("MQSenderChannel", failedDlaIds, mqsc.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;

    }

    public List<String> loadMQServerConnectionChannelList(List<Object> appMQSCCElementList, String prefixId,
            String scanDate, boolean isMeta) {
        if (isMeta) {
            return saveMQServerConnectionChannelMetaList(createMQServerConnectionChannelMetaList(
                    appMQSCCElementList, prefixId, scanDate));
        } else {
            return saveMQServerConnectionChannelList(createMQServerConnectionChannelList(
                    appMQSCCElementList, prefixId, scanDate));
        }
    }

    private List<MQServerConnectionChannelMeta> createMQServerConnectionChannelMetaList(
            List<Object> appMQSCCElementList,
            String prefixId, String scanDate) {
        List<MQServerConnectionChannelMeta> mqServerConnectionChannelList = new ArrayList<>();

        for (Object appMQSCCElement : appMQSCCElementList) {
            AppMessagingMqMQServerConnectionChannel messagingMqMQServerConnectionChannel = (AppMessagingMqMQServerConnectionChannel) appMQSCCElement;
            MQServerConnectionChannelMeta mqServerConnectionChannel = new MQServerConnectionChannelMeta();
            mqServerConnectionChannel.setPrefixId(prefixId);
            mqServerConnectionChannel.setScanDate(scanDate);
            mqServerConnectionChannel
                    .setId(stringUtils.composeUniqueId(messagingMqMQServerConnectionChannel.getId(), prefixId));
            mqServerConnectionChannel.setDlaId(messagingMqMQServerConnectionChannel.getId());
            mqServerConnectionChannelList.add(mqServerConnectionChannel);
        }
        return mqServerConnectionChannelList;
    }

    private List<MQServerConnectionChannel> createMQServerConnectionChannelList(List<Object> appMQSCCElementList,
            String prefixId, String scanDate) {
        List<MQServerConnectionChannel> mqServerConnectionChannelList = new ArrayList<>();

        for (Object appMQSCCElement : appMQSCCElementList) {
            AppMessagingMqMQServerConnectionChannel messagingMqMQServerConnectionChannel = (AppMessagingMqMQServerConnectionChannel) appMQSCCElement;
            MQServerConnectionChannel mqServerConnectionChannel = new MQServerConnectionChannel();
            vmProcessor.mapFilter(messagingMqMQServerConnectionChannel, mqServerConnectionChannel);
            mqServerConnectionChannel.setPrefixId(prefixId);
            mqServerConnectionChannel.setScanDate(scanDate);
            mqServerConnectionChannel
                    .setId(stringUtils.composeUniqueId(messagingMqMQServerConnectionChannel.getId(), prefixId));
            mqServerConnectionChannel.setKafkaSendDate(Instant.now());
            mqServerConnectionChannelList.add(mqServerConnectionChannel);
        }
        return mqServerConnectionChannelList;
    }

    private List<String> saveMQServerConnectionChannelMetaList(
            List<MQServerConnectionChannelMeta> mqServerConnectionChannelList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqServerConnectionChannelMetaRepository.saveAll(mqServerConnectionChannelList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQServerConnectionChannelMetaList",
                    "Failed to saveAll mqServerConnectionChannelList, try to save them one-by-one");
            for (MQServerConnectionChannelMeta mqscc : mqServerConnectionChannelList) {
                try {
                    mqServerConnectionChannelMetaRepository.save(mqscc);
                } catch (Exception ie) {
                    catchException("MQServerConnectionChannelMeta", failedDlaIds, mqscc.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<String> saveMQServerConnectionChannelList(
            List<MQServerConnectionChannel> mqServerConnectionChannelList) {
        // If it raises error return a list containing dlaId(s), else return empty list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            mqServerConnectionChannelRepository.saveAll(mqServerConnectionChannelList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveMQServerConnectionChannelList",
                    "Failed to saveAll mqServerConnectionChannelList, try to save them one-by-one");
            for (MQServerConnectionChannel mqscc : mqServerConnectionChannelList) {
                try {
                    mqServerConnectionChannelRepository.save(mqscc);
                } catch (Exception ie) {
                    catchException("MQServerConnectionChannel", failedDlaIds, mqscc.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private void catchException(String entityName, List<String> failedDlaIds, String dlaId, Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "catchException", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "catchException", ie.toString());
        failedDlaIds.add(dlaId);
    }

    private Object logException(String entityName, String dlaId, Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "logException", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "logException", ie.toString());
        return null;
    }

}
