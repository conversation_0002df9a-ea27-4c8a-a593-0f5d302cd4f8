/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.loader;

import java.lang.reflect.Field;
import java.text.MessageFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.RelationshipServiceNowMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.RelationshipServiceNow;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipServiceNowRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.RelationshipServiceNowMetaRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.utils.StringUtils;

// used for the single relationship solution for GBS-ServiceNow
public class RelationshipServiceNowLoader {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = RelationshipServiceNowLoader.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private String[][] EdgeMapperArray = new String[][] {
            { "virtualizes", "LPAR", "ZSeriesComputerSystem", "hasMember" },
            { "runsOn", "ZOS", "LPAR", "runs" },
            { "memberOf", "ZOS", "Sysplex", "hasMember" },
            { "hostedDependency", "DB2Subsystem", "ZOS", "contains" },
            { "contains", "Sysplex", "DB2DataSharingGroup", "" },
            { "federates", "DB2DataSharingGroup", "DB2Subsystem", "" },
            { "contains", "DB2Subsystem", "Db2Database", "" },
            { "contains", "DB2Subsystem", "Db2StoredProcedure", "" },
            { "runsOn", "CICSRegion", "ZOS", "contains" },
            { "contains", "CICSRegion", "CICSTransaction", "" },
            { "hostedDependency", "MQSubsystem", "ZOS", "contains" },
            { "manages", "MQSubsystem", "MQLocalQueue", "" },
            { "manages", "MQSubsystem", "MQRemoteQueue", "" },
            { "manages", "MQSubsystem", "MQAliasQueue", "" },
            { "manages", "MQSubsystem", "MQModelQueue", "" },
            { "hostedDependency", "IMSSubsystem", "ZOS", "contains" },
            { "contains", "IMSSubsystem", "IMSDatabase", "" },
            { "contains", "IMSSubsystem", "IMSTransaction", "" },
            { "contains", "Sysplex", "MQQueueSharingGroup", "" },
            { "federates", "MQQueueSharingGroup", "MQSubsystem", "" }, };
    private JsonObject CTMapJson;
    private String mapJsonContent = """
            {
              "ZSeriesComputerSystem": "z_series_computer",
              "LPAR": "lpar",
              "Sysplex": "sysplex",
              "ZOS": "zos",
              "CICSRegion": "cics_region",
              "CICSPlex": "cics_plex",
              "CICSProgram": "cics_program",
              "CICSTransaction": "cics_transaction",
              "CICSFile": "cics_file",
              "CICSSitOverrides": "cics_sit_overrides",
              "CICSSit": "cics_sit",
              "CICSDB2Conn": "cics_db2_conn",
              "DB2DataSharingGroup": "db2_data_sharing_group",
              "DB2Subsystem": "db2_subsystem",
              "Db2Database": "db2_database",
              "Db2TableSpace": "db2_table_space",
              "Db2Table": "db2_table",
              "Db2BufferPool": "db2_buffer_pool",
              "Db2StoredProcedure": "db2_stored_procedure",
              "AddressSpace": "address_space",
              "BindAddress": "bind_address",
              "Fqdn": "fqdn",
              "IpAddress": "ip_address",
              "IpInterface": "ip_interface",
              "ProcessPool": "process_pool",
              "TcpPort": "tcp_port",
              "UdpPort": "udp_port",
              "MQSubsystem": "mq_subsystem",
              "MQAliasQueue": "mq_alias_queue",
              "MQAuthInfo": "mq_auth_info",
              "MQBufferPool": "mq_buffer_pool",
              "MQClientConnectionChannel": "mq_client_connection_channel",
              "MQClusterReceiverChannel": "mq_cluster_receiver_channel",
              "MQClusterSenderChannel": "mq_cluster_sender_channel",
              "MQLocalQueue": "mq_local_queue",
              "MQModelQueue": "mq_model_queue",
              "MQReceiverChannel": "mq_receiver_channel",
              "MQRemoteQueue": "mq_remote_queue",
              "MQSenderChannel": "mq_sender_channel",
              "MQServerConnectionChannel": "mq_server_connection_channel",
              "MQQueueSharingGroup": "mq_queue_sharing_group",
              "MQListener": "mq_listener",
              "IMSSubsystem": "ims_subsystem",
              "IMSDatabase": "ims_database",
              "IMSProgram": "ims_program",
              "IMSSysplexGroup": "ims_sysplex_group",  "IMSTransaction": "ims_transaction",
              "JCLOperationData": "jcl_operation_data",
              "JCLDynamicData": "jcl_dynamic_data"
            }""";
    private RelationshipServiceNowRepository relationshipServiceNowRepository;
    private RelationshipServiceNowMetaRepository relationshipServiceNowMetaRepository;
    private StringUtils stringUtils = new StringUtils();

    public RelationshipServiceNowLoader(RelationshipServiceNowRepository relationshipServiceNowRepository,
            RelationshipServiceNowMetaRepository relationshipServiceNowMetaRepository) {
        this.relationshipServiceNowRepository = relationshipServiceNowRepository;
        this.relationshipServiceNowMetaRepository = relationshipServiceNowMetaRepository;

        try {
            JsonElement jsonElement = JsonParser.parseString(mapJsonContent);
            this.CTMapJson = jsonElement.getAsJsonObject();
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "RelationshipServiceNowLoader", e.toString());
        }
    }

    public void loadRelationshipServiceNowList(List<Object> currentElementList, List<String> filterIds,
            String cpcPrefix, String zosPrefix, String scanDate, boolean isMeta) {
        if (isMeta) {
            saveRelationshipsMeta(
                    createRelationshipServiceNowMetaList(currentElementList, filterIds, cpcPrefix, zosPrefix, isMeta,
                            scanDate));
        } else {
            List<RelationshipServiceNow> currentRelationshipList = createRelationshipServiceNowList(currentElementList,
                    filterIds, cpcPrefix, zosPrefix, isMeta, scanDate);
            saveRelationships(currentRelationshipList);
        }
    }

    private List<RelationshipServiceNow> createRelationshipServiceNowList(List<Object> elementList,
            List<String> filterIds, String cpcPrefix,
            String zosPrefix, boolean isMeta, String scanDate) {
        List<RelationshipServiceNow> rsSNList = new ArrayList<>();

        for (Object element : elementList) {
            String className = element.getClass().getName();
            String relationName = stringUtils.getRelationFromClassName(className);

            try {
                Class clazz = Class.forName(className);
                String source = "";
                String target = "";
                String sourceClazz = "";
                String targetClazz = "";
                String sourcePrefix = "";
                String targetPrefix = "";
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    String fieldName = field.getName();
                    field.setAccessible(true);
                    if (fieldName.equals("source")) {
                        source = (String) field.get(element);
                        sourceClazz = stringUtils.getClassTypeByDlaIdIgnoreProgram(source);
                        sourcePrefix = selectPrefix4DlaId(source, cpcPrefix, zosPrefix);
                    } else if (fieldName.equals("target")) {
                        target = (String) field.get(element);
                        targetClazz = stringUtils.getClassTypeByDlaIdIgnoreProgram(target);
                        targetPrefix = selectPrefix4DlaId(target, cpcPrefix, zosPrefix);
                    }
                }

                // Commenting this part of the code after discussion with Patrick | Failed id's
                // should be handled from SNOW side
                // Skip those filtered Items
                // if (filterIds.size() > 0 && (filterIds.contains(source) ||
                // filterIds.contains(target))) {
                // continue;
                // }

                Object result = createRelationship(relationName, source, target, sourceClazz, targetClazz, sourcePrefix,
                        targetPrefix, isMeta, scanDate);
                RelationshipServiceNow r = (RelationshipServiceNow) result;
                if (r != null) {
                    rsSNList.add(r);
                }
            } catch (ClassNotFoundException e) {
                logError(e, ErrorCode.PluginClassNotFoundError.getCodeStr(), "createRelationshipServiceNowList",
                        element);

            } catch (IllegalAccessException e) {
                logError(e, ErrorCode.PluginIllegalAccessError.getCodeStr(), "createRelationshipServiceNowList",
                        element);
            } catch (Exception e) {
                logError(e, ErrorCode.PluginError.getCodeStr(), "createRelationshipServiceNowList", element);
            }
        }
        return rsSNList;
    }

    private List<RelationshipServiceNowMeta> createRelationshipServiceNowMetaList(List<Object> elementList,
            List<String> filterIds, String cpcPrefix,
            String zosPrefix, boolean isMeta, String scanDate) {
        List<RelationshipServiceNowMeta> rsSNList = new ArrayList<>();

        for (Object element : elementList) {
            String className = element.getClass().getName();
            String relationName = stringUtils.getRelationFromClassName(className);

            try {
                Class clazz = Class.forName(className);
                String source = "";
                String target = "";
                String sourceClazz = "";
                String targetClazz = "";
                String sourcePrefix = "";
                String targetPrefix = "";
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    String fieldName = field.getName();
                    field.setAccessible(true);
                    if (fieldName.equals("source")) {
                        source = (String) field.get(element);
                        sourceClazz = stringUtils.getClassTypeByDlaIdIgnoreProgram(source);
                        sourcePrefix = selectPrefix4DlaId(source, cpcPrefix, zosPrefix);
                    } else if (fieldName.equals("target")) {
                        target = (String) field.get(element);
                        targetClazz = stringUtils.getClassTypeByDlaIdIgnoreProgram(target);
                        targetPrefix = selectPrefix4DlaId(target, cpcPrefix, zosPrefix);
                    }
                }

                // Commenting this part of the code as these filterIds are not considered for
                // relationship_service_now table
                // Refer createRelationshipServiceNowList function for more details
                // Skip those filtered Items
                // if (!filterIds.isEmpty() && (filterIds.contains(source) ||
                // filterIds.contains(target))) {
                // continue;
                // }

                Object result = createRelationship(relationName, source, target, sourceClazz, targetClazz,
                        sourcePrefix,
                        targetPrefix, isMeta, scanDate);
                RelationshipServiceNowMeta r = (RelationshipServiceNowMeta) result;
                if (r != null) {
                    rsSNList.add(r);
                }
            } catch (ClassNotFoundException e) {
                logError(e, ErrorCode.PluginClassNotFoundError.getCodeStr(), "createRelationshipServiceNowMetaList",
                        element);
            } catch (IllegalAccessException e) {
                logError(e, ErrorCode.PluginIllegalAccessError.getCodeStr(), "createRelationshipServiceNowMetaList",
                        element);
            } catch (Exception e) {
                logError(e, ErrorCode.PluginError.getCodeStr(), "createRelationshipServiceNowMetaList", element);
            }
        }
        return rsSNList;
    }

    // Generic method to log the exception
    private void logError(Exception e, String errCode, String methodName, Object element, Object... args) {
        LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "logError", "Exception: {}", e.toString());
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "logError", element.toString());
        String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", methodName, args);
        LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "logError", "Error in method {}: {}", methodName, msg);
    }

    public void saveRelationships(List<RelationshipServiceNow> rsSNList) {
        if (!rsSNList.isEmpty()) {
            relationshipServiceNowRepository.saveAll(rsSNList);
        }
    }

    private void saveRelationshipsMeta(List<RelationshipServiceNowMeta> rsSNList) {
        if (!rsSNList.isEmpty()) {
            relationshipServiceNowMetaRepository.saveAll(rsSNList);
        }
    }

    private Object createRelationship(String relationName, String source, String target, String sourceClazz,
            String targetClazz, String sourcePrefix, String targetPrefix, boolean isMeta, String scanDate) {
        if (isMeta) {
            // filter the relationships by EdgeMapper
            for (String[] mapperArray : EdgeMapperArray) {
                List<String> mapper = Arrays.asList(mapperArray);

                // only the relationship is included in the EdgeMapper, it can be stored.
                if (relationName.equals(mapper.get(0)) && source.contains(mapper.get(1))
                        && target.contains(mapper.get(2))) {
                    Object rsSN;
                    String reverseName = mapper.get(3);
                    if (!reverseName.equals("")) {
                        rsSN = buildRelationship(reverseName, target, source, targetClazz, sourceClazz, targetPrefix,
                                sourcePrefix, isMeta, scanDate);
                    } else {
                        rsSN = buildRelationship(relationName, source, target, sourceClazz, targetClazz, sourcePrefix,
                                targetPrefix, isMeta, scanDate);
                    }
                    return rsSN;
                }
            }
            return null;
        } else {
            // filter the relationships by EdgeMapper
            for (String[] mapperArray : EdgeMapperArray) {
                List<String> mapper = Arrays.asList(mapperArray);

                // only the relationship is included in the EdgeMapper, it can be stored.
                if (relationName.equals(mapper.get(0)) && source.contains(mapper.get(1))
                        && target.contains(mapper.get(2))) {
                    Object rsSN;
                    String reverseName = mapper.get(3);
                    if (!reverseName.equals("")) {
                        rsSN = buildRelationship(reverseName, target, source, targetClazz, sourceClazz, targetPrefix,
                                sourcePrefix, isMeta, scanDate);
                    } else {
                        rsSN = buildRelationship(relationName, source, target, sourceClazz, targetClazz, sourcePrefix,
                                targetPrefix, isMeta, scanDate);
                    }
                    return rsSN;
                }
            }
            return null;
        }
    }

    private Object buildRelationship(String name, String source, String target, String clazzFrom,
            String clazzTo, String sourcePrefix, String targetPrefix, boolean isMeta, String scanDate) {

        // Check if either the source or target class doesn't exist in CTMapJson
        // For example: PRSMLpar is not present and would return null
        // This check prevents a NullPointerException when accessing the mapping
        if (this.CTMapJson.get(clazzFrom) != null
                && this.CTMapJson.get(clazzTo) != null) {
            if (isMeta) {
                RelationshipServiceNowMeta rsSN = new RelationshipServiceNowMeta();
                rsSN.setName(name);
                rsSN.setSourceId(stringUtils.composeUniqueId(source, sourcePrefix));
                rsSN.setTargetId(stringUtils.composeUniqueId(target, targetPrefix));
                rsSN.setScanDate(scanDate);
                return rsSN;

            } else {
                RelationshipServiceNow rsSN = new RelationshipServiceNow();
                rsSN.setName(name);
                rsSN.setSourceId(stringUtils.composeUniqueId(source, sourcePrefix));
                rsSN.setTargetId(stringUtils.composeUniqueId(target, targetPrefix));
                rsSN.setSource(source);
                rsSN.setTarget(target);
                rsSN.setSourceType(this.CTMapJson.get(clazzFrom).getAsString());
                rsSN.setTargetType(this.CTMapJson.get(clazzTo).getAsString());
                rsSN.setSourcePrefix(sourcePrefix);
                rsSN.setTargetPrefix(targetPrefix);
                rsSN.setDataSource("DLA");
                rsSN.setScanDate(scanDate);
                rsSN.setKafkaSendDate(Instant.now());
                return rsSN;
            }
        }
        return null;
    }

    private List<String> cpcPrefixItems = Arrays.asList("ZSeriesComputerSystem", "LPAR");
    private List<String> emptyPrefixItems = Arrays.asList("Sysplex", "DB2DataSharingGroup", "IMSSysplexGroup",
            "MQQueueSharingGroup");

    private String selectPrefix4DlaId(String dlaId, String cpcPrefixId, String zosPrefixId) {
        String clazz = stringUtils.getClassTypeByDlaIdIgnoreProgram(dlaId);
        if (emptyPrefixItems.contains(clazz)) {
            return "";
        } else if (cpcPrefixItems.contains(clazz)) {
            return cpcPrefixId;
        } else {
            return zosPrefixId;
        }
    }

    public static void main(String[] args) {
        RelationshipServiceNowLoader relationshipServiceNowLoader = new RelationshipServiceNowLoader(null, null);
        if (relationshipServiceNowLoader.CTMapJson != null) {
            System.out.println(relationshipServiceNowLoader.CTMapJson.get("CICSSitOverrides"));
            System.out.println(relationshipServiceNowLoader.CTMapJson.get("MQServerConnectionChannel"));
            System.out.println(relationshipServiceNowLoader.CTMapJson.get("Db2Database"));
            System.out.println(relationshipServiceNowLoader.CTMapJson.get("Db2BufferPool"));
        } else {
            System.out.println("CTMapJson is null!");
        }
    }
}
