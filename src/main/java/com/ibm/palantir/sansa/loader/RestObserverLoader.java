/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.loader;

import java.util.HashMap;
import java.util.Map;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.ibm.palantir.catelyn.util.HttpUtils;

public class RestObserverLoader {

    private static final String topologyUrl = "/1.0/topology/types/edge";
    private static final String FileObserverJobUrl = "/1.0/rest-observer/jobs/listen";
    private static final String fileObserverRestUrl = "/1.0/rest-observer/rest/resources";

    private Map<String, String> headers;
    private String url;

    public RestObserverLoader(String url, String tenantID) {
        this.headers = new HashMap<>();
        this.headers.put("Accept", "application/json");
        this.headers.put("Content-Type", "application/json");
        this.headers.put("X-TenantID", tenantID);
        this.headers.put("Authorization", "Basic YXNtOmFzbQ==");
        this.url = url;
    }

    public void createType() throws Exception {
        String address = url + topologyUrl;
        String[] relationships = new String[]{
                "contains", "uses", "hasMember", "federates", "transactionalDependency", "connects",
                "memberOf", "runsOn", "virtualizes", "hostedDependency", "appliesTo", "runs"
        };
        for (String relationship : relationships) {
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("edgeType", "zDiscovery_" + relationship);
            jsonObject.addProperty("edgeLabel", "association");
            jsonObject.addProperty("description", relationship + " edge type in zDiscovery");
            HttpUtils.post(address, headers, null, jsonObject.toString(), true);
        }
    }


    public void createJob() throws Exception {
        String address = url + FileObserverJobUrl;
        String jobObject = "{\"unique_id\": \"sansa_job\", \"type\": \"listen\", \"description\": \"job for sansa\", \"parameters\": {\"provider\": \"MyProgram\"}, \"schedule\": {\"interval\": 0, \"units\": \"Days\", \"nextRunTime\": 0}}";
        HttpUtils.post(address, headers, null, jobObject, true);
    }


    public void triggerRest(JsonArray jsonArray) throws Exception {
        this.headers.put("JobId", "sansa_job");
        String address = url + fileObserverRestUrl;
        for (JsonElement jsonObject : jsonArray) {
            String body = jsonObject.toString();
            HttpUtils.post(address, headers, null, body, true);
        }
    }

}
