{"ZSeriesComputerSystem": "z_series_computer", "LPAR": "lpar", "Sysplex": "sysplex", "ZOS": "zos", "CICSRegion": "cics_region", "CICSPlex": "cics_plex", "CICSProgram": "cics_program", "CICSTransaction": "cics_transaction", "CICSFile": "cics_file", "CICSSitOverrides": "cics_sit_overrides", "CICSSit": "cics_sit", "CICSDB2Conn": "cics_db2_conn", "DB2DataSharingGroup": "db2_data_sharing_group", "DB2Subsystem": "db2_subsystem", "Db2Database": "db2_database", "Db2TableSpace": "db2_table_space", "Db2Table": "db2_table", "Db2BufferPool": "db2_buffer_pool", "Db2StoredProcedure": "db2_stored_procedure", "AddressSpace": "address_space", "BindAddress": "bind_address", "Fqdn": "fqdn", "IpAddress": "ip_address", "IpInterface": "ip_interface", "ProcessPool": "process_pool", "TcpPort": "tcp_port", "UdpPort": "udp_port", "MQSubsystem": "mq_subsystem", "MQAliasQueue": "mq_alias_queue", "MQAuthInfo": "mq_auth_info", "MQBufferPool": "mq_buffer_pool", "MQClientConnectionChannel": "mq_client_connection_channel", "MQClusterReceiverChannel": "mq_cluster_receiver_channel", "MQClusterSenderChannel": "mq_cluster_sender_channel", "MQLocalQueue": "mq_local_queue", "MQModelQueue": "mq_model_queue", "MQReceiverChannel": "mq_receiver_channel", "MQRemoteQueue": "mq_remote_queue", "MQSenderChannel": "mq_sender_channel", "MQServerConnectionChannel": "mq_server_connection_channel", "MQQueueSharingGroup": "mq_queue_sharing_group", "MQListener": "mq_listener", "IMSSubsystem": "ims_subsystem", "IMSDatabase": "ims_database", "IMSProgram": "ims_program", "IMSTransaction": "ims_transaction", "IMSSysplexGroup": "ims_sysplex_group", "JCLOperationData": "jcl_operation_data", "JCLDynamicData": "jcl_dynamic_data"}