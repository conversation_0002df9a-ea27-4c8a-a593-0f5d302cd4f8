/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.loader;

import java.time.Instant;
import java.util.List;

import com.ibm.palantir.catelyn.jaxb.SysZOSCICSPlex;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSRegion;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2Subsystem;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSSubsystem;
import com.ibm.palantir.catelyn.jaxb.SysZOSLPAR;
import com.ibm.palantir.catelyn.jaxb.SysZOSMQSubsystem;
import com.ibm.palantir.catelyn.jaxb.SysZOSSysplex;
import com.ibm.palantir.catelyn.jaxb.SysZOSZOS;
import com.ibm.palantir.catelyn.jaxb.SysZOSZSeriesComputerSystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CFComputerSystemMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CFLPARMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CICSPlexMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CICSRegionMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.LPARMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.PRSMLPARMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.SysplexMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.ZOSMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.ZSeriesComputerMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.ZSubSystemMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CFComputerSystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CFLPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSPlex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.LPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.PRSMLPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Sysplex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZOS;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSeriesComputer;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSubSystem;
import com.ibm.palantir.catelyn.jpa.repository.dla.CFComputerSystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CFLPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSPlexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSRegionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.LPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.PRSMLPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.SysplexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZOSRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSeriesComputerRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSubSystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CFComputerSystemMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CFLPARMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSPlexMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSRegionMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.LPARMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.PRSMLPARMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.RelationshipMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.SysplexMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.ZOSMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.ZSeriesComputerMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.ZSubSystemMetaRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.utils.CommonUtils;
import com.ibm.palantir.sansa.utils.StringUtils;

public class ElementLoader {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = ElementLoader.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private final StringUtils stringUtils = new StringUtils();
    private final VertexMapperProcessor vmProcessor = new VertexMapperProcessor();

    private final ZSeriesComputerRepository zSeriesComputerRepository;
    private final CFComputerSystemRepository cfComputerSystemRepository;
    private final LPARRepository lparRepository;
    private final CFLPARRepository cfLparRepository;
    private final PRSMLPARRepository prsmLparRepository;
    private final ZOSRepository zosRepository;
    private final SysplexRepository sysplexRepository;
    private final ZSubSystemRepository zSubSystemRepository;
    private final CICSRegionRepository cicsRegionRepository;
    private final CICSPlexRepository cicsPlexRepository;
    private final RelationshipLoader relationshipLoader;

    private final ZSeriesComputerMetaRepository zSeriesComputerMetaRepository;
    private final CFComputerSystemMetaRepository cfComputerSystemMetaRepository;
    private final LPARMetaRepository lparMetaRepository;
    private final CFLPARMetaRepository cfLparMetaRepository;
    private final PRSMLPARMetaRepository prsmLparMetaRepository;
    private final ZOSMetaRepository zosMetaRepository;
    private final SysplexMetaRepository sysplexMetaRepository;
    private final ZSubSystemMetaRepository zSubSystemMetaRepository;
    private final CICSRegionMetaRepository cicsRegionMetaRepository;
    private final CICSPlexMetaRepository cicsPlexMetaRepository;
    private CommonUtils commonUtils;

    public ElementLoader(ZSeriesComputerRepository zSeriesComputerRepository,
            CFComputerSystemRepository cfComputerSystemRepository,
            LPARRepository lparRepository, CFLPARRepository cfLparRepository, PRSMLPARRepository prsmLparRepository,
            ZOSRepository zosRepository, SysplexRepository sysplexRepository, ZSubSystemRepository zSubSystemRepository,
            CICSRegionRepository cicsRegionRepository, CICSPlexRepository cicsPlexRepository,
            RelationshipRepository relationshipRepository, ZSeriesComputerMetaRepository zSeriesComputerMetaRepository,
            CFComputerSystemMetaRepository cfComputerSystemMetaRepository,
            LPARMetaRepository lparMetaRepository, CFLPARMetaRepository cfLparMetaRepository,
            PRSMLPARMetaRepository prsmLparMetaRepository,
            ZOSMetaRepository zosMetaRepository, SysplexMetaRepository sysplexMetaRepository,
            ZSubSystemMetaRepository zSubSystemMetaRepository, CICSRegionMetaRepository cicsRegionMetaRepository,
            CICSPlexMetaRepository cicsPlexMetaRepository, RelationshipMetaRepository relationshipMetaRepository) {
        this.zSeriesComputerRepository = zSeriesComputerRepository;
        this.cfComputerSystemRepository = cfComputerSystemRepository;
        this.lparRepository = lparRepository;
        this.cfLparRepository = cfLparRepository;
        this.prsmLparRepository = prsmLparRepository;
        this.zosRepository = zosRepository;
        this.sysplexRepository = sysplexRepository;
        this.zSubSystemRepository = zSubSystemRepository;
        this.cicsRegionRepository = cicsRegionRepository;
        this.cicsPlexRepository = cicsPlexRepository;
        this.relationshipLoader = new RelationshipLoader(relationshipRepository, relationshipMetaRepository);

        this.zSeriesComputerMetaRepository = zSeriesComputerMetaRepository;
        this.cfComputerSystemMetaRepository = cfComputerSystemMetaRepository;
        this.lparMetaRepository = lparMetaRepository;
        this.cfLparMetaRepository = cfLparMetaRepository;
        this.prsmLparMetaRepository = prsmLparMetaRepository;
        this.zosMetaRepository = zosMetaRepository;
        this.sysplexMetaRepository = sysplexMetaRepository;
        this.zSubSystemMetaRepository = zSubSystemMetaRepository;
        this.cicsRegionMetaRepository = cicsRegionMetaRepository;
        this.cicsPlexMetaRepository = cicsPlexMetaRepository;
        this.commonUtils = new CommonUtils();
    }

    public Object loadZSeriesComputer(SysZOSZSeriesComputerSystem sysZOSZSeriesComputerSystem, String scanDate,
            boolean isMeta) {
        if (isMeta) {
            return saveZseriesComputerMeta(createZSeriesComputerMeta(sysZOSZSeriesComputerSystem, scanDate));
        } else {
            return saveZseriesComputer(createZSeriesComputer(sysZOSZSeriesComputerSystem, scanDate));
        }
    }

    private ZSeriesComputerMeta createZSeriesComputerMeta(SysZOSZSeriesComputerSystem sysZOSZSeriesComputerSystem,
            String scanDate) {
        ZSeriesComputerMeta zSeriesComputer = new ZSeriesComputerMeta();
        String prefixId = String.join("-", sysZOSZSeriesComputerSystem.getManufacturer(),
                sysZOSZSeriesComputerSystem.getModel(),
                sysZOSZSeriesComputerSystem.getSerialNumber());
        zSeriesComputer.setPrefixId(prefixId);
        zSeriesComputer.setScanDate(scanDate);
        zSeriesComputer.setDlaId(sysZOSZSeriesComputerSystem.getId());
        zSeriesComputer.setId(stringUtils.composeUniqueId(sysZOSZSeriesComputerSystem.getId(), prefixId));
        return zSeriesComputer;
    }

    private ZSeriesComputer createZSeriesComputer(SysZOSZSeriesComputerSystem sysZOSZSeriesComputerSystem,
            String scanDate) {
        ZSeriesComputer zSeriesComputer = new ZSeriesComputer();
        vmProcessor.mapFilter(sysZOSZSeriesComputerSystem, zSeriesComputer);
        String prefixId = String.join("-", zSeriesComputer.getManufacturer(), zSeriesComputer.getModel(),
                zSeriesComputer.getSerialNumber());
        zSeriesComputer.setPrefixId(prefixId);
        zSeriesComputer.setScanDate(scanDate);
        zSeriesComputer.setId(stringUtils.composeUniqueId(sysZOSZSeriesComputerSystem.getId(), prefixId));
        zSeriesComputer.setKafkaSendDate(Instant.now());
        return zSeriesComputer;
    }

    private Object saveZseriesComputerMeta(ZSeriesComputerMeta zSeriesComputer) {
        try {
            return zSeriesComputerMetaRepository.save(zSeriesComputer);
        } catch (Exception e) {
            return logException("ZSeriesComputerMeta", zSeriesComputer.getDlaId(), e);
        }
    }

    private Object saveZseriesComputer(ZSeriesComputer zSeriesComputer) {
        try {
            return zSeriesComputerRepository.save(zSeriesComputer);
        } catch (Exception e) {
            return logException("ZSeriesComputer", zSeriesComputer.getDlaId(), e);
        }
    }

    // The CFComputerSystem may have different info to the ZSeriesComputer, so it
    // should use itself prefixId.
    public Object loadCFComputerSystem(SysZOSZSeriesComputerSystem sysZOSZSeriesComputerSystem, String prefixId,
            String scanDate, boolean isMeta) {
        if (isMeta) {
            CFComputerSystemMeta cfComputerSystem = new CFComputerSystemMeta();
            cfComputerSystem.setPrefixId(prefixId);
            cfComputerSystem.setScanDate(scanDate);
            cfComputerSystem.setId(stringUtils.composeUniqueId(sysZOSZSeriesComputerSystem.getId(), prefixId));
            cfComputerSystem.setDlaId(sysZOSZSeriesComputerSystem.getId());
            return saveCfComputerSystemMeta(cfComputerSystem);
        } else {
            CFComputerSystem cfComputerSystem = new CFComputerSystem();
            vmProcessor.mapFilter(sysZOSZSeriesComputerSystem, cfComputerSystem);
            cfComputerSystem.setPrefixId(prefixId);
            cfComputerSystem.setScanDate(scanDate);
            cfComputerSystem.setId(stringUtils.composeUniqueId(sysZOSZSeriesComputerSystem.getId(), prefixId));
            cfComputerSystem.setKafkaSendDate(Instant.now());
            return saveCfComputerSystem(cfComputerSystem, sysZOSZSeriesComputerSystem.getId());
        }
    }

    private Object saveCfComputerSystemMeta(CFComputerSystemMeta cfComputerSystem) {
        try {
            return cfComputerSystemMetaRepository.save(cfComputerSystem);
        } catch (Exception e) {
            return logException("ZSeriesComputerMeta", cfComputerSystem.getDlaId(), e);
        }
    }

    private Object saveCfComputerSystem(CFComputerSystem cfComputerSystem, String dlaId) {
        try {
            return cfComputerSystemRepository.save(cfComputerSystem);
        } catch (Exception e) {
            return logException("ZSeriesComputer", dlaId, e);
        }
    }

    public Object loadLpar(SysZOSLPAR sysZOSLPAR, String scanDate, boolean isMeta, List<String> ciIdentifierList) {
        String prefixId = String.join("-", sysZOSLPAR.getManufacturer(), sysZOSLPAR.getModel(),
                sysZOSLPAR.getSerialNumber());
        if (isMeta) {
            LPARMeta lpar = new LPARMeta();
            lpar.setPrefixId(prefixId);
            lpar.setScanDate(scanDate);
            lpar.setLparName(sysZOSLPAR.getLPARName());
            lpar.setDlaId(sysZOSLPAR.getId());
            lpar.setId(stringUtils.composeUniqueId(sysZOSLPAR.getId(), prefixId));
            return saveLparMeta(lpar);
        } else {
            LPAR lpar = new LPAR();
            vmProcessor.mapFilter(sysZOSLPAR, lpar);
            lpar.setPrefixId(prefixId);
            lpar.setScanDate(scanDate);
            lpar.setId(stringUtils.composeUniqueId(sysZOSLPAR.getId(), prefixId));
            lpar.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 0, prefixId, lpar.getDlaId(), lpar.getLparName(), null,
                    null, null,
                    null, null, null);
            lpar.setCiIdentifier(String.join("|", ciIdentifierList));
            return saveLpar(lpar, sysZOSLPAR.getId());
        }
    }

    private Object saveLparMeta(LPARMeta lpar) {
        try {
            return lparMetaRepository.save(lpar);
        } catch (Exception e) {
            return logException("LPARMeta", lpar.getDlaId(), e);
        }
    }

    private Object saveLpar(LPAR lpar, String dlaId) {
        try {
            return lparRepository.save(lpar);
        } catch (Exception e) {
            return logException("LPAR", dlaId, e);
        }
    }

    public Object loadCFLpar(SysZOSLPAR sysZOSLPAR, String prefixId, String scanDate, boolean isMeta) {
        if (isMeta) {
            CFLPARMeta cflpar = new CFLPARMeta();
            cflpar.setPrefixId(prefixId);
            cflpar.setScanDate(scanDate);
            cflpar.setId(stringUtils.composeUniqueId(sysZOSLPAR.getId(), prefixId));
            cflpar.setDlaId(sysZOSLPAR.getId());
            return saveZseriesComputerMeta(cflpar);
        } else {
            CFLPAR cflpar = new CFLPAR();
            vmProcessor.mapFilter(sysZOSLPAR, cflpar);
            cflpar.setPrefixId(prefixId);
            cflpar.setScanDate(scanDate);
            cflpar.setId(stringUtils.composeUniqueId(sysZOSLPAR.getId(), prefixId));
            cflpar.setKafkaSendDate(Instant.now());
            return saveZseriesComputer(cflpar, sysZOSLPAR.getId());
        }
    }

    private Object saveZseriesComputerMeta(CFLPARMeta cflpar) {
        try {
            return cfLparMetaRepository.save(cflpar);
        } catch (Exception e) {
            return logException("CFLPARMeta", cflpar.getDlaId(), e);
        }
    }

    private Object saveZseriesComputer(CFLPAR cflpar, String dlaId) {
        try {
            return cfLparRepository.save(cflpar);
        } catch (Exception e) {
            return logException("CFLPAR", dlaId, e);
        }
    }

    // The PRSMLPAR also has the same info to the ZSeriesComputer, so it can
    // generate the CPC_PrefixId by itself.
    public Object loadPRSMLpar(SysZOSLPAR sysZOSLPAR, String scanDate, boolean isMeta) {
        if (isMeta) {
            PRSMLPARMeta prsmlpar = new PRSMLPARMeta();
            String prefixId = String.join("-", sysZOSLPAR.getManufacturer(), sysZOSLPAR.getModel(),
                    sysZOSLPAR.getSerialNumber());
            prsmlpar.setPrefixId(prefixId);
            prsmlpar.setScanDate(scanDate);
            prsmlpar.setId(stringUtils.composeUniqueId(sysZOSLPAR.getId(), prefixId));
            prsmlpar.setDlaId(sysZOSLPAR.getId());
            return savePRSMLPARMeta(prsmlpar);
        } else {
            PRSMLPAR prsmlpar = new PRSMLPAR();
            vmProcessor.mapFilter(sysZOSLPAR, prsmlpar);
            String prefixId = String.join("-", sysZOSLPAR.getManufacturer(), sysZOSLPAR.getModel(),
                    sysZOSLPAR.getSerialNumber());
            prsmlpar.setPrefixId(prefixId);
            prsmlpar.setScanDate(scanDate);
            prsmlpar.setId(stringUtils.composeUniqueId(sysZOSLPAR.getId(), prefixId));
            prsmlpar.setKafkaSendDate(Instant.now());
            return savePRSMLPAR(prsmlpar, sysZOSLPAR.getId());
        }
    }

    private Object savePRSMLPARMeta(PRSMLPARMeta prsmlpar) {
        try {
            return prsmLparMetaRepository.save(prsmlpar);
        } catch (Exception e) {
            return logException("PRSMLPARMeta", prsmlpar.getDlaId(), e);
        }
    }

    private Object savePRSMLPAR(PRSMLPAR prsmlpar, String dlaId) {
        try {
            return prsmLparRepository.save(prsmlpar);
        } catch (Exception e) {
            return logException("PRSMLPAR", dlaId, e);
        }
    }

    public Object loadSysplex(SysZOSSysplex sysZOSSysplex, String prefixId, String scanDate, boolean isMeta) {
        if (isMeta) {
            SysplexMeta sysplex = new SysplexMeta();
            sysplex.setPrefixId(prefixId);
            sysplex.setScanDate(scanDate);
            sysplex.setId(stringUtils.composeUniqueId(sysZOSSysplex.getId(), prefixId));
            sysplex.setDlaId(sysZOSSysplex.getId());
            return saveSysplexMeta(sysplex);
        } else {
            Sysplex sysplex = new Sysplex();
            vmProcessor.mapFilter(sysZOSSysplex, sysplex);
            sysplex.setPrefixId(prefixId);
            sysplex.setScanDate(scanDate);
            sysplex.setId(stringUtils.composeUniqueId(sysZOSSysplex.getId(), prefixId));
            sysplex.setKafkaSendDate(Instant.now());
            return saveSysplex(sysplex, sysZOSSysplex.getId());
        }
    }

    private Object saveSysplexMeta(SysplexMeta sysplex) {
        try {
            return sysplexMetaRepository.save(sysplex);
        } catch (Exception e) {
            return logException("SysplexMeta", sysplex.getDlaId(), e);
        }
    }

    private Object saveSysplex(Sysplex sysplex, String dlaId) {
        try {
            return sysplexRepository.save(sysplex);
        } catch (Exception e) {
            return logException("Sysplex", dlaId, e);
        }
    }

    public Object loadZosZos(SysZOSZOS sysZOSZOS, String cpcPrefixId, String lparName, String scanDate,
            boolean isMeta, List<String> ciIdentifierList) {
        String zosPrefixId = String.join("-", cpcPrefixId, lparName, sysZOSZOS.getLabel());
        if (isMeta) {
            try {
                return getAndSaveZOSMeta(sysZOSZOS, scanDate, zosPrefixId);
            } catch (Exception e) {
                return logException("ZOSMeta", sysZOSZOS.getId(), e);
            }

        } else {
            try {
                return getAndSaveZOS(sysZOSZOS, scanDate, zosPrefixId, ciIdentifierList);
            } catch (Exception e) {
                return logException("ZOS", sysZOSZOS.getId(), e);
            }
        }
    }

    private Object getAndSaveZOSMeta(SysZOSZOS sysZOSZOS, String scanDate, String zosPrefixId) {
        ZOSMeta zOSMeta = zosMetaRepository.findByDlaIdAndPrefixId(sysZOSZOS.getId(), zosPrefixId);
        if (zOSMeta != null) {
            if (sysZOSZOS.getOSName() != null && !sysZOSZOS.getOSName().equals("")) {
                zOSMeta.setScanDate(scanDate);
                zOSMeta.setDlaId(sysZOSZOS.getId());
                return zosMetaRepository.save(zOSMeta);
            } else {
                return zOSMeta;
            }
        } else {
            zOSMeta = new ZOSMeta();
            zOSMeta.setPrefixId(zosPrefixId);
            zOSMeta.setScanDate(scanDate);
            zOSMeta.setId(stringUtils.composeUniqueId(sysZOSZOS.getId(), zosPrefixId));
            zOSMeta.setDlaId(sysZOSZOS.getId());
            return zosMetaRepository.save(zOSMeta);
        }
    }

    private Object getAndSaveZOS(SysZOSZOS sysZOSZOS, String scanDate, String zosPrefixId,
            List<String> ciIdentifierList) {
        ZOS zos = zosRepository.findByDlaIdAndPrefixId(sysZOSZOS.getId(), zosPrefixId);
        if (zos != null) {
            // only the ZOS in ZOSBASE file have all properties, need to update the record.
            if (sysZOSZOS.getOSName() != null && !sysZOSZOS.getOSName().equals("")) {
                vmProcessor.mapFilter(sysZOSZOS, zos);
                zos.setScanDate(scanDate);
                zos.setSoftDel(false);
                zos.setDlaId(sysZOSZOS.getId());
                zos.setKafkaSendDate(Instant.now());
                commonUtils.setValuesAtIndexes(ciIdentifierList, 4, zos.getSmfId(), zos.getName(), null,
                        null, null);
                zos.setCiIdentifier(String.join("|", ciIdentifierList));
                return zosRepository.save(zos);
            } else {
                // else return the existing record directly
                zos.setSoftDel(false);
                return zos;
            }
        } else {
            // create a record for new
            zos = new ZOS();
            vmProcessor.mapFilter(sysZOSZOS, zos);
            zos.setPrefixId(zosPrefixId);
            zos.setScanDate(scanDate);
            zos.setId(stringUtils.composeUniqueId(sysZOSZOS.getId(), zosPrefixId));
            zos.setDlaId(sysZOSZOS.getId());
            zos.setKafkaSendDate(Instant.now());
            commonUtils.setValuesAtIndexes(ciIdentifierList, 4, zos.getSmfId(), zos.getName(), null,
                    null, null);
            zos.setCiIdentifier(String.join("|", ciIdentifierList));
            return zosRepository.save(zos);
        }
    }

    public Object loadZSubSystemFromCICSRegion(SysZOSCICSRegion sysZOSCICSRegion, String prefixId, String scanDate,
            boolean isMeta) {
        String dlaId = stringUtils.replaceClassTypeInDlaID(sysZOSCICSRegion.getId(), "ZSubSystem");
        if (isMeta) {
            return saveZSubSystemMeta(sysZOSCICSRegion, prefixId, dlaId, scanDate, isMeta,
                    createZSubSystemMeta(prefixId, scanDate, dlaId));
        } else {
            return saveZSubSystem(sysZOSCICSRegion, prefixId, dlaId, scanDate,
                    createZSubSystem(sysZOSCICSRegion, prefixId, scanDate, dlaId));
        }
    }

    private ZSubSystemMeta createZSubSystemMeta(String prefixId, String scanDate, String dlaId) {
        ZSubSystemMeta zSubSystemMeta = new ZSubSystemMeta();
        zSubSystemMeta.setDlaId(dlaId);
        zSubSystemMeta.setPrefixId(prefixId);
        zSubSystemMeta.setScanDate(scanDate);
        zSubSystemMeta.setId(stringUtils.composeUniqueId(dlaId, prefixId));
        return zSubSystemMeta;
    }

    private ZSubSystem createZSubSystem(SysZOSCICSRegion sysZOSCICSRegion, String prefixId, String scanDate,
            String dlaId) {
        // prepare for creating OVertex by ClassType
        ZSubSystem zSubSystem = new ZSubSystem();
        zSubSystem.setDlaId(dlaId);
        zSubSystem.setPrefixId(prefixId);
        zSubSystem.setType("CICS");
        zSubSystem.setSubSystemManufacturer("IBM");
        // use the label to be zSubSystem's name
        zSubSystem.setLabel(sysZOSCICSRegion.getLabel());
        zSubSystem.setSubSystemName(sysZOSCICSRegion.getJobName());
        zSubSystem.setSubSystemVersion(sysZOSCICSRegion.getVersionString());
        zSubSystem.setScanDate(scanDate);
        zSubSystem.setId(stringUtils.composeUniqueId(dlaId, prefixId));
        zSubSystem.setKafkaSendDate(Instant.now());
        return zSubSystem;
    }

    private Object saveZSubSystemMeta(SysZOSCICSRegion sysZOSCICSRegion, String prefixId, String dlaId, String scanDate,
            boolean isMeta,
            ZSubSystemMeta zSubSystem) {
        try {
            zSubSystemMetaRepository.save(zSubSystem);
            relationshipLoader.addOrUpdateRelationship("hasMember", dlaId, sysZOSCICSRegion.getId(), prefixId, isMeta,
                    scanDate);
            return zSubSystem;
        } catch (Exception e) {
            return printException("ZSubSystemMeta", "CICSRegionMeta", sysZOSCICSRegion.getId(), e);
        }
    }

    private Object saveZSubSystem(SysZOSCICSRegion sysZOSCICSRegion, String prefixId, String dlaId, String scanDate,
            ZSubSystem zSubSystem) {
        try {
            zSubSystemRepository.save(zSubSystem);
            // ZSubsystem hasMember CICSRegion, defined in data model
            relationshipLoader.addOrUpdateRelationship("hasMember", dlaId, sysZOSCICSRegion.getId(), prefixId, false,
                    scanDate);
            return zSubSystem;
        } catch (Exception e) {
            return printException("ZSubSystem", "CICSRegion", sysZOSCICSRegion.getId(), e);
        }
    }

    public Object loadZSubSystemFromDB2(SysZOSDB2Subsystem sysZOSDB2Subsystem, String prefixId, String scanDate,
            boolean isMeta) {
        String dlaId = stringUtils.replaceClassTypeInDlaID(sysZOSDB2Subsystem.getId(), "ZSubSystem");
        if (isMeta) {
            return saveZSubSystemMeta(sysZOSDB2Subsystem, prefixId, dlaId, scanDate, isMeta,
                    createZSubSystemfromDB2Meta(prefixId, scanDate, dlaId));
        } else {
            return saveZSubSystem(sysZOSDB2Subsystem, prefixId, dlaId, scanDate,
                    createZSubSystem(sysZOSDB2Subsystem, prefixId, scanDate, dlaId));
        }
    }

    private ZSubSystemMeta createZSubSystemfromDB2Meta(String prefixId, String scanDate, String dlaId) {
        ZSubSystemMeta zSubSystem = new ZSubSystemMeta();
        zSubSystem.setDlaId(dlaId);
        zSubSystem.setPrefixId(prefixId);
        zSubSystem.setScanDate(scanDate);
        zSubSystem.setId(stringUtils.composeUniqueId(dlaId, prefixId));
        return zSubSystem;
    }

    private ZSubSystem createZSubSystem(SysZOSDB2Subsystem sysZOSDB2Subsystem, String prefixId, String scanDate,
            String dlaId) {
        // prepare for creating OVertex by ClassType
        ZSubSystem zSubSystem = new ZSubSystem();
        zSubSystem.setDlaId(dlaId);
        zSubSystem.setPrefixId(prefixId);
        zSubSystem.setType("DB2");
        zSubSystem.setSubSystemManufacturer("IBM");
        zSubSystem.setSubSystemName(sysZOSDB2Subsystem.getSubsystemName());
        zSubSystem.setLabel(sysZOSDB2Subsystem.getLabel());
        zSubSystem.setSubSystemVersion(sysZOSDB2Subsystem.getVersionString());
        zSubSystem.setScanDate(scanDate);
        zSubSystem.setId(stringUtils.composeUniqueId(dlaId, prefixId));
        zSubSystem.setKafkaSendDate(Instant.now());
        return zSubSystem;
    }

    private Object saveZSubSystemMeta(SysZOSDB2Subsystem sysZOSDB2Subsystem, String prefixId, String dlaId,
            String scanDate, boolean isMeta,
            ZSubSystemMeta zSubSystem) {
        try {
            zSubSystemMetaRepository.save(zSubSystem);
            relationshipLoader.addOrUpdateRelationship("hasMember", dlaId, sysZOSDB2Subsystem.getId(), prefixId, true,
                    scanDate);
            return zSubSystem;
        } catch (Exception e) {
            return printException("ZSubSystemMeta", "DB2SubsystemMeta", sysZOSDB2Subsystem.getId(), e);
        }
    }

    private Object saveZSubSystem(SysZOSDB2Subsystem sysZOSDB2Subsystem, String prefixId, String dlaId, String scanDate,
            ZSubSystem zSubSystem) {
        try {
            zSubSystemRepository.save(zSubSystem);
            // ZSubsystem hasMember DB2Subsystem, defined in data model
            relationshipLoader.addOrUpdateRelationship("hasMember", dlaId, sysZOSDB2Subsystem.getId(), prefixId, false,
                    scanDate);
            return zSubSystem;
        } catch (Exception e) {
            return printException("ZSubSystem", "DB2Subsystem", sysZOSDB2Subsystem.getId(), e);
        }
    }

    public Object loadZSubsystemFromMQ(SysZOSMQSubsystem sysZOSMQSubsystem, String prefixId, String scanDate,
            boolean isMeta) {
        String dlaId = stringUtils.replaceClassTypeInDlaID(sysZOSMQSubsystem.getId(), "ZSubSystem");
        if (isMeta) {
            return saveZSubSystemMetaFromMQ(sysZOSMQSubsystem, prefixId, dlaId, scanDate,
                    createZSubSystemMetaFromMQ(prefixId, scanDate, dlaId));
        } else {
            return saveZSubSystemFromMQ(sysZOSMQSubsystem, prefixId, dlaId, scanDate,
                    createZSubSystemFromMQ(sysZOSMQSubsystem, prefixId, scanDate, dlaId));
        }
    }

    private ZSubSystemMeta createZSubSystemMetaFromMQ(String prefixId, String scanDate, String dlaId) {
        ZSubSystemMeta zSubSystem = new ZSubSystemMeta();
        zSubSystem.setDlaId(dlaId);
        zSubSystem.setPrefixId(prefixId);
        zSubSystem.setScanDate(scanDate);
        zSubSystem.setId(stringUtils.composeUniqueId(dlaId, prefixId));
        return zSubSystem;
    }

    private ZSubSystem createZSubSystemFromMQ(SysZOSMQSubsystem sysZOSMQSubsystem, String prefixId, String scanDate,
            String dlaId) {
        // prepare for creating OVertex by ClassType
        ZSubSystem zSubSystem = new ZSubSystem();
        zSubSystem.setDlaId(dlaId);
        zSubSystem.setPrefixId(prefixId);
        zSubSystem.setType("MQ");
        zSubSystem.setSubSystemManufacturer("IBM");
        zSubSystem.setSubSystemName(sysZOSMQSubsystem.getSubsystemName());
        zSubSystem.setLabel(sysZOSMQSubsystem.getLabel());
        zSubSystem.setSubSystemVersion(sysZOSMQSubsystem.getVersionString());
        zSubSystem.setScanDate(scanDate);
        zSubSystem.setId(stringUtils.composeUniqueId(dlaId, prefixId));
        zSubSystem.setKafkaSendDate(Instant.now());
        return zSubSystem;
    }

    private Object saveZSubSystemMetaFromMQ(SysZOSMQSubsystem sysZOSMQSubsystem, String prefixId, String dlaId,
            String scanDate,
            ZSubSystemMeta zSubSystem) {
        try {
            zSubSystemMetaRepository.save(zSubSystem);
            relationshipLoader.addOrUpdateRelationship("hasMember", dlaId, sysZOSMQSubsystem.getId(), prefixId,
                    false, scanDate);
            return zSubSystem;
        } catch (Exception e) {
            return printException("ZSubSystemMeta", "MQSubsystemMeta", sysZOSMQSubsystem.getId(), e);
        }
    }

    private Object saveZSubSystemFromMQ(SysZOSMQSubsystem sysZOSMQSubsystem, String prefixId, String dlaId,
            String scanDate,
            ZSubSystem zSubSystem) {
        try {
            zSubSystemRepository.save(zSubSystem);
            // ZSubsystem hasMember MQSubsystem
            relationshipLoader.addOrUpdateRelationship("hasMember", dlaId, sysZOSMQSubsystem.getId(), prefixId,
                    false, scanDate);
            return zSubSystem;
        } catch (Exception e) {
            return printException("ZSubSystem", "MQSubsystem", sysZOSMQSubsystem.getId(), e);
        }
    }

    public Object loadZSubsystemFromIMS(SysZOSIMSSubsystem sysZOSIMSSubsystem, String prefixId, String scanDate,
            boolean isMeta) {
        String dlaId = stringUtils.replaceClassTypeInDlaID(sysZOSIMSSubsystem.getId(), "ZSubSystem");
        if (isMeta) {
            return saveZSubSystemFromImsMeta(sysZOSIMSSubsystem, prefixId, dlaId, scanDate,
                    createZSubSystemFromImsMeta(prefixId, scanDate, dlaId));
        } else {
            return saveZSubSystemFromIms(sysZOSIMSSubsystem, prefixId, dlaId, scanDate,
                    createZSubSystemFromIms(sysZOSIMSSubsystem, prefixId, scanDate, dlaId));
        }
    }

    private ZSubSystemMeta createZSubSystemFromImsMeta(String prefixId, String scanDate, String dlaId) {
        ZSubSystemMeta zSubSystem = new ZSubSystemMeta();
        zSubSystem.setDlaId(dlaId);
        zSubSystem.setPrefixId(prefixId);
        zSubSystem.setScanDate(scanDate);
        zSubSystem.setId(stringUtils.composeUniqueId(dlaId, prefixId));
        return zSubSystem;
    }

    private ZSubSystem createZSubSystemFromIms(SysZOSIMSSubsystem sysZOSIMSSubsystem, String prefixId, String scanDate,
            String dlaId) {
        // prepare for creating OVertex by ClassType
        ZSubSystem zSubSystem = new ZSubSystem();
        zSubSystem.setDlaId(dlaId);
        zSubSystem.setPrefixId(prefixId);
        zSubSystem.setType("IMS");
        zSubSystem.setSubSystemManufacturer("IBM");
        zSubSystem.setSubSystemName(sysZOSIMSSubsystem.getSubsystemName());
        zSubSystem.setLabel(sysZOSIMSSubsystem.getLabel());
        zSubSystem.setSubSystemVersion(sysZOSIMSSubsystem.getVersionString());
        zSubSystem.setScanDate(scanDate);
        zSubSystem.setId(stringUtils.composeUniqueId(dlaId, prefixId));
        zSubSystem.setKafkaSendDate(Instant.now());
        return zSubSystem;
    }

    private Object saveZSubSystemFromImsMeta(SysZOSIMSSubsystem sysZOSIMSSubsystem, String prefixId, String dlaId,
            String scanDate,
            ZSubSystemMeta zSubSystem) {
        try {
            zSubSystemMetaRepository.save(zSubSystem);
            relationshipLoader.addOrUpdateRelationship("hasMember", dlaId, sysZOSIMSSubsystem.getId(), prefixId,
                    false, scanDate);
            return zSubSystem;
        } catch (Exception e) {
            return printException("ZSubSystemMeta", "IMSSubsystemMeta", sysZOSIMSSubsystem.getId(), e);
        }
    }

    private Object saveZSubSystemFromIms(SysZOSIMSSubsystem sysZOSIMSSubsystem, String prefixId, String dlaId,
            String scanDate,
            ZSubSystem zSubSystem) {
        try {
            zSubSystemRepository.save(zSubSystem);
            // ZSubsystem hasMember MQSubsystem
            relationshipLoader.addOrUpdateRelationship("hasMember", dlaId, sysZOSIMSSubsystem.getId(), prefixId,
                    false, scanDate);
            return zSubSystem;
        } catch (Exception e) {
            return printException("ZSubSystem", "IMSSubsystem", sysZOSIMSSubsystem.getId(), e);
        }
    }

    public Object loadCICSRegion(SysZOSCICSRegion sysZOSCICSRegion, String prefixId, String scanDate, boolean isMeta,
            List<String> ciIdentifierList) {
        if (isMeta) {
            return saveCICSPlexMeta(createCicsRegionMeta(sysZOSCICSRegion, prefixId, scanDate));
        } else {
            return saveCICSPlex(createCicsRegion(sysZOSCICSRegion, prefixId, scanDate, ciIdentifierList));
        }
    }

    private CICSRegionMeta createCicsRegionMeta(SysZOSCICSRegion sysZOSCICSRegion, String prefixId, String scanDate) {
        CICSRegionMeta cicsRegion = new CICSRegionMeta();
        cicsRegion.setPrefixId(prefixId);
        cicsRegion.setScanDate(scanDate);
        cicsRegion.setId(stringUtils.composeUniqueId(sysZOSCICSRegion.getId(), prefixId));
        cicsRegion.setDlaId(sysZOSCICSRegion.getId());
        return cicsRegion;
    }

    private CICSRegion createCicsRegion(SysZOSCICSRegion sysZOSCICSRegion, String prefixId, String scanDate,
            List<String> ciIdentifierList) {
        CICSRegion cicsRegion = new CICSRegion();
        vmProcessor.mapFilter(sysZOSCICSRegion, cicsRegion);
        cicsRegion.setPrefixId(prefixId);
        // cicsRegion.setDfhCICSHLQ("DFH.V5R5M0"); // TODO: hardcode for TIVLP02-ZOS
        cicsRegion.setScanDate(scanDate);
        cicsRegion.setId(stringUtils.composeUniqueId(sysZOSCICSRegion.getId(), prefixId));
        cicsRegion.setKafkaSendDate(Instant.now());
        commonUtils.setValuesAtIndexes(ciIdentifierList, 6, null, sysZOSCICSRegion.getJobName(),
                sysZOSCICSRegion.getId());
        cicsRegion.setCiIdentifier(String.join("|", ciIdentifierList));
        return cicsRegion;
    }

    private Object saveCICSPlexMeta(CICSRegionMeta cicsRegion) {
        try {
            return cicsRegionMetaRepository.save(cicsRegion);
        } catch (Exception e) {
            return logException("CICSRegionMeta", cicsRegion.getDlaId(), e);
        }
    }

    private Object saveCICSPlex(CICSRegion cicsRegion) {
        try {
            return cicsRegionRepository.save(cicsRegion);
        } catch (Exception e) {
            return logException("CICSRegion", cicsRegion.getDlaId(), e);
        }
    }

    public Object loadCICSPlex(SysZOSCICSPlex sysZOSCICSPlex, String prefixId, String scanDate, boolean isMeta) {
        if (isMeta) {
            return saveCICSPlexMeta(createCicsPlexMeta(sysZOSCICSPlex, prefixId, scanDate));
        } else {
            return saveCICSPlex(createCicsPlex(sysZOSCICSPlex, prefixId, scanDate));
        }
    }

    private CICSPlexMeta createCicsPlexMeta(SysZOSCICSPlex sysZOSCICSPlex, String prefixId, String scanDate) {
        CICSPlexMeta cicsPlex = new CICSPlexMeta();
        cicsPlex.setPrefixId(prefixId);
        cicsPlex.setScanDate(scanDate);
        cicsPlex.setId(stringUtils.composeUniqueId(sysZOSCICSPlex.getId(), prefixId));
        cicsPlex.setDlaId(sysZOSCICSPlex.getId());
        return cicsPlex;
    }

    private CICSPlex createCicsPlex(SysZOSCICSPlex sysZOSCICSPlex, String prefixId, String scanDate) {
        CICSPlex cicsPlex = new CICSPlex();
        vmProcessor.mapFilter(sysZOSCICSPlex, cicsPlex);
        cicsPlex.setPrefixId(prefixId);
        cicsPlex.setScanDate(scanDate);
        cicsPlex.setId(stringUtils.composeUniqueId(sysZOSCICSPlex.getId(), prefixId));
        cicsPlex.setKafkaSendDate(Instant.now());
        return cicsPlex;
    }

    private Object saveCICSPlexMeta(CICSPlexMeta cicsPlex) {
        try {
            return cicsPlexMetaRepository.save(cicsPlex);
        } catch (Exception e) {
            return logException("CICSPlexMeta", cicsPlex.getDlaId(), e);
        }
    }

    private Object saveCICSPlex(CICSPlex cicsPlex) {
        try {
            return cicsPlexRepository.save(cicsPlex);
        } catch (Exception e) {
            return logException("CICSPlex", cicsPlex.getDlaId(), e);
        }
    }

    private Object logException(String entityName, String dlaId, Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "logException", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "logException", ie.toString());
        return null;
    }

    private Object printException(String entityName, String dataBaseName, String dlaId, Exception e) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "logException", "Failed to save {} from {} or its relationship: {}",
                entityName, dataBaseName, dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "logException", e.toString());
        return null;
    }

}