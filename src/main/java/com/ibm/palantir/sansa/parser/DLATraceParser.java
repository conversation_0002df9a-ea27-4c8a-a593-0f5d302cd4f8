/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2024-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.parser;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2BufferPool;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2Database;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2StoredProcedure;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2Table;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2TableSpace;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQAliasQueue;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQAuthInfo;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQBufferPool;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQClientConnectionChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQClusterReceiverChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQClusterSenderChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQLocalQueue;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQModelQueue;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQReceiverChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQRemoteQueue;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQSenderChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQServerConnectionChannel;
import com.ibm.palantir.catelyn.jaxb.AppProcessPool;
import com.ibm.palantir.catelyn.jaxb.NetBindAddress;
import com.ibm.palantir.catelyn.jaxb.NetFqdn;
import com.ibm.palantir.catelyn.jaxb.NetIpAddress;
import com.ibm.palantir.catelyn.jaxb.NetIpInterface;
import com.ibm.palantir.catelyn.jaxb.NetTcpPort;
import com.ibm.palantir.catelyn.jaxb.NetUdpPort;
import com.ibm.palantir.catelyn.jaxb.SysZOSAddressSpace;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSFile;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSPlex;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSProgram;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSRegion;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSTransaction;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2Conn;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2DataSharingGroup;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2Subsystem;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSDatabase;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSProgram;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSSubsystem;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSSysplexGroup;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSTransaction;
import com.ibm.palantir.catelyn.jaxb.SysZOSLPAR;
import com.ibm.palantir.catelyn.jaxb.SysZOSMQQueueSharingGroup;
import com.ibm.palantir.catelyn.jaxb.SysZOSMQSubsystem;
import com.ibm.palantir.catelyn.jaxb.SysZOSSysplex;
import com.ibm.palantir.catelyn.jaxb.SysZOSZOS;
import com.ibm.palantir.catelyn.jaxb.SysZOSZReportFile;
import com.ibm.palantir.catelyn.jaxb.SysZOSZSeriesComputerSystem;
import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSubSystem;
import com.ibm.palantir.catelyn.jpa.entity.manage.DLADataTrace;
import com.ibm.palantir.catelyn.jpa.repository.manage.DLADataTraceRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.exception.ServiceExceptionStore;
import com.ibm.palantir.sansa.extractor.ElementExtractor;
import com.ibm.palantir.sansa.utils.ReflectUtil;
import com.ibm.palantir.sansa.utils.StringUtils;

import jakarta.xml.bind.JAXBElement;

public class DLATraceParser {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = DLATraceParser.class.getSimpleName();
    private static final String REPONAME = "Sansa";
    private static final ServiceExceptionStore errorStore = ServiceExceptionStore.getInstance();
    private StringUtils stringUtils = new StringUtils();
    private DLADataTraceRepository dlaDataTraceRepository;
    private final ElementExtractor elementExtractor;
    private final String cpcPrefixId;
    private final String zosPrefixId;
    private final String smfId;
    private final String lparName;
    private final HashSet<String> filterIds;
    private HashSet<String> classStrList = new HashSet<>();
    private HashSet<String> cpcPrefixClassList = new HashSet<>();
    private HashSet<String> noPrefixClassList = new HashSet<>();
    private HashSet<String> zosPrefixClassList = new HashSet<>();

    // The deltaDataBook just has part elements, so we have to use new
    // DLATraceParser to update traces for those full elements
    public DLATraceParser(ElementExtractor elementExtractor, String cpcPrefixId, String zosPrefixId,
            String smfId, String lparName, HashSet<String> filterIds) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "DLATraceParser",
                "Running DLATraceParser with params - cpcPrefixId: {}, zosPrefixId: {}, smfId: {}, lparName: {}, filterIds: {}",
                cpcPrefixId, zosPrefixId, smfId, lparName, filterIds);
        this.elementExtractor = elementExtractor;
        this.cpcPrefixId = cpcPrefixId;
        this.zosPrefixId = zosPrefixId;
        this.smfId = smfId;
        this.lparName = lparName;
        this.filterIds = filterIds;
        this.init();
    }

    public void updateScanDateFromElementExtractor() {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "updateScanDateFromElementExtractor",
                "Running updateScanDateFromElementExtractor");
        List<DLADataTrace> dataTraceList = new ArrayList<>();
        for (JAXBElement jaxbElement : this.elementExtractor.managedElementsAndRelationships) {
            String className = jaxbElement.getDeclaredType().getName();
            if (this.classStrList.contains(className)) {

                if (className.equals(SysZOSZReportFile.class.getName())) { // Process CICS_SIT and CICS_SIT_Overrides
                    SysZOSZReportFile zosReportFile = (SysZOSZReportFile) jaxbElement.getValue();
                    String fixedPath = zosReportFile.getFixedPath();
                    if (fixedPath.equals("CICS_SIT") || fixedPath.equals("CICS_SIT_Overrides")) {
                        String id = stringUtils.composeUniqueId(zosReportFile.getId(), this.zosPrefixId);
                        // Check that the object data corresponding to the ID has been successfully
                        // saved
                        if (!this.filterIds.contains(id)) {
                            DLADataTrace dlaDataTrace = new DLADataTrace();
                            dlaDataTrace.setId(id);
                            dlaDataTrace.setDlaId(zosReportFile.getId());
                            dlaDataTrace.setFilePath(this.elementExtractor.filePath);
                            dlaDataTrace.setScanDate(this.elementExtractor.createTimestamp);
                            dataTraceList.add(dlaDataTrace);
                        }
                    }
                } else { // Process other objects
                    try {
                        Object dlaIdObj = ReflectUtil.getObjectValueByField(jaxbElement.getValue(), "id");
                        if (dlaIdObj == null) {
                            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "updateScanDateFromElementExtractor",
                                    "DLATraceParser get empty dlaId, please check the idml book's content.");
                            continue;
                        }

                        String dlaId = (String) dlaIdObj;
                        String id;
                        if (this.cpcPrefixClassList.contains(className)) {
                            id = stringUtils.composeUniqueId(dlaId, cpcPrefixId);
                        } else if (this.zosPrefixClassList.contains(className)) {
                            id = stringUtils.composeUniqueId(dlaId, zosPrefixId);
                        } else {
                            id = dlaId;
                        }

                        // Check that the object data corresponding to the ID has been successfully
                        // saved
                        if (!this.filterIds.contains(id)) {
                            DLADataTrace dlaDataTrace = new DLADataTrace();
                            dlaDataTrace.setId(id);
                            dlaDataTrace.setDlaId(dlaId);
                            dlaDataTrace.setFilePath(this.elementExtractor.filePath);
                            dlaDataTrace.setScanDate(this.elementExtractor.createTimestamp);
                            dataTraceList.add(dlaDataTrace);
                        }

                    } catch (Exception e) {
                        LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "updateScanDateFromElementExtractor",
                                e.toString());
                        errorStore.addError(e.toString());
                    }
                }
            }
        }

        if (dataTraceList.size() > 0) {
            try {
                dlaDataTraceRepository.saveAll(dataTraceList);
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "updateScanDateFromElementExtractor",
                        String.format("DLATraceParser finish updateScanDateFromElementExtractor, count: %s records.",
                                dataTraceList.size()));
            } catch (Exception oe) {
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "updateScanDateFromElementExtractor",
                        "DLATraceParser failed to saveAll dataTraceList in updateScanDateFromElementExtractor, try to save them one-by-one");
                Integer c = 0;
                for (DLADataTrace trace : dataTraceList) {
                    try {
                        dlaDataTraceRepository.save(trace);
                    } catch (Exception ie) {
                        c += 1;
                        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "updateScanDateFromElementExtractor",
                                "Failed to save DLADataTrace: {}", trace.getDlaId());
                        LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "updateScanDateFromElementExtractor",
                                ie.toString());
                    }
                }
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "updateScanDateFromElementExtractor", String.format(
                        "DLATraceParser finished updateScanDateFromElementExtractor, count: %s successful records and %s abnormal records.",
                        dataTraceList.size() - c, c));
            }
        } else {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "updateScanDateFromElementExtractor",
                    "DLATraceParser finished updateScanDateFromElementExtractor, but saved 0 records.");
        }
    }

    public void updateScanDateForZSubsystem(List<ZSubSystem> zSubSystemList) {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "updateScanDateForZSubsystem",
                "Running updateScanDateForZSubsystem");
        List<DLADataTrace> dataTraceList = new ArrayList<>();
        for (ZSubSystem zSubSystem : zSubSystemList) {
            DLADataTrace dlaDataTrace = new DLADataTrace();
            dlaDataTrace.setId(zSubSystem.getId());
            dlaDataTrace.setDlaId(zSubSystem.getDlaId());
            dlaDataTrace.setFilePath(this.elementExtractor.filePath);
            dlaDataTrace.setScanDate(this.elementExtractor.createTimestamp);
            dataTraceList.add(dlaDataTrace);
        }

        if (dataTraceList.size() > 0) {
            try {
                dlaDataTraceRepository.saveAll(dataTraceList);
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "updateScanDateForZSubsystem", String.format(
                        "DLATraceParser finish updateScanDateForZSubsystem, count: %d records.", dataTraceList.size()));
            } catch (Exception oe) {
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "updateScanDateForZSubsystem",
                        "DLATraceParser failed to saveAll dataTraceList in updateScanDateForZSubsystem, try to save them one-by-one");
                Integer c = 0;
                for (DLADataTrace trace : dataTraceList) {
                    try {
                        dlaDataTraceRepository.save(trace);
                    } catch (Exception ie) {
                        c += 1;
                        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "updateScanDateForZSubsystem",
                                "Failed to save DLADataTrace: {}", trace.getDlaId());
                        LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "updateScanDateForZSubsystem", ie.toString());
                    }
                }
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "updateScanDateForZSubsystem", String.format(
                        "DLATraceParser finish updateScanDateForZSubsystem, count: %d successful records and %d abnormal records.",
                        dataTraceList.size() - c, c));
            }
        } else {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "updateScanDateForZSubsystem",
                    "DLATraceParser finished updateScanDateForZSubsystem, but saved 0 records.");
        }
    }

    private void init() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "init", "Running init");
        this.dlaDataTraceRepository = (DLADataTraceRepository) JPAManager.getJPAManager().getRepos()
                .get("dlaDataTrace");

        this.cpcPrefixClassList.add(SysZOSZSeriesComputerSystem.class.getName());
        this.cpcPrefixClassList.add(SysZOSLPAR.class.getName());

        this.noPrefixClassList.add(SysZOSSysplex.class.getName());
        this.noPrefixClassList.add(SysZOSDB2DataSharingGroup.class.getName());
        this.noPrefixClassList.add(SysZOSMQQueueSharingGroup.class.getName());
        this.noPrefixClassList.add(SysZOSIMSSysplexGroup.class.getName());

        this.zosPrefixClassList.add(SysZOSZOS.class.getName());
        this.zosPrefixClassList.add(NetFqdn.class.getName());
        this.zosPrefixClassList.add(NetIpAddress.class.getName());
        this.zosPrefixClassList.add(NetIpInterface.class.getName());
        this.zosPrefixClassList.add(NetBindAddress.class.getName());
        this.zosPrefixClassList.add(SysZOSDB2Conn.class.getName());
        this.zosPrefixClassList.add(SysZOSCICSPlex.class.getName());
        this.zosPrefixClassList.add(AppDbDb2Db2Table.class.getName());
        this.zosPrefixClassList.add(SysZOSAddressSpace.class.getName());
        this.zosPrefixClassList.add(AppProcessPool.class.getName());
        this.zosPrefixClassList.add(NetTcpPort.class.getName());
        this.zosPrefixClassList.add(NetUdpPort.class.getName());
        this.zosPrefixClassList.add(SysZOSCICSRegion.class.getName());
        this.zosPrefixClassList.add(SysZOSCICSProgram.class.getName());
        this.zosPrefixClassList.add(SysZOSCICSTransaction.class.getName());
        this.zosPrefixClassList.add(SysZOSCICSFile.class.getName());
        this.zosPrefixClassList.add(SysZOSDB2Subsystem.class.getName());
        this.zosPrefixClassList.add(AppDbDb2Db2StoredProcedure.class.getName());
        this.zosPrefixClassList.add(AppDbDb2Db2StoredProcedure.class.getName());
        this.zosPrefixClassList.add(AppDbDb2Db2Database.class.getName());
        this.zosPrefixClassList.add(AppDbDb2Db2TableSpace.class.getName());
        this.zosPrefixClassList.add(AppDbDb2Db2BufferPool.class.getName());
        this.zosPrefixClassList.add(SysZOSMQSubsystem.class.getName());
        this.zosPrefixClassList.add(AppMessagingMqMQAliasQueue.class.getName());
        this.zosPrefixClassList.add(AppMessagingMqMQAuthInfo.class.getName());
        this.zosPrefixClassList.add(AppMessagingMqMQBufferPool.class.getName());
        this.zosPrefixClassList.add(AppMessagingMqMQClientConnectionChannel.class.getName());
        this.zosPrefixClassList.add(AppMessagingMqMQClusterReceiverChannel.class.getName());
        this.zosPrefixClassList.add(AppMessagingMqMQClusterSenderChannel.class.getName());
        this.zosPrefixClassList.add(AppMessagingMqMQLocalQueue.class.getName());
        this.zosPrefixClassList.add(AppMessagingMqMQModelQueue.class.getName());
        this.zosPrefixClassList.add(AppMessagingMqMQReceiverChannel.class.getName());
        this.zosPrefixClassList.add(AppMessagingMqMQRemoteQueue.class.getName());
        this.zosPrefixClassList.add(AppMessagingMqMQSenderChannel.class.getName());
        this.zosPrefixClassList.add(AppMessagingMqMQServerConnectionChannel.class.getName());
        this.zosPrefixClassList.add(SysZOSIMSSubsystem.class.getName());
        this.zosPrefixClassList.add(SysZOSIMSDatabase.class.getName());
        this.zosPrefixClassList.add(SysZOSIMSProgram.class.getName());
        this.zosPrefixClassList.add(SysZOSIMSTransaction.class.getName());

        this.classStrList.addAll(this.cpcPrefixClassList);
        this.classStrList.addAll(this.noPrefixClassList);
        this.classStrList.addAll(this.zosPrefixClassList);
        this.classStrList.add(SysZOSZReportFile.class.getName());
    }
}
