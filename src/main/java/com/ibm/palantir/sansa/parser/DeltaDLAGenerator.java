/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2024, 2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.parser;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.time.LocalDateTime;

import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.entity.manage.FileTrace;
import com.ibm.palantir.catelyn.jpa.repository.manage.FileTraceRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;

public class DeltaDLAGenerator {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = DeltaDLAGenerator.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    // Make location and scriptPath configurable via environment variables
    private static String location = System.getenv("UPLOAD_DIR") != null ? System.getenv("UPLOAD_DIR") : "./upload-dir";
    private final Path rootLocation = Paths.get(location);
    private final String scriptPath = System.getenv("SCRIPT_PATH") != null ? System.getenv("SCRIPT_PATH")
            : "./artifacts/deltabooks.sh";
    private FileTraceRepository fileTraceRepository;

    public DeltaDLAGenerator() throws Exception {
        Files.createDirectories(this.rootLocation);
        this.fileTraceRepository = (FileTraceRepository) JPAManager.getJPAManager().getRepos().get("fileTrace");
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "DeltaDLAGenerator",
                "DeltaDLAGenerator initialized with location: {} and scriptPath: {}", location, scriptPath);
    }

    private FileTrace findPreviousFileTrace(FileTrace currentFileTrace) {

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "findPreviousFileTrace", "Running findPreviousFileTrace");
        FileTrace previousFileTrace = fileTraceRepository.getPreviousTraceByParameters(
                currentFileTrace.getFileName(), currentFileTrace.getSysplexName(), currentFileTrace.getDatasetName(),
                currentFileTrace.getMemberName(), currentFileTrace.getCreatedAt());
        if (previousFileTrace == null) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "findPreviousFileTrace", String.format(
                    "Failed to find the previous fileTrace by combined parameters, try to find it by fileName %s. ",
                    currentFileTrace.getFileName()));
            previousFileTrace = fileTraceRepository.getPreviousTracByFileName(currentFileTrace.getFileName(),
                    currentFileTrace.getCreatedAt());
            if (previousFileTrace == null) {
                // can't get the previous fileTrace
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "findPreviousFileTrace", String.format(
                        "Also failed to find the previousFileTrace By fileName %s and the current createdAt timestamp %s.",
                        currentFileTrace.getFileName(), currentFileTrace.getCreatedAt()));
            }
        }
        return previousFileTrace;
    }

    public Integer shellExecutor(String benchmarkBookPath, String currentFilePath) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "shellExecutor",
                "Running shellExecutor with params - benchmarkBookPath: {}, currentFilePath: {}", benchmarkBookPath,
                currentFilePath);
        try {
            Path originPath = Paths.get(location, benchmarkBookPath);
            Path targetPath = Paths.get(location, currentFilePath);
            String cmd = String.format("%s -f %s -t %s -o %s -verbose", scriptPath, originPath, targetPath,
                    rootLocation);
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "shellExecutor", "Delta generator's process cmd: {}", cmd);

            Process process = Runtime.getRuntime().exec(cmd);
            int exitValue = process.waitFor();

            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "shellExecutor", "Delta generator's process exitValue: {}",
                    exitValue);

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "shellExecutor", line);
            }
            reader.close();
            return exitValue;
        } catch (Exception e) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "shellExecutor", e.toString());
            return 1;
        }

    }

    public static String getLatestDeltaBookPath() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "getLatestDeltaBookPath", "Running getLatestDeltaBookPath");
        String deltaBookPath = "";

        File directory = new File(location);
        File[] files = directory.listFiles((dir, name) -> name.endsWith(".out"));

        long latestFileTime = Long.MIN_VALUE;
        File latestFile = null;
        for (File file : files) {
            if (file.lastModified() > latestFileTime) {
                latestFile = file;
                latestFileTime = file.lastModified();
            }
        }

        if (latestFile != null) {
            try {
                Path path = Paths.get(latestFile.getAbsolutePath());
                String content = Files.readString(path);
                String keyWord = "Delta book:";
                // content format:
                // 09:59:43:524 Delta book:
                // <EMAIL>
                for (String line : content.split("\n")) {
                    // Same Case:
                    // 03:16:04:622 Delta book: Books are identical.
                    if (line.contains("Books are identical.")) {
                        return null;
                    }
                    if (line.contains(keyWord)) {
                        String[] sl = line.split(" ");
                        deltaBookPath = sl[sl.length - 1].trim();
                    }
                }
            } catch (Exception e) {
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "getLatestDeltaBookPath", e.toString());
            }
        }

        return deltaBookPath;
    }

    public String generateDeltaBook(String currentFilePath, String scanDate) {

        FileTrace currentFileTrace = fileTraceRepository.getTraceByFilePath(currentFilePath);
        // No fileTrace record for the filePath
        if (currentFileTrace == null) {
            return "NoFileTraceRecord";
        }

        FileTrace previousFileTrace = this.findPreviousFileTrace(currentFileTrace);
        // No previous fileTrace record for the filePath
        if (previousFileTrace == null) {
            // save the scanDate for currentFileTrace
            currentFileTrace.setScanDate(scanDate);
            fileTraceRepository.save(currentFileTrace);
            return "";
        }

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "generateDeltaBook",
                String.format("Get the previousFileTrace with id %d for the currentFilePath %s. ",
                        previousFileTrace.getId(), currentFilePath));
        String benchmarkBookPath = previousFileTrace.getFilePath();
        Integer processExitValue = this.shellExecutor(benchmarkBookPath, currentFilePath);
        if (processExitValue != 0) {
            // generate delta book failed
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "generateDeltaBook",
                    String.format("Finish executing the deltabook.sh but get exitValue %d", processExitValue));
            return "";
        } else {
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "generateDeltaBook", String.format(
                    "Try to get the latest delta book path for benchmarkBookPath %s and currentFilePath %s",
                    benchmarkBookPath, currentFilePath));
            // get the delta book path from the latest log and fileName
            String deltaBookPath = this.getLatestDeltaBookPath();
            if (deltaBookPath != null && !deltaBookPath.isEmpty()) {
                // save the benchmarkBookPath, deltaBookPath and deltaGenerateTime
                currentFileTrace.setBenchmarkBookPath(benchmarkBookPath);
                currentFileTrace.setDeltaBookPath(deltaBookPath);
                currentFileTrace.setDeltaGenerateTime(Timestamp.valueOf(LocalDateTime.now()));
                currentFileTrace.setScanDate(scanDate);
                fileTraceRepository.save(currentFileTrace);
            } else {
                // save the scanDate for currentFileTrace
                currentFileTrace.setScanDate(scanDate);
                fileTraceRepository.save(currentFileTrace);
            }
            return deltaBookPath;
        }
    }

    public String getPreviousFilePath(String currentFilePath, String scanDate) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "getPreviousFilePath",
                "Running getPreviousFilePath with params - currentFilePath: {}, scanDate: {}", currentFilePath,
                scanDate);
        FileTrace currentFileTrace = fileTraceRepository.getTraceByFilePath(currentFilePath);
        // No fileTrace record for the filePath
        if (currentFileTrace == null) {
            return "NoFileTraceRecord";
        }

        FileTrace previousFileTrace = this.findPreviousFileTrace(currentFileTrace);
        // No previous fileTrace record for the filePath
        if (previousFileTrace == null) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "getPreviousFilePath",
                    "PreviousFileTrace is null, current FileTrace is: {}", currentFileTrace);
            return "";
        }

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "getPreviousFilePath",
                String.format("Get the previousFileTrace with id %d for the currentFilePath %s. ",
                        previousFileTrace.getId(), currentFilePath));
        return previousFileTrace.getFilePath();
    }

    public static void main(String[] args) throws IOException, InterruptedException {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "main",
                "Running main with params - main: {}", String.join(" ", args));
        // Use the same environment variables for consistency
        String uploadDir = System.getenv("UPLOAD_DIR") != null ? System.getenv("UPLOAD_DIR") : "./upload-dir";
        String scriptPath = System.getenv("SCRIPT_PATH") != null ? System.getenv("SCRIPT_PATH")
                : "./script/deltabooks.sh";

        String cmd = String.format("%s -f %s -t %s -o %s -verbose",
                scriptPath,
                uploadDir + "/DC1EMSTR",
                uploadDir + "/DC1VMSTR",
                uploadDir);

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "main", "Running command: {}", cmd);
        Process process = Runtime.getRuntime().exec(cmd);

        int exitCode = process.waitFor();
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "main", "Exit code: {}", exitCode);

        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line;
        while ((line = reader.readLine()) != null) {
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "main", line);
        }
        reader.close();
    }
}
