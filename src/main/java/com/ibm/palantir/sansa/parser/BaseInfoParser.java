/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2024
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.parser;

import com.ibm.palantir.catelyn.jaxb.SysZOSLPAR;
import com.ibm.palantir.catelyn.jaxb.SysZOSZOS;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.extractor.ElementExtractor;

import java.util.HashMap;
import java.util.List;

public class BaseInfoParser {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = BaseInfoParser.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    public HashMap<String,String> extractBaseInfo(ElementExtractor elementExtractor){

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "extractBaseInfo", "Running extractBaseInfo");
        String cpcPrefixId = "";
        String zosPrefixId = "";
        String smfId = "";
        String lparName = "";

        // ZOSLPAR
        List<Object> sysZOSLPARElementList = elementExtractor.getElementListByClassName(SysZOSLPAR.class.getName());
        // Prioritize LPAR processing to ensure the generation of the cpcPrefixId
        if (!sysZOSLPARElementList.isEmpty()) {
            for (Object sysZOSLPARElement : sysZOSLPARElementList) {
                SysZOSLPAR sysZOSLPAR = (SysZOSLPAR) sysZOSLPARElement;
                if (sysZOSLPAR.getLabel() != null && !sysZOSLPAR.getId().contains("PRSM")) {
                    cpcPrefixId = String.join("-", sysZOSLPAR.getManufacturer(), sysZOSLPAR.getModel(), sysZOSLPAR.getSerialNumber());
                    lparName = sysZOSLPAR.getLPARName();
                }

            }
        }

        SysZOSZOS sysZOSZOS = (SysZOSZOS) elementExtractor.getElementByClassName(SysZOSZOS.class.getName());
        zosPrefixId = String.join("-", cpcPrefixId, lparName, sysZOSZOS.getLabel());
        smfId = sysZOSZOS.getSMFID();

        if (!cpcPrefixId.isEmpty() && !zosPrefixId.isEmpty() && !smfId.isEmpty()) {
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "extractBaseInfo", "Finish extractBaseInfo from the file {}", elementExtractor.filePath);
            HashMap res = new HashMap<String, String>();
            res.put("cpcPrefixId", cpcPrefixId);
            res.put("zosPrefixId", zosPrefixId);
            res.put("smfId", smfId);
            res.put("lparName", lparName);
            res.put("scanDate", elementExtractor.createTimestamp);
            return res;
        } else {
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "extractBaseInfo", "Failed to extract BaseInfo from the file {}", elementExtractor.filePath);
            return null;
        }
    }
}
