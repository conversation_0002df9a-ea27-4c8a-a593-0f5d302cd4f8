/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2024-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.parser;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2BufferPool;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2Database;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2StoredProcedure;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2Table;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2TableSpace;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQAliasQueue;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQAuthInfo;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQBufferPool;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQClientConnectionChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQClusterReceiverChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQClusterSenderChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQLocalQueue;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQModelQueue;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQReceiverChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQRemoteQueue;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQSenderChannel;
import com.ibm.palantir.catelyn.jaxb.AppMessagingMqMQServerConnectionChannel;
import com.ibm.palantir.catelyn.jaxb.AppProcessPool;
import com.ibm.palantir.catelyn.jaxb.NetBindAddress;
import com.ibm.palantir.catelyn.jaxb.NetFqdn;
import com.ibm.palantir.catelyn.jaxb.NetIpAddress;
import com.ibm.palantir.catelyn.jaxb.NetIpInterface;
import com.ibm.palantir.catelyn.jaxb.NetTcpPort;
import com.ibm.palantir.catelyn.jaxb.NetUdpPort;
import com.ibm.palantir.catelyn.jaxb.SysZOSAddressSpace;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSFile;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSPlex;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSProgram;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSRegion;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSTransaction;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2Conn;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2DataSharingGroup;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2Subsystem;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSDatabase;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSProgram;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSSubsystem;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSSysplexGroup;
import com.ibm.palantir.catelyn.jaxb.SysZOSIMSTransaction;
import com.ibm.palantir.catelyn.jaxb.SysZOSLPAR;
import com.ibm.palantir.catelyn.jaxb.SysZOSMQQueueSharingGroup;
import com.ibm.palantir.catelyn.jaxb.SysZOSMQSubsystem;
import com.ibm.palantir.catelyn.jaxb.SysZOSSysplex;
import com.ibm.palantir.catelyn.jaxb.SysZOSZOS;
import com.ibm.palantir.catelyn.jaxb.SysZOSZReportFile;
import com.ibm.palantir.catelyn.jaxb.SysZOSZSeriesComputerSystem;
import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CFComputerSystemMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CFLPARMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CICSDB2ConnMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CICSPlexMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CICSRegionMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CICSSystemInitTableMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.CICSSystemInitTableOverrideMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.DB2DataSharingGroupMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.DB2SubsystemMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.FqdnMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.IMSSubsystemMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.IMSSysplexGroupMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.LPARMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQQueueSharingGroupMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.MQSubsystemMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.PRSMLPARMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.SysplexMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.ZOSMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.meta.ZSeriesComputerMeta;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CFComputerSystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CFLPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSDB2Conn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSPlex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTable;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTableOverride;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2DataSharingGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Subsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Fqdn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSSubsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IMSSysplexGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.LPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQQueueSharingGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.MQSubsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.PRSMLPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Sysplex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZOS;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSeriesComputer;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSubSystem;
import com.ibm.palantir.catelyn.jpa.repository.dla.AddressSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.BindAddressRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CFComputerSystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CFLPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSDB2ConnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSFileRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSPlexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSRegionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableOverrideRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2BufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DataSharingGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2StoredProcedureRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2SubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.FqdnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSDatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSSysplexGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IdmlOperationTimeRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IpAddressRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IpInterfaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.LPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAliasQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAuthInfoRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQBufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClientConnectionChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClusterReceiverChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQClusterSenderChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQLocalQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQModelQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQQueueSharingGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQReceiverChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQRemoteQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSenderChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQServerConnectionChannelRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.PRSMLPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ProcessPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipServiceNowRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.SysplexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.TcpPortRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.UdpPortRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZOSRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSeriesComputerRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSubSystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.AddressSpaceMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.BindAddressMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CFComputerSystemMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CFLPARMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSDB2ConnMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSFileMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSPlexMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSProgramMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSRegionMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSSystemInitTableMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSSystemInitTableOverrideMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSTransactionMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2BufferPoolMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2DataSharingGroupMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2DatabaseMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2StoredProcedureMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2SubsystemMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2TableMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2TableSpaceMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.FqdnMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.IMSDatabaseMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.IMSProgramMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.IMSSubsystemMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.IMSSysplexGroupMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.IMSTransactionMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.IpAddressMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.IpInterfaceMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.LPARMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQAliasQueueMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQAuthInfoMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQBufferPoolMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQClientConnectionChannelMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQClusterReceiverChannelMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQClusterSenderChannelMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQLocalQueueMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQModelQueueMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQQueueSharingGroupMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQReceiverChannelMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQRemoteQueueMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQSenderChannelMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQServerConnectionChannelMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.MQSubsystemMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.PRSMLPARMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.ProcessPoolMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.RelationshipMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.RelationshipServiceNowMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.SysplexMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.TcpPortMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.UdpPortMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.ZOSMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.ZSeriesComputerMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.ZSubSystemMetaRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.exception.ServiceExceptionStore;
import com.ibm.palantir.sansa.extractor.ElementExtractor;
import com.ibm.palantir.sansa.loader.DB2ElementLoader;
import com.ibm.palantir.sansa.loader.ElementLoader;
import com.ibm.palantir.sansa.loader.IMSElementLoader;
import com.ibm.palantir.sansa.loader.Layer4CICSLoader;
import com.ibm.palantir.sansa.loader.MQElementLoader;
import com.ibm.palantir.sansa.loader.OperationTimeLoader;
import com.ibm.palantir.sansa.loader.RelationshipLoader;
import com.ibm.palantir.sansa.loader.RelationshipServiceNowLoader;
import com.ibm.palantir.sansa.loader.ZOSTaskLoader;
import com.ibm.palantir.sansa.loader.ZReportFileLoader;
import com.ibm.palantir.sansa.utils.CommonUtils;
import com.ibm.palantir.sansa.utils.PipeCounter;

public class DLADataParser {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = DLADataParser.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private ElementExtractor currentFileElementExtractor;
    private ElementExtractor previousFileElementExtractor;
    private String scanDate;
    private HashMap<String, Object> repos;
    private ElementLoader elementLoader;
    private ZReportFileLoader zReportFileLoader;
    private Layer4CICSLoader layer4CICSLoader;
    private DB2ElementLoader db2ElementLoader;
    private RelationshipLoader relationshipLoader;
    private ZOSTaskLoader zosTaskLoader;
    private OperationTimeLoader operationTimeLoader;
    private MQElementLoader mqElementLoader;
    private IMSElementLoader imsElementLoader;
    private RelationshipServiceNowLoader relationshipServiceNowLoader;

    private List<String> filterIds;
    private List<ZSubSystem> zSubSystemList;
    PipeCounter pipeCounter;
    // used for CPC && Sysplex && LPAR
    private String cpcPrefixId;
    // used for other Objects
    private String zosPrefixId;
    private String smfId;
    private String lparName;
    private String previousFileCpcPrefixId;
    private String previousFileZosPrefixId;
    private String cicsSubSystemName;
    private String db2SubSystemName;
    private String mqSubSystemName;
    private String imsSubSystemName;
    private String cicsSubSystemId;
    private String db2SubSystemId;
    private String mqSubSystemId;
    private String imsSubSystemId;
    private String db2DataSharingGroupName;
    private String mqDataSharingGroupName;
    private String imsDataSharingGroupName;
    private final List<String> ciIdentifierList = new ArrayList<>();
    private final CommonUtils commonUtils;

    String pipelineErrorCode = ErrorCode.PipelineError.getCodeStr();
    private static final ServiceExceptionStore errorStore = ServiceExceptionStore.getInstance();

    // filterIds, cpcPrefixId, zosPrefixId -> need these to load relationship data
    // for previous file.

    // Used for the PopulateDeltaDLA, need to specify the scanDate which is
    // extracted out of the delta IDML book
    public DLADataParser(ElementExtractor elementExtractor, ElementExtractor previousFileElementExtractor,
            String cpcPrefixId, String zosPrefixId, String smfId,
            String lparName, String scanDate, String previousFileCpcPrefixId, String previousFileZosPrefixId) {
        this.currentFileElementExtractor = elementExtractor;
        this.previousFileElementExtractor = previousFileElementExtractor;
        this.scanDate = scanDate;
        this.cpcPrefixId = cpcPrefixId;
        this.zosPrefixId = zosPrefixId;
        this.smfId = smfId;
        this.lparName = lparName;
        this.previousFileCpcPrefixId = previousFileCpcPrefixId;
        this.previousFileZosPrefixId = previousFileZosPrefixId;
        this.init();
        this.commonUtils = new CommonUtils();
    }

    // Used for PopulateDLA using the local createTimestamp as scanDate
    public DLADataParser(ElementExtractor elementExtractor, ElementExtractor previousFileElementExtractor,
            String previousFileCpcPrefixId, String previousFileZosPrefixId) {
        this.currentFileElementExtractor = elementExtractor;
        this.previousFileElementExtractor = previousFileElementExtractor;
        this.scanDate = elementExtractor.createTimestamp;
        this.cpcPrefixId = "";
        this.zosPrefixId = "";
        this.smfId = "";
        this.lparName = "";
        this.previousFileCpcPrefixId = previousFileCpcPrefixId;
        this.previousFileZosPrefixId = previousFileZosPrefixId;
        this.init();
        this.commonUtils = new CommonUtils();
    }

    public PipeCounter populateDlaFromIdml(boolean isDelta, boolean isMeta) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                "Running populateDlaFromIdml with params - isDelta: {}, isMeta: {}", isDelta, isMeta);
        pipeCounter = new PipeCounter();
        filterIds = new ArrayList<>();
        zSubSystemList = new ArrayList<>();
        List<Object> currentFileRelationshipList;
        this.ciIdentifierList.clear();

        // 1st, generate the global cpcPrefixId ZOSLPAR
        List<Object> sysZOSLPARElementList = currentFileElementExtractor
                .getElementListByClassName(SysZOSLPAR.class.getName());
        // Prioritize LPAR processing to ensure the generation of the cpcPrefixId
        if (!sysZOSLPARElementList.isEmpty()) {
            for (Object sysZOSLPARElement : sysZOSLPARElementList) {

                if (sysZOSLPARElement instanceof SysZOSLPAR) {
                    SysZOSLPAR sysZOSLPAR = (SysZOSLPAR) sysZOSLPARElement;

                    if (sysZOSLPAR.getLabel() != null && !sysZOSLPAR.getId().contains("PRSM")) {
                        Object result = elementLoader.loadLpar(sysZOSLPAR, scanDate, isMeta, ciIdentifierList);

                        if (result != null) {
                            if (result instanceof LPARMeta) {
                                LPARMeta lPARMeta = (LPARMeta) result;
                                this.lparName = lPARMeta.getLparName();
                                cpcPrefixId = lPARMeta.getPrefixId();
                                pipeCounter.uniqueCount("LPARMeta", sysZOSLPAR.getId());
                            } else if (result instanceof LPAR) {
                                LPAR lpar = (LPAR) result;
                                this.lparName = lpar.getLparName();
                                // This is the first time setting the global parameter cpcPrefixId here
                                cpcPrefixId = lpar.getPrefixId();
                                pipeCounter.uniqueCount("LPAR", sysZOSLPAR.getId());
                            } else {
                                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                                        "Unexpected return type from loadLpar: " + result);
                                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                        "IDML Processing Error: {}", msg);
                            }
                        } else {
                            // Can't generate cpcPrefixId if LPAR is null
                            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                                    "Failed to parse the LPAR and couldn't generate the cpcPrefixId. Please check the IDML book with filePath: "
                                            + currentFileElementExtractor.filePath);
                            LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                    "IDML Processing Error: {}", msg);
                            errorStore.addError(msg);
                            return null;
                        }
                    }
                } else {
                    String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                            "An item is not a valid SysZOSLPAR: " + sysZOSLPARElement
                                    + ", Please check the IDML book with filePath: "
                                    + currentFileElementExtractor.filePath);
                    LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "populateDlaFromIdml", "IDML Processing Error: {}",
                            msg);
                    errorStore.addError(msg);
                }
            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "finish loadLpar, count: {}",
                    sysZOSLPARElementList.size());
        }

        // check the global cpcPrefixId
        if (cpcPrefixId.isEmpty()) {
            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                    "failed to generate the cpcPrefixId from LPAR, Please check the IDML book with filePath: "
                            + currentFileElementExtractor.filePath);
            LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "populateDlaFromIdml", "IDML Processing Error: {}", msg);
            errorStore.addError(msg);
            return null;
        }

        // ZSeriesComputer + CFComputerSystem
        ZSeriesComputer zSeriesComputer = null;
        ZSeriesComputerMeta zSeriesComputerMeta = null;
        List<Object> sysZOSZSeriesComputerSystemElementList = currentFileElementExtractor
                .getElementListByClassName(SysZOSZSeriesComputerSystem.class.getName());
        if (!sysZOSZSeriesComputerSystemElementList.isEmpty()) {
            for (Object sysZOSZSeriesComputerSystemElement : sysZOSZSeriesComputerSystemElementList) {

                if (sysZOSZSeriesComputerSystemElement instanceof SysZOSZSeriesComputerSystem) {
                    SysZOSZSeriesComputerSystem sysZOSZSeriesComputerSystem = (SysZOSZSeriesComputerSystem) sysZOSZSeriesComputerSystemElement;

                    if (sysZOSZSeriesComputerSystem.getLabel() == null) {
                        parseCFComputerSystem(sysZOSZSeriesComputerSystem, isMeta);
                    } else {
                        // generate the prefixId by the ZSeriesComputer itself, and then to Verify the
                        // global cpcPrefixId with it
                        Object result = elementLoader.loadZSeriesComputer(sysZOSZSeriesComputerSystem,
                                scanDate, isMeta);

                        if (result instanceof ZSeriesComputerMeta) {
                            zSeriesComputerMeta = (ZSeriesComputerMeta) result;
                            if (zSeriesComputerMeta != null) {
                                pipeCounter.uniqueCount("ZSeriesComputerMeta", sysZOSZSeriesComputerSystem.getId());
                            } else {
                                pipeCounter.addFailedDlaId(sysZOSZSeriesComputerSystem.getId());
                            }
                        } else if (result instanceof ZSeriesComputer) {
                            zSeriesComputer = (ZSeriesComputer) result;
                            if (zSeriesComputer != null) {
                                pipeCounter.uniqueCount("ZSeriesComputer", sysZOSZSeriesComputerSystem.getId());
                            } else {
                                pipeCounter.addFailedDlaId(sysZOSZSeriesComputerSystem.getId());
                            }
                        } else {
                            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                                    "Unexpected return type from loadZSeriesComputer: " + result);
                            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                    "IDML Processing Error: {}", msg);
                        }
                    }
                } else {
                    String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                            "An item is not a valid SysZOSZSeriesComputerSystem: " + sysZOSZSeriesComputerSystemElement
                                    + ", Please check the IDML book with filePath: "
                                    + currentFileElementExtractor.filePath);
                    LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "populateDlaFromIdml", "IDML Processing Error: {}",
                            msg);
                    errorStore.addError(msg);
                }
            }
            Integer cfcpCount = sysZOSZSeriesComputerSystemElementList.size() - 1;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                    "finish loadZSeriesComputer && loadCFComputerSystem, count ZSeriesComputer 1 , CFComputerSystem {}",
                    cfcpCount);
        }

        // Verify the consistency of CPC properties between ZSeriesComputer and LPAR,
        // only for ZOSBASE
        if (isMeta) {
            if (zSeriesComputerMeta != null) {
                if (!zSeriesComputerMeta.getPrefixId().equals(cpcPrefixId)) {
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "The ZSeriesComputerMeta and LPARMeta have different cpcPrefixId, please check the ZOSBASE file.");
                }
            }
        } else {
            if (zSeriesComputer != null) {
                if (!zSeriesComputer.getPrefixId().equals(cpcPrefixId)) {
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "The ZSeriesComputer and LPAR have different cpcPrefixId, please check the ZOSBASE file.");
                }
            }
        }

        // save the idml:create.timestamp in IdmlOperationTime object
        // it requires that the processManagementSoftwareSystem should not be None
        operationTimeLoader.loadCreateTimestamp(
                currentFileElementExtractor.processManagementSoftwareSystem.getLabel(), cpcPrefixId, scanDate);

        // There have CFLPAR and PRSMLPAR, not only the LPAR
        if (sysZOSLPARElementList.size() > 1) {
            Integer b = 0; // count CFLPAR
            Integer c = 0; // count PRSMLPAR
            for (Object sysZOSLPARElement : sysZOSLPARElementList) {
                if (sysZOSLPARElement instanceof SysZOSLPAR) {
                    SysZOSLPAR sysZOSLPAR = (SysZOSLPAR) sysZOSLPARElement;

                    // filter PR/SM LPAR and Coupling Facility LPAR
                    if (sysZOSLPAR.getLabel() == null) {
                        Object result = elementLoader.loadCFLpar(sysZOSLPAR, cpcPrefixId, scanDate, isMeta);

                        if (result instanceof CFLPARMeta) {
                            CFLPARMeta cfLparMeta = (CFLPARMeta) result;
                            if (cfLparMeta == null) {
                                pipeCounter.addFailedDlaId(sysZOSLPAR.getId());
                            } else {
                                b += 1;
                                pipeCounter.uniqueCount("CFLPAR", sysZOSLPAR.getId());
                                filterIds.add(sysZOSLPAR.getId());
                            }
                        } else if (result instanceof CFLPAR) {
                            CFLPAR cflpar = (CFLPAR) result;
                            if (cflpar == null) {
                                pipeCounter.addFailedDlaId(sysZOSLPAR.getId());
                            } else {
                                b += 1;
                                pipeCounter.uniqueCount("CFLPAR", sysZOSLPAR.getId());
                                filterIds.add(sysZOSLPAR.getId());
                            }
                        } else {
                            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                                    "Unexpected return type from loadCFLpar: " + result);
                            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                    "IDML Processing Error: {}", msg);
                        }
                    } else if (sysZOSLPAR.getId().contains("PRSM")) {
                        Object result = elementLoader.loadPRSMLpar(sysZOSLPAR, scanDate, isMeta);

                        if (result instanceof PRSMLPARMeta) {
                            PRSMLPARMeta prsmLparMeta = (PRSMLPARMeta) result;
                            if (prsmLparMeta == null) {
                                pipeCounter.addFailedDlaId(sysZOSLPAR.getId());
                            } else {
                                c += 1;
                                pipeCounter.uniqueCount("PRSMLPARMeta", sysZOSLPAR.getId());
                                filterIds.add(sysZOSLPAR.getId());
                            }
                        } else if (result instanceof PRSMLPAR) {
                            PRSMLPAR prsmlpar = (PRSMLPAR) result;
                            if (prsmlpar == null) {
                                pipeCounter.addFailedDlaId(sysZOSLPAR.getId());
                            } else {
                                c += 1;
                                pipeCounter.uniqueCount("PRSMLPAR", sysZOSLPAR.getId());
                                filterIds.add(sysZOSLPAR.getId());
                            }
                        } else {
                            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                                    "Unexpected return type from loadPRSMLpar: " + result);
                            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                    "IDML Processing Error: {}", msg);
                        }

                    }
                } else {
                    String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                            "An item is not a valid sysZOSLPAR: " + sysZOSLPARElement
                                    + ", Please check the IDML book with filePath: "
                                    + currentFileElementExtractor.filePath);
                    LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "populateDlaFromIdml", "IDML Processing Error: {}",
                            msg);
                    errorStore.addError(msg);
                }
            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                    String.format("finish loadCFLPAR && loadPRSMLPAR, count: %d, %d, respectively.", b, c));
        }

        // Sysplex
        SysZOSSysplex sysZOSSysplex = (SysZOSSysplex) currentFileElementExtractor
                .getElementByClassName(SysZOSSysplex.class.getName());
        if (sysZOSSysplex != null) {
            parseSysZOSSysplex(sysZOSSysplex, isMeta);
        }

        // ZOS
        SysZOSZOS sysZOSZOS = (SysZOSZOS) currentFileElementExtractor.getElementByClassName(SysZOSZOS.class.getName());
        ZOS zos = null;
        ZOSMeta zOSMeta = null;
        // under the delta mode, sysZOSZOS.getLabel() is null
        if (sysZOSZOS != null && sysZOSZOS.getLabel() != null) {
            Object result = elementLoader.loadZosZos(sysZOSZOS, cpcPrefixId, lparName, scanDate, isMeta,
                    ciIdentifierList);
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "finish loadZosZos {}",
                    sysZOSZOS.getId());

            if (result instanceof ZOSMeta) {
                zOSMeta = (ZOSMeta) result;
                if (zOSMeta != null) {
                    // under the global mode, use the current ZOS' smfId
                    zosPrefixId = zOSMeta.getPrefixId();
                    pipeCounter.uniqueCount("ZOSMeta", zosPrefixId);
                } else {
                    // Can't get zosMetaPrefixId if zos is null
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "Failed to parse the zOSMeta, and couldn't retrieve the zosPrefixId. Please check the IDML book with filePath: "
                                    + currentFileElementExtractor.filePath);
                    return null;
                }
            } else if (result instanceof ZOS) {
                zos = (ZOS) result;
                if (zos != null) {
                    // under the global mode, use the current ZOS' smfId
                    zosPrefixId = zos.getPrefixId();
                    commonUtils.setValueAtIndex(ciIdentifierList, 4, zos.getSmfId());
                    commonUtils.setValueAtIndex(ciIdentifierList, 5, zos.getName());
                    pipeCounter.uniqueCount("ZOS", zosPrefixId);
                } else {
                    // Can't get zosPrefixId if zos is null
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "Failed to parse the ZOS metadata and couldn't retrieve the zosPrefixId. Please check the IDML book with filePath: "
                                    + currentFileElementExtractor.filePath);
                    return null;
                }

            } else {
                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                        "Unexpected return type from loadZosZos: " + result);
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml", "IDML Processing Error: {}", msg);
            }
        }

        // These 3 Net info are shared in each IDML books
        // Fqdn
        NetFqdn netFqdn = (NetFqdn) currentFileElementExtractor.getElementByClassName(NetFqdn.class.getName());
        if (netFqdn != null) {
            parseNetFqdn(netFqdn, isMeta);
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "finish parseNetFqdn");

        // IpAddress
        List<Object> ipAddressElements = currentFileElementExtractor
                .getElementListByClassName(NetIpAddress.class.getName());
        if (!ipAddressElements.isEmpty()) {
            parseNetIpAddress(ipAddressElements, isMeta);
        }

        // IpInterface
        List<Object> ipInterfaceElements = currentFileElementExtractor
                .getElementListByClassName(NetIpInterface.class.getName());
        if (!ipInterfaceElements.isEmpty()) {
            parseNetIpInterface(ipInterfaceElements, isMeta);
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "finish loadIpInterfaceList, count: {}",
                    ipInterfaceElements.size());
        }

        // BindAddress
        List<Object> bindAddressElements = currentFileElementExtractor
                .getElementListByClassName(NetBindAddress.class.getName());
        if (!bindAddressElements.isEmpty()) {
            parseNetBindAddress(bindAddressElements, isMeta);
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "finish loadBindAddressList, count: {}",
                    bindAddressElements.size());
        }

        // 3rd, process the special CICSResources and DB2Resources
        // only the manually generated IDML book of CICS or DB2 resources don't have
        // ProcessManagementSoftwareSystem
        if (currentFileElementExtractor.processManagementSoftwareSystem == null
                || currentFileElementExtractor.processManagementSoftwareSystem.getHostname() == null) {
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                    "This IDML book is generated for CICSResources or DB2Resources");

            // DB2CONN between CICS and Db2
            List<Object> sysZOSDB2ConnElementList = currentFileElementExtractor

                    .getElementListByClassName(SysZOSDB2Conn.class.getName());
            if (!sysZOSDB2ConnElementList.isEmpty()) {
                parseSysZOSDB2Conn(sysZOSDB2ConnElementList, isMeta);
            }

            // CICSPlex
            List<Object> sysZOSCICSPlexElementList = currentFileElementExtractor
                    .getElementListByClassName(SysZOSCICSPlex.class.getName());
            if (!sysZOSCICSPlexElementList.isEmpty()) {
                parseSysZOSCICSPlex(sysZOSCICSPlexElementList, isMeta);
            }

            // Db2Table
            List<Object> appDB2TableElementList = currentFileElementExtractor
                    .getElementListByClassName(AppDbDb2Db2Table.class.getName());
            if (!appDB2TableElementList.isEmpty()) {
                parseAppDbDb2Db2Table(appDB2TableElementList, isMeta);
            }

            // CICSRegion MemberOf CICSPlex and so on
            filterIds.addAll(pipeCounter.getFailedDlaIds()); // need to filter all failed ids
            currentFileRelationshipList = currentFileElementExtractor.getRelationshipElementList();

            relationshipLoader.loadRelationshipList(currentFileRelationshipList,
                    filterIds, cpcPrefixId, zosPrefixId, scanDate, isMeta);

            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                    "finish loading relationships in CICSResources or DB2Resources. count: {}",
                    currentFileRelationshipList.size());

            return pipeCounter; // If remove this "return", u will get an NPE.
        }

        // 4th, parse different objects
        String hostname = currentFileElementExtractor.processManagementSoftwareSystem.getHostname();
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "This IDML book's hostname: {}", hostname);

        // Determine whether ZOSTASK
        if (hostname.toUpperCase().contains("ZOSTASK")) {

            // AddressSpace
            List<Object> addressSpaceElements = currentFileElementExtractor
                    .getElementListByClassName(SysZOSAddressSpace.class.getName());
            if (!addressSpaceElements.isEmpty()) {
                parseSysZOSAddressSpace(addressSpaceElements, isMeta);
            }

            // ProcessPool
            List<Object> processPoolElements = currentFileElementExtractor
                    .getElementListByClassName(AppProcessPool.class.getName());
            if (!processPoolElements.isEmpty()) {
                parseAppProcessPool(processPoolElements, isMeta);
            }

            // TcpPort
            List<Object> tcpPortElements = currentFileElementExtractor
                    .getElementListByClassName(NetTcpPort.class.getName());
            if (!tcpPortElements.isEmpty()) {
                parseNetTcpPort(tcpPortElements, isMeta);
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                        "finish loadTcpPortList in ZOSTASK, count: {}", tcpPortElements.size());
            }

            // UdpPort
            List<Object> udpPortElements = currentFileElementExtractor
                    .getElementListByClassName(NetUdpPort.class.getName());
            if (!udpPortElements.isEmpty()) {
                parseNetUdpPort(udpPortElements, isMeta);
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                        "finish loadUdpPortList in ZOSTASK, count: {}", udpPortElements.size());
            }

            // Below Objects are multiple in ZOSTASK, but single exists in each subsystem
            // file
            List<Object> cicsRegionElements = currentFileElementExtractor
                    .getElementListByClassName(SysZOSCICSRegion.class.getName());
            if (!cicsRegionElements.isEmpty()) {
                parseSysZOSCICSRegion(cicsRegionElements, isMeta);
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                        "finish loadCICSRegionList in ZOSTASK, count: {}", cicsRegionElements.size());
            }

            List<SysZOSZReportFile> cicsSitZReportFileList = currentFileElementExtractor.getZReportFiles("CICS_SIT");
            if (!cicsSitZReportFileList.isEmpty()) {
                for (SysZOSZReportFile zoszReportFile : cicsSitZReportFileList) {
                    String dlaId = zoszReportFile.getId();
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            String.format("get ZReportFile %s in ZOSTASK.", dlaId));
                    String name = zoszReportFile.getName();
                    String fixedPath = zoszReportFile.getFixedPath();
                    String content = zoszReportFile.getContent();

                    if (fixedPath.equals("CICS_SIT")) {
                        parseCicsSit(dlaId, name, content, isMeta);
                    } else if (fixedPath.equals("CICS_SIT_Overrides")) {
                        parseCicsSitOverrides(dlaId, name, content, isMeta);

                    }
                }
            }

            List<Object> db2DataSharingGroupElements = currentFileElementExtractor
                    .getElementListByClassName(SysZOSDB2DataSharingGroup.class.getName());
            if (!db2DataSharingGroupElements.isEmpty()) {
                for (Object db2DataSharingGroupElement : db2DataSharingGroupElements) {
                    parseSysZOSDB2DataSharingGroup(db2DataSharingGroupElement, isMeta);
                }
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "finish loadDB2DataSharingGroupList in ZOSTASK, count: {}", db2DataSharingGroupElements.size());
            }

            List<Object> db2SubsystemElements = currentFileElementExtractor
                    .getElementListByClassName(SysZOSDB2Subsystem.class.getName());
            if (!db2SubsystemElements.isEmpty()) {
                parseSysZOSDB2Subsystem(db2SubsystemElements, isMeta);
            }

            List<Object> db2StoredProcedureElementList = currentFileElementExtractor
                    .getElementListByClassName(AppDbDb2Db2StoredProcedure.class.getName());
            if (!db2StoredProcedureElementList.isEmpty()) {
                parseAppDbDb2Db2StoredProcedure(db2StoredProcedureElementList, isMeta);
            }

            List<Object> mqQueueSharingGroupElements = currentFileElementExtractor
                    .getElementListByClassName(SysZOSMQQueueSharingGroup.class.getName());

            if (!mqQueueSharingGroupElements.isEmpty()) {
                parseSysZOSMQQueueSharingGroup(mqQueueSharingGroupElements, isMeta);
            }

            List<Object> mqSubsystemElements = currentFileElementExtractor
                    .getElementListByClassName(SysZOSMQSubsystem.class.getName());
            if (!mqSubsystemElements.isEmpty()) {
                parseSysZOSMQSubsystem(mqSubsystemElements, isMeta);
            }

            List<Object> imsSysplexGroupElements = currentFileElementExtractor
                    .getElementListByClassName(SysZOSIMSSysplexGroup.class.getName());
            if (!imsSysplexGroupElements.isEmpty()) {
                parseSysZOSIMSSysplexGroup(imsSysplexGroupElements, isMeta);
            }

            List<Object> imsSubsystemElements = currentFileElementExtractor
                    .getElementListByClassName(SysZOSIMSSubsystem.class.getName());
            if (!imsSubsystemElements.isEmpty()) {
                parseSysZOSIMSSubsystem(imsSubsystemElements, isMeta);
            }

        } else {
            // ----------------------------------------------CICS--------------------------------------------------------
            // CICSRegion and ZSubSystem
            SysZOSCICSRegion sysZOSCICSRegion = (SysZOSCICSRegion) currentFileElementExtractor
                    .getElementByClassName(SysZOSCICSRegion.class.getName());
            if (sysZOSCICSRegion != null) {
                CICSRegion cicsRegion = null;
                CICSRegionMeta cicsRegionMeta = null;

                // Compatibility handling for the DeltaBooks
                if (sysZOSCICSRegion.getLabel() == null) {
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            String.format("Skipped the sysZOSCICSRegion %s because missing the label field.",
                                    sysZOSCICSRegion.getId()));
                } else {
                    Object result = elementLoader.loadCICSRegion(sysZOSCICSRegion, zosPrefixId, scanDate, isMeta,
                            ciIdentifierList);
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "finish loadCICSRegion {}",
                            sysZOSCICSRegion.getId());

                    if (result instanceof CICSRegionMeta) {
                        cicsRegionMeta = (CICSRegionMeta) result;
                        if (cicsRegionMeta == null) {
                            pipeCounter.addFailedDlaId(sysZOSCICSRegion.getId());
                        } else {
                            pipeCounter.uniqueCount("CICSRegionMeta", cicsRegionMeta.getDlaId());
                            // create ZSubSystem from cicsRegionMeta
                            elementLoader.loadZSubSystemFromCICSRegion(sysZOSCICSRegion,
                                    zosPrefixId, scanDate, isMeta);
                            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                    "finish loadZSubSystemMetaFromCICSRegion");
                        }
                    } else if (result instanceof CICSRegion) {
                        cicsRegion = (CICSRegion) result;
                        if (cicsRegion == null) {
                            pipeCounter.addFailedDlaId(sysZOSCICSRegion.getId());
                        } else {
                            pipeCounter.uniqueCount("CICSRegion", cicsRegion.getDlaId());
                            cicsSubSystemName = cicsRegion.getRegionName();
                            cicsSubSystemId = cicsRegion.getDlaId();
                            // create ZSubSystem from CICSRegion
                            Object loadResult = elementLoader.loadZSubSystemFromCICSRegion(sysZOSCICSRegion,
                                    zosPrefixId, scanDate, isMeta);
                            if (loadResult instanceof ZSubSystem) {
                                ZSubSystem zSubSystem = (ZSubSystem) loadResult;
                                // add its id to update DLADataTrace's scanDate
                                if (zSubSystem != null) {
                                    this.zSubSystemList.add(zSubSystem);
                                }
                                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                        "finish loadZSubSystemFromCICSRegion");
                            }
                        }
                    } else {
                        String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                                "Unexpected return type from loadCICSRegion: " + result);
                        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml", "IDML Processing Error: {}",
                                msg);
                    }
                }

                // check if there has new CICSTransaction, CICSProgram and CICSFile Objects
                // parse CICS Program Objects
                List<Object> CICSProgramElementList = currentFileElementExtractor
                        .getElementListByClassName(SysZOSCICSProgram.class.getName());
                if (!CICSProgramElementList.isEmpty()) {
                    parseSysZOSCICSProgram(CICSProgramElementList, isMeta);

                }

                List<Object> CICSTransactionElementList = currentFileElementExtractor
                        .getElementListByClassName(SysZOSCICSTransaction.class.getName());
                // parse CICS Transaction Objects
                if (!CICSTransactionElementList.isEmpty()) {
                    parseSysZOSCICSTransaction(CICSTransactionElementList, isMeta);

                }

                List<Object> CICSFileElementList = currentFileElementExtractor
                        .getElementListByClassName(SysZOSCICSFile.class.getName());
                if (!CICSFileElementList.isEmpty()) {
                    parseSysZOSCICSFile(CICSFileElementList, isMeta);

                }

                // CICS ZOSZReportFile
                List<SysZOSZReportFile> cicsZReportFileList = currentFileElementExtractor.getZReportFiles("CICS");
                for (SysZOSZReportFile zoszReportFile : cicsZReportFileList) {

                    String dlaId = zoszReportFile.getId();
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            String.format("get ZReportFile %s in CICS Subsystem File.", dlaId));
                    String name = zoszReportFile.getName();

                    // The delta book doesn't have this attribute, so we have to skip it.
                    if (zoszReportFile.getFixedPath() == null) {
                        continue;
                    }

                    String fixedPath = zoszReportFile.getFixedPath();
                    String content = zoszReportFile.getContent();
                    // under the global mode, use the current ZOS' smfId
                    if (zos != null) {
                        this.smfId = zos.getSmfId();
                    }
                    Integer itemCount;

                    if (fixedPath.equals("CICS_SIT")) {
                        parseCICS_SIT(dlaId, name, content, isMeta);

                    } else if (fixedPath.equals("CICS_SIT_Overrides")) {

                        parseCICS_SIT_Overrides(dlaId, name, content, isMeta);

                    } else if (fixedPath.equals("Programs") && CICSProgramElementList.isEmpty()) {
                        if (cicsRegion == null) {
                            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml", String
                                    .format("CICSRegion is null, skip to loadPrograms from ZReportFile {}", dlaId));
                            continue;
                        }
                        itemCount = zReportFileLoader.loadPrograms(content, cicsRegion, smfId, scanDate, isMeta);

                        pipeCounter.count("CICSProgram", itemCount);
                        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                String.format("finish loadPrograms from ZReportFile %s, count: %d", dlaId, itemCount));
                    } else if (fixedPath.equals("Files") && CICSFileElementList.isEmpty()) {
                        if (cicsRegion == null) {
                            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                    "CICSRegion is null, skip to loadCICSFiles from ZReportFile {}", dlaId);
                            continue;
                        }
                        itemCount = zReportFileLoader.loadCICSFiles(content, cicsRegion, smfId, scanDate, isMeta);
                        pipeCounter.count("CICSFile", itemCount);
                        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                String.format("finish loadCICSFiles from ZReportFile %s, count: %d", dlaId, itemCount));
                    } else if (fixedPath.equals("Transactions") && CICSTransactionElementList.isEmpty()) {
                        if (cicsRegion == null) {
                            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                    "CICSRegion is null, skip to loadTransactions from ZReportFile {}", dlaId);
                            continue;
                        }
                        // do it for those files without new CICSTransaction Object
                        itemCount = zReportFileLoader.loadTransactions(content, cicsRegion, smfId, scanDate, isMeta);
                        pipeCounter.count("CICSTransaction", itemCount);
                        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                String.format("finish loadTransactions from ZReportFile %s, count: %d", dlaId,
                                        itemCount));
                    }
                }
            }

            // ----------------------------------------------DB2--------------------------------------------------------
            SysZOSDB2DataSharingGroup sysZOSDB2DataSharingGroup = (SysZOSDB2DataSharingGroup) currentFileElementExtractor
                    .getElementByClassName(SysZOSDB2DataSharingGroup.class.getName());

            if (sysZOSDB2DataSharingGroup != null) {
                parseSysZOSDB2DataSharingGroup(sysZOSDB2DataSharingGroup, isMeta);

            }

            SysZOSDB2Subsystem sysZOSDB2Subsystem = (SysZOSDB2Subsystem) currentFileElementExtractor
                    .getElementByClassName(SysZOSDB2Subsystem.class.getName());
            if (sysZOSDB2Subsystem != null) {
                // Compatibility handling for the DeltaBooks
                if (sysZOSDB2Subsystem.getLabel() == null) {
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            String.format("Skipped the sysZOSDB2Subsystem %s because missing the label field.",
                                    sysZOSDB2Subsystem.getId()));
                } else {
                    commonUtils.setValueAtIndex(ciIdentifierList, 6, db2DataSharingGroupName);
                    Object loadResult = db2ElementLoader.loadDB2Subsystem(sysZOSDB2Subsystem, zosPrefixId, scanDate,
                            isMeta, ciIdentifierList);
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "finish loadDB2Subsystem {}",
                            sysZOSDB2Subsystem.getId());

                    if (loadResult instanceof DB2SubsystemMeta) {
                        DB2SubsystemMeta dB2SubsystemMeta = (DB2SubsystemMeta) loadResult;
                        if (dB2SubsystemMeta == null) {
                            pipeCounter.addFailedDlaId(sysZOSDB2Subsystem.getId());
                        } else {
                            pipeCounter.uniqueCount("DB2SubsystemMeta", sysZOSDB2Subsystem.getId());
                            // create ZSubSystem from DB2Subsystem
                            elementLoader.loadZSubSystemFromDB2(sysZOSDB2Subsystem, zosPrefixId,
                                    scanDate, isMeta);
                            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                    "finish loadZSubSystemMetaFromDB2");
                        }

                    } else if (loadResult instanceof DB2Subsystem) {
                        DB2Subsystem dB2Subsystem = (DB2Subsystem) loadResult;
                        if (dB2Subsystem == null) {
                            pipeCounter.addFailedDlaId(sysZOSDB2Subsystem.getId());
                        } else {
                            db2SubSystemName = dB2Subsystem.getSubSystemName();
                            db2SubSystemId = dB2Subsystem.getDlaId();
                            pipeCounter.uniqueCount("DB2Subsystem", sysZOSDB2Subsystem.getId());
                            // create ZSubSystem from DB2Subsystem
                            Object result = elementLoader.loadZSubSystemFromDB2(sysZOSDB2Subsystem, zosPrefixId,
                                    scanDate, isMeta);
                            if (result instanceof ZSubSystem) {
                                ZSubSystem zSubSystem = (ZSubSystem) result;
                                // add its id to update DLADataTrace's scanDate
                                if (zSubSystem != null) {
                                    this.zSubSystemList.add(zSubSystem);
                                }
                                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                        "finish loadZSubSystemFromDB2");
                            } else {
                                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                                        "Unexpected return type from loadZSubSystemFromDB2: " + result);
                                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                        "IDML Processing Error: {}", msg);
                            }
                        }
                    } else {
                        String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                                "Unexpected return type from loadDB2Subsystem: " + loadResult);
                        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml", "IDML Processing Error: {}",
                                msg);
                    }
                }

                List<Object> db2StoredProcedureElementList = currentFileElementExtractor
                        .getElementListByClassName(AppDbDb2Db2StoredProcedure.class.getName());
                if (!db2StoredProcedureElementList.isEmpty()) {
                    parseAppDbDb2Db2StoredProcedure(db2StoredProcedureElementList, isMeta);
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadDB2StoredProcedureList, count: {}", db2StoredProcedureElementList.size());
                }

                List<Object> appDB2DBElementList = currentFileElementExtractor
                        .getElementListByClassName(AppDbDb2Db2Database.class.getName());
                if (!appDB2DBElementList.isEmpty()) {
                    parseAppDbDb2Db2Database(appDB2DBElementList, isMeta);
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadDB2DatabaseList from AppDbDb2Db2Database sections, count: {}",
                            appDB2DBElementList.size());
                }

                List<Object> appDB2TBElementList = currentFileElementExtractor
                        .getElementListByClassName(AppDbDb2Db2TableSpace.class.getName());
                if (!appDB2TBElementList.isEmpty()) {
                    parseAppDbDb2Db2TableSpace(appDB2TBElementList, isMeta);

                }

                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                        "finish loadDB2TableSpaceList from AppDbDb2Db2TableSpace sections, count: {}",
                        appDB2TBElementList.size());

                List<Object> db2BufferPoolElementList = currentFileElementExtractor
                        .getElementListByClassName(AppDbDb2Db2BufferPool.class.getName());
                if (!db2BufferPoolElementList.isEmpty()) {
                    parseAppDbDb2Db2BufferPool(db2BufferPoolElementList, isMeta);
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadDB2BufferPoolList from AppDbDb2Db2BufferPool sections, count: {}",
                            db2BufferPoolElementList.size());
                }

            }

            // ----------------------------------------------MQ---------------------------------------------------------
            // MQQueueSharingGroup
            SysZOSMQQueueSharingGroup sysZOSMQQSGElement = (SysZOSMQQueueSharingGroup) currentFileElementExtractor
                    .getElementByClassName(SysZOSMQQueueSharingGroup.class.getName());
            if (sysZOSMQQSGElement != null) {
                parseSysZOSMQQueueSharingGroup(sysZOSMQQSGElement, isMeta);

            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                    "Finished parseSysZOSMQQueueSharingGroup");

            SysZOSMQSubsystem sysZOSMQSubsystem = (SysZOSMQSubsystem) currentFileElementExtractor
                    .getElementByClassName(SysZOSMQSubsystem.class.getName());
            if (sysZOSMQSubsystem != null) {
                // Compatibility handling for the DeltaBooks
                if (sysZOSMQSubsystem.getLabel() == null) {
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            String.format("Skipped the sysZOSMQSubsystem %s because missing the label field.",
                                    sysZOSMQSubsystem.getId()));
                } else {
                    commonUtils.setValueAtIndex(ciIdentifierList, 6, mqDataSharingGroupName);
                    Object loadResult = mqElementLoader.loadMQSubsystem(sysZOSMQSubsystem, zosPrefixId, scanDate,
                            isMeta, ciIdentifierList);
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "finish loadMQSubsystem {}",
                            sysZOSMQSubsystem.getId());

                    if (loadResult instanceof MQSubsystemMeta) {
                        MQSubsystemMeta mqsys = (MQSubsystemMeta) loadResult;
                        if (mqsys == null) {
                            pipeCounter.addFailedDlaId(sysZOSMQSubsystem.getId());
                        } else {
                            pipeCounter.uniqueCount("MQSubsystem", sysZOSMQSubsystem.getId());
                            // create ZSubSystem from MQSubsystem
                            elementLoader.loadZSubsystemFromMQ(sysZOSMQSubsystem, zosPrefixId,
                                    scanDate, isMeta);
                            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                    "finish loadZSubsystemMetaFromMQ");
                        }

                    } else if (loadResult instanceof MQSubsystem) {
                        MQSubsystem mqsys = (MQSubsystem) loadResult;
                        if (mqsys == null) {
                            pipeCounter.addFailedDlaId(sysZOSMQSubsystem.getId());
                        } else {
                            mqSubSystemName = mqsys.getSubsystemName();
                            mqSubSystemId = mqsys.getDlaId();
                            pipeCounter.uniqueCount("MQSubsystem", sysZOSMQSubsystem.getId());
                            // create ZSubSystem from MQSubsystem
                            Object result = elementLoader.loadZSubsystemFromMQ(sysZOSMQSubsystem, zosPrefixId,
                                    scanDate, isMeta);

                            if (result instanceof ZSubSystem) {
                                ZSubSystem zSubSystem = (ZSubSystem) result;
                                // add its id to update DLADataTrace's scanDate
                                if (zSubSystem != null) {
                                    this.zSubSystemList.add(zSubSystem);
                                }
                                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                        "finish loadZSubsystemFromMQ");
                            } else {
                                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                                        "Unexpected return type from loadZSubsystemFromMQ: " + result);
                                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                        "IDML Processing Error: {}", msg);
                            }
                        }
                    } else {
                        String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                                "Unexpected return type from loadMQSubsystem: " + loadResult);
                        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml", "IDML Processing Error: {}",
                                msg);
                    }

                }

                // MQAliasQueue
                List<Object> appMQAQElementList = currentFileElementExtractor
                        .getElementListByClassName(AppMessagingMqMQAliasQueue.class.getName());
                if (!appMQAQElementList.isEmpty()) {
                    commonUtils.setValueAtIndex(ciIdentifierList, 6, mqDataSharingGroupName);
                    List<String> ids = mqElementLoader.loadMQAliasQueueList(appMQAQElementList, zosPrefixId, scanDate,
                            isMeta, ciIdentifierList, mqSubSystemName, mqSubSystemId);
                    pipeCounter.addFailedDlaIds(ids);
                    pipeCounter.count("MQAliasQueue", appMQAQElementList.size() - ids.size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadMQAliasQueueList, count: {}", appMQAQElementList.size());
                }

                // MQAuthInfo
                List<Object> appMQAIElementList = currentFileElementExtractor
                        .getElementListByClassName(AppMessagingMqMQAuthInfo.class.getName());
                if (!appMQAIElementList.isEmpty()) {
                    List<String> ids = mqElementLoader.loadMQAuthInfoList(appMQAIElementList, zosPrefixId, scanDate,
                            isMeta);
                    pipeCounter.addFailedDlaIds(ids);
                    pipeCounter.count("MQAuthInfo", appMQAIElementList.size() - ids.size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadMQAuthInfoList, count: {}", appMQAIElementList.size());
                }

                // MQBufferPool
                List<Object> appMQBPElementList = currentFileElementExtractor
                        .getElementListByClassName(AppMessagingMqMQBufferPool.class.getName());
                if (!appMQBPElementList.isEmpty()) {
                    List<String> ids = mqElementLoader.loadMQBufferPoolList(appMQBPElementList, zosPrefixId, scanDate,
                            isMeta);
                    pipeCounter.addFailedDlaIds(ids);
                    pipeCounter.count("MQBufferPool", appMQBPElementList.size() - ids.size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadMQBufferPoolList, count: {}", appMQBPElementList.size());
                }

                // MQClientConnectionChannel
                List<Object> appMQCCCElementList = currentFileElementExtractor
                        .getElementListByClassName(AppMessagingMqMQClientConnectionChannel.class.getName());
                if (!appMQCCCElementList.isEmpty()) {
                    List<String> ids = mqElementLoader.loadMQClientConnectionChannelList(appMQCCCElementList,
                            zosPrefixId, scanDate, isMeta);
                    pipeCounter.addFailedDlaIds(ids);
                    pipeCounter.count("MQClientConnectionChannel", appMQCCCElementList.size() - ids.size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadMQClientConnectionChannelList, count: {}", appMQCCCElementList.size());
                }

                // MQClusterReceiverChannel
                List<Object> appMQCRCElementList = currentFileElementExtractor
                        .getElementListByClassName(AppMessagingMqMQClusterReceiverChannel.class.getName());
                if (!appMQCRCElementList.isEmpty()) {
                    List<String> ids = mqElementLoader.loadMQClusterReceiverChannelList(appMQCRCElementList,
                            zosPrefixId, scanDate, isMeta);
                    pipeCounter.addFailedDlaIds(ids);
                    pipeCounter.count("MQClusterReceiverChannel", appMQCRCElementList.size() - ids.size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadMQClusterReceiverChannelList, count: {}", appMQCRCElementList.size());
                }

                // MQClusterSenderChannel
                List<Object> appMQCSCElementList = currentFileElementExtractor
                        .getElementListByClassName(AppMessagingMqMQClusterSenderChannel.class.getName());
                if (!appMQCSCElementList.isEmpty()) {
                    List<String> ids = mqElementLoader.loadMQClusterSenderChannelList(appMQCSCElementList, zosPrefixId,
                            scanDate, isMeta);
                    pipeCounter.addFailedDlaIds(ids);
                    pipeCounter.count("MQClusterSenderChannel", appMQCSCElementList.size() - ids.size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadMQClusterSenderChannelList, count: {}", appMQCSCElementList.size());
                }

                // MQLocalQueue
                List<Object> appMQLQElementList = currentFileElementExtractor
                        .getElementListByClassName(AppMessagingMqMQLocalQueue.class.getName());
                if (!appMQLQElementList.isEmpty()) {
                    commonUtils.setValueAtIndex(ciIdentifierList, 6, mqDataSharingGroupName);
                    List<String> ids = mqElementLoader.loadMQLocalQueueList(appMQLQElementList, zosPrefixId, scanDate,
                            isMeta, ciIdentifierList, mqSubSystemName, mqSubSystemId);
                    pipeCounter.addFailedDlaIds(ids);
                    pipeCounter.count("MQLocalQueue", appMQLQElementList.size() - ids.size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadMQLocalQueueList, count: {}", appMQLQElementList.size());
                }

                // MQModelQueue
                List<Object> appMQMQElementList = currentFileElementExtractor
                        .getElementListByClassName(AppMessagingMqMQModelQueue.class.getName());
                if (!appMQMQElementList.isEmpty()) {
                    commonUtils.setValueAtIndex(ciIdentifierList, 6, mqDataSharingGroupName);
                    List<String> ids = mqElementLoader.loadMQModelQueueList(appMQMQElementList, zosPrefixId, scanDate,
                            isMeta, ciIdentifierList, mqSubSystemName, mqSubSystemId);
                    pipeCounter.addFailedDlaIds(ids);
                    pipeCounter.count("MQModelQueue", appMQMQElementList.size() - ids.size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadMQModelQueueList, count: {}", appMQMQElementList.size());
                }

                // MQReceiverChannel
                List<Object> appMQRCElementList = currentFileElementExtractor
                        .getElementListByClassName(AppMessagingMqMQReceiverChannel.class.getName());
                if (!appMQRCElementList.isEmpty()) {
                    List<String> ids = mqElementLoader.loadMQReceiverChannelList(appMQRCElementList, zosPrefixId,
                            scanDate, isMeta);
                    pipeCounter.addFailedDlaIds(ids);
                    pipeCounter.count("MQReceiverChannel", appMQRCElementList.size() - ids.size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadMQReceiverChannelList, count: {}", appMQRCElementList.size());
                }

                // MQRemoteQueue
                List<Object> appMQRQElementList = currentFileElementExtractor
                        .getElementListByClassName(AppMessagingMqMQRemoteQueue.class.getName());
                if (!appMQRQElementList.isEmpty()) {
                    commonUtils.setValueAtIndex(ciIdentifierList, 6, mqDataSharingGroupName);
                    List<String> ids = mqElementLoader.loadMQRemoteQueueList(appMQRQElementList, zosPrefixId, scanDate,
                            isMeta, ciIdentifierList, mqSubSystemName, mqSubSystemId);
                    pipeCounter.addFailedDlaIds(ids);
                    pipeCounter.count("MQRemoteQueue", appMQRQElementList.size() - ids.size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadMQRemoteQueueList, count: {}", appMQRQElementList.size());
                }

                // MQSenderChannel
                List<Object> appMQSCElementList = currentFileElementExtractor
                        .getElementListByClassName(AppMessagingMqMQSenderChannel.class.getName());
                if (!appMQSCElementList.isEmpty()) {
                    List<String> ids = mqElementLoader.loadMQSenderChannelList(appMQSCElementList, zosPrefixId,
                            scanDate, isMeta);
                    pipeCounter.addFailedDlaIds(ids);
                    pipeCounter.count("MQSenderChannel", appMQSCElementList.size() - ids.size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadMQSenderChannelList, count: {}", appMQSCElementList.size());
                }

                // MQServerConnectionChannel
                List<Object> appMQSCCElementList = currentFileElementExtractor
                        .getElementListByClassName(AppMessagingMqMQServerConnectionChannel.class.getName());
                if (!appMQSCCElementList.isEmpty()) {
                    List<String> ids = mqElementLoader.loadMQServerConnectionChannelList(appMQSCCElementList,
                            zosPrefixId, scanDate, isMeta);
                    pipeCounter.addFailedDlaIds(ids);
                    pipeCounter.count("MQServerConnectionChannel", appMQSCCElementList.size() - ids.size());
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadMQServerConnectionChannelList, count: {}", appMQSCCElementList.size());
                }

            }

            // ----------------------------------------------IMS--------------------------------------------------------

            SysZOSIMSSysplexGroup sysZOSIMSSysplexGroup = (SysZOSIMSSysplexGroup) currentFileElementExtractor
                    .getElementByClassName(SysZOSIMSSysplexGroup.class.getName());
            if (sysZOSIMSSysplexGroup != null) {
                parseSysZOSIMSSysplexGroup(sysZOSIMSSysplexGroup, isMeta);
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "finish loadIMSSysplexGroup {}",
                        sysZOSIMSSysplexGroup.getId());
            }

            SysZOSIMSSubsystem sysZOSIMSSubsystem = (SysZOSIMSSubsystem) currentFileElementExtractor
                    .getElementByClassName(SysZOSIMSSubsystem.class.getName());
            if (sysZOSIMSSubsystem != null) {
                // Compatibility handling for the DeltaBooks
                if (sysZOSIMSSubsystem.getLabel() == null) {
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            String.format("Skipped the sysZOSIMSSubsystem %s because missing the label field.",
                                    sysZOSIMSSubsystem.getId()));
                } else {
                    commonUtils.setValueAtIndex(ciIdentifierList, 6, imsDataSharingGroupName);
                    Object loadResult = imsElementLoader.loadIMSSubsystem(sysZOSIMSSubsystem, zosPrefixId, scanDate,
                            isMeta, ciIdentifierList);
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "finish loadIMSSubsystem {}",
                            sysZOSIMSSubsystem.getId());

                    if (loadResult instanceof IMSSubsystemMeta) {
                        IMSSubsystemMeta iMSSubsystemMeta = (IMSSubsystemMeta) loadResult;
                        if (iMSSubsystemMeta == null) {
                            pipeCounter.addFailedDlaId(sysZOSIMSSubsystem.getId());
                        } else {
                            pipeCounter.uniqueCount("IMSSubsystemMeta", sysZOSIMSSubsystem.getId());
                            // create ZSubSystem from IMSSubsystem
                            elementLoader.loadZSubsystemFromIMS(sysZOSIMSSubsystem, zosPrefixId,
                                    scanDate, isMeta);
                            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                    "finish loadZSubsystemMetaFromIMS");
                        }

                    } else if (loadResult instanceof IMSSubsystem) {
                        IMSSubsystem iMSSubsystem = (IMSSubsystem) loadResult;
                        if (iMSSubsystem == null) {
                            pipeCounter.addFailedDlaId(sysZOSIMSSubsystem.getId());
                        } else {
                            imsSubSystemName = iMSSubsystem.getSubsystemName();
                            imsSubSystemId = iMSSubsystem.getDlaId();
                            pipeCounter.uniqueCount("IMSSubsystem", sysZOSIMSSubsystem.getId());
                            // create ZSubSystem from IMSSubsystem
                            Object result = elementLoader.loadZSubsystemFromIMS(sysZOSIMSSubsystem, zosPrefixId,
                                    scanDate, isMeta);

                            if (result instanceof ZSubSystem) {
                                ZSubSystem zSubSystem = (ZSubSystem) result;
                                // add its id to update DLADataTrace's scanDate
                                if (zSubSystem != null) {
                                    this.zSubSystemList.add(zSubSystem);
                                }
                                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                        "finish loadZSubsystemFromIMS");
                            } else {
                                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                                        "Unexpected return type from loadZSubsystemFromIMS: " + result);
                                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml",
                                        "IDML Processing Error: {}", msg);
                            }

                        }
                    } else {
                        String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                                "Unexpected return type from loadIMSSubsystem: " + loadResult);
                        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "populateDlaFromIdml", "IDML Processing Error: {}",
                                msg);
                    }

                }

                List<Object> sysZOSIMSDatabaseElementList = currentFileElementExtractor
                        .getElementListByClassName(SysZOSIMSDatabase.class.getName());

                // sysZOSIMSDatabaseElementList.removeIf(sysZOSIMSDatabaseElement ->
                // !(((SysZOSIMSDatabase)
                // sysZOSIMSDatabaseElement).getIMSDatabaseType()).equalsIgnoreCase("Database"));

                if (!sysZOSIMSDatabaseElementList.isEmpty()) {
                    parseSysZOSIMSDatabase(sysZOSIMSDatabaseElementList, isMeta);
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadIMSDatabaseList, count: {}", sysZOSIMSDatabaseElementList.size());
                }

                List<Object> sysZOSIMSProgramElementList = currentFileElementExtractor
                        .getElementListByClassName(SysZOSIMSProgram.class.getName());
                if (!sysZOSIMSProgramElementList.isEmpty()) {
                    parseSysZOSIMSProgram(sysZOSIMSProgramElementList, isMeta);
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadIMSProgramList, count: {}", sysZOSIMSProgramElementList.size());
                }

                List<Object> sysZOSIMSTransactionElementList = currentFileElementExtractor
                        .getElementListByClassName(SysZOSIMSTransaction.class.getName());
                if (!sysZOSIMSTransactionElementList.isEmpty()) {
                    parseSysZOSIMSTransaction(sysZOSIMSTransactionElementList, isMeta);
                    LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml",
                            "finish loadIMSTransactionList, count: {}", sysZOSIMSTransactionElementList.size());
                }

            }
        }

        filterIds.addAll(pipeCounter.getFailedDlaIds()); // need to filter all failed ids

        // -------------Process all the relationships at once time-------------

        currentFileRelationshipList = currentFileElementExtractor.getRelationshipElementList();
        loadRelationShipData(isMeta, currentFileRelationshipList, isDelta);
        return pipeCounter;

    }

    private void loadRelationShipData(boolean isMeta, List<Object> currentFileRelationshipList,
            boolean isDelta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "loadRelationShipData",
                "Running loadRelationShipData with params - isDelta: {}, isMeta: {}", isDelta, isMeta);
        relationshipLoader.loadRelationshipList(currentFileRelationshipList, filterIds,
                cpcPrefixId, zosPrefixId, scanDate, isMeta);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "loadRelationShipData", "Finished loadRelationshipList");

        relationshipServiceNowLoader.loadRelationshipServiceNowList(currentFileRelationshipList,
                filterIds, cpcPrefixId, zosPrefixId, scanDate, isMeta);
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "loadRelationShipData", "Finished loadRelationshipServiceNowList");
    }

    private void parseSysZOSIMSTransaction(List<Object> sysZOSIMSTransactionElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSIMSTransaction",
                "Running parseSysZOSIMSTransaction with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = imsElementLoader.loadIMSTransactionList(sysZOSIMSTransactionElementList,
                    zosPrefixId, scanDate, isMeta, ciIdentifierList, imsSubSystemName, imsSubSystemId);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("IMSTransactionMeta", sysZOSIMSTransactionElementList.size() - ids.size());
        } else {
            commonUtils.setValueAtIndex(ciIdentifierList, 6, imsDataSharingGroupName);
            List<String> ids = imsElementLoader.loadIMSTransactionList(sysZOSIMSTransactionElementList,
                    zosPrefixId, scanDate, isMeta, ciIdentifierList, imsSubSystemName, imsSubSystemId);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("IMSTransaction", sysZOSIMSTransactionElementList.size() - ids.size());
        }
    }

    private void parseSysZOSIMSProgram(List<Object> sysZOSIMSProgramElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSIMSProgram",
                "Running parseSysZOSIMSProgram with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = imsElementLoader.loadIMSProgramList(sysZOSIMSProgramElementList, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("IMSProgramMeta", sysZOSIMSProgramElementList.size() - ids.size());
        } else {
            List<String> ids = imsElementLoader.loadIMSProgramList(sysZOSIMSProgramElementList, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("IMSProgram", sysZOSIMSProgramElementList.size() - ids.size());
        }
    }

    private void parseSysZOSIMSDatabase(List<Object> sysZOSIMSDatabaseElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSIMSDatabase",
                "Running parseSysZOSIMSDatabase with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = imsElementLoader.loadIMSDatabaseList(sysZOSIMSDatabaseElementList,
                    zosPrefixId,
                    scanDate, isMeta, ciIdentifierList, imsSubSystemName, imsSubSystemId);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("IMSDatabaseMeta", sysZOSIMSDatabaseElementList.size() - ids.size());
        } else {
            commonUtils.setValueAtIndex(ciIdentifierList, 6, imsDataSharingGroupName);
            List<String> ids = imsElementLoader.loadIMSDatabaseList(sysZOSIMSDatabaseElementList,
                    zosPrefixId,
                    scanDate, isMeta, ciIdentifierList, imsSubSystemName, imsSubSystemId);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("IMSDatabase", sysZOSIMSDatabaseElementList.size() - ids.size());
        }
    }

    private void parseSysZOSIMSSysplexGroup(SysZOSIMSSysplexGroup sysZOSIMSSysplexGroup, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSIMSSysplexGroup",
                "Running parseSysZOSIMSSysplexGroup with params - isMeta: {}", isMeta);
        Object result = imsElementLoader.loadIMSSysplexGroup(sysZOSIMSSysplexGroup, "", scanDate, isMeta);

        if (result instanceof IMSSysplexGroupMeta) {
            IMSSysplexGroupMeta iMSSysplexGroupMeta = (IMSSysplexGroupMeta) result;
            if (iMSSysplexGroupMeta == null) {
                pipeCounter.addFailedDlaId(sysZOSIMSSysplexGroup.getId());
            } else {
                pipeCounter.uniqueCount("IMSSysplexGroupMeta", sysZOSIMSSysplexGroup.getId());
            }
        } else if (result instanceof IMSSysplexGroup) {
            IMSSysplexGroup iMSSysplexGroup = (IMSSysplexGroup) result;
            if (iMSSysplexGroup == null) {
                pipeCounter.addFailedDlaId(sysZOSIMSSysplexGroup.getId());
            } else {
                imsDataSharingGroupName = iMSSysplexGroup.getName();
                pipeCounter.uniqueCount("IMSSysplexGroup", sysZOSIMSSysplexGroup.getId());
            }
        } else {
            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                    "Unexpected return type from loadIMSSysplexGroup: " + result);
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSIMSSysplexGroup", "IDML Processing Error: {}", msg);
        }
    }

    private void parseSysZOSMQQueueSharingGroup(SysZOSMQQueueSharingGroup sysZOSMQQSGElement, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSMQQueueSharingGroup",
                "Running parseSysZOSMQQueueSharingGroup with params - isMeta: {}", isMeta);
        Object result = mqElementLoader.loadMQQueueSharingGroup(sysZOSMQQSGElement, "", scanDate, isMeta);

        if (result instanceof MQQueueSharingGroupMeta) {
            MQQueueSharingGroupMeta mqqsg = (MQQueueSharingGroupMeta) result;
            if (mqqsg == null) {
                pipeCounter.addFailedDlaId(sysZOSMQQSGElement.getId());
            } else {
                pipeCounter.uniqueCount("MQQueueSharingGroup", sysZOSMQQSGElement.getId());
            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "populateDlaFromIdml", "finish loadMQQueueSharingGroup: {}",
                    sysZOSMQQSGElement.getId());

        } else if (result instanceof MQQueueSharingGroup) {
            MQQueueSharingGroup mqqsg = (MQQueueSharingGroup) result;
            if (mqqsg == null) {
                pipeCounter.addFailedDlaId(sysZOSMQQSGElement.getId());
            } else {
                mqDataSharingGroupName = mqqsg.getName();
                pipeCounter.uniqueCount("MQQueueSharingGroup", sysZOSMQQSGElement.getId());
            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSMQQueueSharingGroup",
                    "finish loadMQQueueSharingGroup: {}", sysZOSMQQSGElement.getId());
        } else {
            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                    "Unexpected return type from loadMQQueueSharingGroup: " + result);
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSMQQueueSharingGroup", "IDML Processing Error: {}",
                    msg);
        }
    }

    private void parseAppDbDb2Db2BufferPool(List<Object> db2BufferPoolElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseAppDbDb2Db2BufferPool",
                "Running parseAppDbDb2Db2BufferPool with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = db2ElementLoader.loadDB2BufferPoolList(db2BufferPoolElementList, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("DB2BufferPoolMeta", db2BufferPoolElementList.size() - ids.size());
        } else {
            List<String> ids = db2ElementLoader.loadDB2BufferPoolList(db2BufferPoolElementList, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("DB2BufferPool", db2BufferPoolElementList.size() - ids.size());
        }
    }

    private void parseAppDbDb2Db2TableSpace(List<Object> appDB2TBElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseAppDbDb2Db2TableSpace",
                "Running parseAppDbDb2Db2TableSpace with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = db2ElementLoader.loadDB2TableSpaceList(appDB2TBElementList, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("DB2TableSpaceMeta", appDB2TBElementList.size() - ids.size());
        } else {
            List<String> ids = db2ElementLoader.loadDB2TableSpaceList(appDB2TBElementList, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("DB2TableSpace", appDB2TBElementList.size() - ids.size());
        }
    }

    private void parseAppDbDb2Db2Database(List<Object> appDB2DBElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseAppDbDb2Db2Database",
                "Running parseAppDbDb2Db2Database with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = db2ElementLoader.loadDB2DatabaseList(appDB2DBElementList, zosPrefixId,
                    scanDate, isMeta, ciIdentifierList, db2SubSystemName, db2SubSystemId);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("DB2DatabaseMeta", appDB2DBElementList.size() - ids.size());
        } else {
            commonUtils.setValueAtIndex(ciIdentifierList, 6, db2DataSharingGroupName);
            List<String> ids = db2ElementLoader.loadDB2DatabaseList(appDB2DBElementList, zosPrefixId,
                    scanDate, isMeta, ciIdentifierList, db2SubSystemName, db2SubSystemId);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("DB2Database", appDB2DBElementList.size() - ids.size());
        }

    }

    private void parseSysZOSDB2DataSharingGroup(SysZOSDB2DataSharingGroup sysZOSDB2DataSharingGroup, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSDB2DataSharingGroup",
                "Running parseSysZOSDB2DataSharingGroup with params - isMeta: {}", isMeta);
        Object result = db2ElementLoader.loadDB2DataSharingGroup(sysZOSDB2DataSharingGroup, "",
                scanDate, isMeta);

        if (result instanceof DB2DataSharingGroupMeta) {
            DB2DataSharingGroupMeta db2DataSharingGroupMeta = (DB2DataSharingGroupMeta) result;
            if (db2DataSharingGroupMeta == null) {
                pipeCounter.addFailedDlaId(sysZOSDB2DataSharingGroup.getId());
            } else {
                pipeCounter.uniqueCount("DB2DataSharingGroupMeta", sysZOSDB2DataSharingGroup.getId());
            }
        } else if (result instanceof DB2DataSharingGroup) {
            DB2DataSharingGroup db2DataSharingGroup = (DB2DataSharingGroup) result;
            if (db2DataSharingGroup == null) {
                pipeCounter.addFailedDlaId(sysZOSDB2DataSharingGroup.getId());
            } else {
                db2DataSharingGroupName = db2DataSharingGroup.getName();
                pipeCounter.uniqueCount("DB2DataSharingGroup", sysZOSDB2DataSharingGroup.getId());
            }
        } else {
            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                    "Unexpected return type from loadDB2DataSharingGroup: " + result);
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSDB2DataSharingGroup", "IDML Processing Error: {}",
                    msg);
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSDB2DataSharingGroup",
                "finish loadDB2DataSharingGroup: {}", sysZOSDB2DataSharingGroup.getId());
    }

    private void parseCICS_SIT_Overrides(String dlaId, String name, String content, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseCICS_SIT_Overrides",
                "Running parseCICS_SIT_Overrides with params - isMeta: {}", isMeta);
        Object result = zReportFileLoader.loadCICSSitOverrides(dlaId, name,
                content, zosPrefixId, scanDate, isMeta);

        if (result instanceof CICSSystemInitTableOverrideMeta) {
            CICSSystemInitTableOverrideMeta cicsSitO = (CICSSystemInitTableOverrideMeta) result;
            if (cicsSitO == null) {
                pipeCounter.addFailedDlaId(dlaId);
            } else {
                pipeCounter.uniqueCount("CICS_SIT_Overrides", dlaId);
            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseCICS_SIT_Overrides",
                    "finish loadCICSSitOverrides ZReportFile: {}", dlaId);

        } else if (result instanceof CICSSystemInitTableOverride) {
            CICSSystemInitTableOverride cicsSitO = (CICSSystemInitTableOverride) result;
            if (cicsSitO == null) {
                pipeCounter.addFailedDlaId(dlaId);
            } else {
                pipeCounter.uniqueCount("CICS_SIT_Overrides", dlaId);
            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseCICS_SIT_Overrides",
                    "finish loadCICSSitOverrides ZReportFile: {}", dlaId);
        } else {
            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                    "Unexpected return type from loadCICSSitOverrides: " + result);
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseCICS_SIT_Overrides", "IDML Processing Error: {}", msg);
        }
    }

    private void parseCICS_SIT(String dlaId, String name, String content, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseCICS_SIT",
                "Running parseCICS_SIT with params - isMeta: {}", isMeta);
        Object result = zReportFileLoader.loadCICSSit(dlaId, name, content, zosPrefixId,
                scanDate, isMeta);
        // CICSSystemInitTable cicsSit = zReportFileLoader.loadCICSSit(dlaId, name,
        // content, zosPrefixId,
        // scanDate);

        if (result instanceof CICSSystemInitTableMeta) {
            CICSSystemInitTableMeta cicsSit = (CICSSystemInitTableMeta) result;
            if (cicsSit == null) {
                pipeCounter.addFailedDlaId(dlaId);
            } else {
                pipeCounter.uniqueCount("CICS_SIT", dlaId);
            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseCICS_SIT", "finish loadCICSSit from ZReportFile: {}",
                    dlaId);

        } else if (result instanceof CICSSystemInitTable) {
            CICSSystemInitTable cicsSit = (CICSSystemInitTable) result;
            if (cicsSit == null) {
                pipeCounter.addFailedDlaId(dlaId);
            } else {
                pipeCounter.uniqueCount("CICS_SIT", dlaId);
            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseCICS_SIT", "finish loadCICSSit from ZReportFile: {}",
                    dlaId);
        } else {
            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                    "Unexpected return type from loadCICSSit: " + result);
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseCICS_SIT", "IDML Processing Error: {}", msg);
        }
    }

    private void parseSysZOSCICSFile(List<Object> CICSFileElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSCICSFile",
                "Running parseSysZOSCICSFile with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = layer4CICSLoader.loadCICSFileList(CICSFileElementList, zosPrefixId, scanDate,
                    isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("CICSFileMeta", CICSFileElementList.size() - ids.size());
        } else {
            List<String> ids = layer4CICSLoader.loadCICSFileList(CICSFileElementList, zosPrefixId, scanDate,
                    isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("CICSFile", CICSFileElementList.size() - ids.size());
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSCICSFile",
                "finish loadCICSFileList from CICSFile sections, count: {}", CICSFileElementList.size());
    }

    private void parseSysZOSCICSTransaction(List<Object> CICSTransactionElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSCICSTransaction",
                "Running parseSysZOSCICSTransaction with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = layer4CICSLoader.loadCICSTransactionList(CICSTransactionElementList,
                    zosPrefixId,
                    scanDate, isMeta, ciIdentifierList, cicsSubSystemName, cicsSubSystemId);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("CICSTransaction", CICSTransactionElementList.size() - ids.size());
        } else {
            List<String> ids = layer4CICSLoader.loadCICSTransactionList(CICSTransactionElementList,
                    zosPrefixId,
                    scanDate, isMeta, ciIdentifierList, cicsSubSystemName, cicsSubSystemId);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("CICSTransaction", CICSTransactionElementList.size() - ids.size());
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSCICSFile",
                "finish loadCICSTransactionList from CICSTransaction sections, count: {}",
                CICSTransactionElementList.size());
    }

    private void parseSysZOSCICSProgram(List<Object> CICSProgramElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSCICSProgram",
                "Running parseSysZOSCICSProgram with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = layer4CICSLoader.loadCICSProgramList(CICSProgramElementList, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("CICSProgramMeta", CICSProgramElementList.size() - ids.size());
        } else {
            List<String> ids = layer4CICSLoader.loadCICSProgramList(CICSProgramElementList, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("CICSProgram", CICSProgramElementList.size() - ids.size());
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSCICSFile",
                "finish loadCCICSProgramList from CICSProgram sections, count: {}", CICSProgramElementList.size());
    }

    private void parseSysZOSIMSSubsystem(List<Object> imsSubsystemElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSIMSSubsystem",
                "Running parseSysZOSIMSSubsystem with params - isMeta: {}", isMeta);
        for (Object imsSubsystemElement : imsSubsystemElements) {
            SysZOSIMSSubsystem sysZOSIMSSubsystem = (SysZOSIMSSubsystem) imsSubsystemElement;
            // Compatibility handling for the DeltaBooks
            if (sysZOSIMSSubsystem.getLabel() == null) {
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSIMSSubsystem",
                        String.format("Skipped the sysZOSIMSSubsystem %s because missing the label field.",
                                sysZOSIMSSubsystem.getId()));
                continue;
            }
            commonUtils.setValueAtIndex(ciIdentifierList, 6, imsDataSharingGroupName);
            Object result = imsElementLoader.loadIMSSubsystem(sysZOSIMSSubsystem, zosPrefixId,
                    scanDate, isMeta, ciIdentifierList);

            if (result instanceof IMSSubsystemMeta) {
                IMSSubsystemMeta imsSubsystem = (IMSSubsystemMeta) result;
                if (imsSubsystem != null) {
                    pipeCounter.uniqueCount("IMSSubsystemMeta", sysZOSIMSSubsystem.getId());
                } else {
                    pipeCounter.addFailedDlaId(sysZOSIMSSubsystem.getId());
                }
            } else if (result instanceof IMSSubsystem) {
                IMSSubsystem imsSubsystem = (IMSSubsystem) result;
                if (imsSubsystem != null) {
                    imsSubSystemName = imsSubsystem.getSubsystemName();
                    imsSubSystemId = imsSubsystem.getDlaId();
                    pipeCounter.uniqueCount("IMSSubsystem", sysZOSIMSSubsystem.getId());
                } else {
                    pipeCounter.addFailedDlaId(sysZOSIMSSubsystem.getId());
                }
            } else {
                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                        "Unexpected return type from loadIMSSubsystem: " + result);
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSIMSSubsystem", "IDML Processing Error: {}",
                        msg);
            }

        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSIMSSubsystem",
                "finish loadIMSSubsystemList in ZOSTASK, count: {}", imsSubsystemElements.size());
    }

    private void parseSysZOSIMSSysplexGroup(List<Object> imsSysplexGroupElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSIMSSysplexGroup",
                "Running parseSysZOSIMSSysplexGroup with params - isMeta: {}", isMeta);
        for (Object imsSysplexGroupElement : imsSysplexGroupElements) {
            SysZOSIMSSysplexGroup sysZOSIMSSysplexGroup = (SysZOSIMSSysplexGroup) imsSysplexGroupElement;
            Object result = imsElementLoader.loadIMSSysplexGroup(sysZOSIMSSysplexGroup, "",
                    scanDate, isMeta);

            if (result instanceof IMSSysplexGroupMeta) {
                IMSSysplexGroupMeta imsSysplexGroup = (IMSSysplexGroupMeta) result;
                if (imsSysplexGroup != null) {
                    pipeCounter.uniqueCount("IMSSysplexGroupMeta", sysZOSIMSSysplexGroup.getId());
                } else {
                    pipeCounter.addFailedDlaId(sysZOSIMSSysplexGroup.getId());
                }
            } else if (result instanceof IMSSysplexGroup) {
                IMSSysplexGroup imsSysplexGroup = (IMSSysplexGroup) result;
                if (imsSysplexGroup != null) {
                    imsDataSharingGroupName = imsSysplexGroup.getName();
                    pipeCounter.uniqueCount("IMSSysplexGroup", sysZOSIMSSysplexGroup.getId());
                } else {
                    pipeCounter.addFailedDlaId(sysZOSIMSSysplexGroup.getId());
                }
            } else {
                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                        "Unexpected return type from loadIMSSysplexGroup: " + result);
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSIMSSysplexGroup", "IDML Processing Error: {}",
                        msg);
            }

        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSIMSSysplexGroup",
                "finish loadIMSSysplexGroupList in ZOSTASK, count: {}", imsSysplexGroupElements.size());
    }

    private void parseSysZOSMQSubsystem(List<Object> mqSubsystemElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSMQSubsystem",
                "Running parseSysZOSMQSubsystem with params - isMeta: {}", isMeta);
        for (Object mqSubsystemElement : mqSubsystemElements) {
            SysZOSMQSubsystem sysZOSMQSubsystem = (SysZOSMQSubsystem) mqSubsystemElement;
            // Compatibility handling for the DeltaBooks
            if (sysZOSMQSubsystem.getLabel() == null) {
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSMQSubsystem",
                        String.format("Skipped the sysZOSMQSubsystem %s because missing the label field.",
                                sysZOSMQSubsystem.getId()));
                continue;
            }
            commonUtils.setValueAtIndex(ciIdentifierList, 6, mqDataSharingGroupName);
            Object result = mqElementLoader.loadMQSubsystem(sysZOSMQSubsystem, zosPrefixId, scanDate, isMeta,
                    ciIdentifierList);

            if (result instanceof MQSubsystemMeta) {
                MQSubsystemMeta cfLparMeta = (MQSubsystemMeta) result;

                if (cfLparMeta != null) {
                    pipeCounter.uniqueCount("MQSubsystem", sysZOSMQSubsystem.getId());
                } else {
                    pipeCounter.addFailedDlaId(sysZOSMQSubsystem.getId());
                }
            } else if (result instanceof MQSubsystem) {
                MQSubsystem mqSubsystem = (MQSubsystem) result;
                if (mqSubsystem != null) {
                    mqSubSystemName = mqSubsystem.getSubsystemName();
                    mqSubSystemId = mqSubsystem.getDlaId();
                    pipeCounter.uniqueCount("MQSubsystem", sysZOSMQSubsystem.getId());
                } else {
                    pipeCounter.addFailedDlaId(sysZOSMQSubsystem.getId());
                }
            } else {
                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                        "Unexpected return type from loadMQSubsystem: " + result);
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSMQSubsystem", "IDML Processing Error: {}", msg);
            }

        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSMQSubsystem",
                "finish loadMQSubsystemList in ZOSTASK, count: {}", mqSubsystemElements.size());
    }

    private void parseSysZOSMQQueueSharingGroup(List<Object> mqQueueSharingGroupElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSMQQueueSharingGroup",
                "Running parseSysZOSMQQueueSharingGroup with params - isMeta: {}", isMeta);
        for (Object mqQueueSharingGroupElement : mqQueueSharingGroupElements) {
            SysZOSMQQueueSharingGroup sysZOSMQQueueSharingGroup = (SysZOSMQQueueSharingGroup) mqQueueSharingGroupElement;
            Object result = mqElementLoader
                    .loadMQQueueSharingGroup(sysZOSMQQueueSharingGroup, "", scanDate, isMeta);

            if (result instanceof MQQueueSharingGroupMeta) {
                MQQueueSharingGroupMeta mqQueueSharingGroup = (MQQueueSharingGroupMeta) result;

                if (mqQueueSharingGroup != null) {
                    pipeCounter.uniqueCount("MQQueueSharingGroup", sysZOSMQQueueSharingGroup.getId());
                } else {
                    pipeCounter.addFailedDlaId(sysZOSMQQueueSharingGroup.getId());
                }
            } else if (result instanceof MQQueueSharingGroup) {
                MQQueueSharingGroup mqQueueSharingGroup = (MQQueueSharingGroup) result;
                if (mqQueueSharingGroup != null) {
                    mqDataSharingGroupName = mqQueueSharingGroup.getName();
                    pipeCounter.uniqueCount("MQQueueSharingGroup", sysZOSMQQueueSharingGroup.getId());
                } else {
                    pipeCounter.addFailedDlaId(sysZOSMQQueueSharingGroup.getId());
                }
            } else {
                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                        "Unexpected return type from loadZSeriesComputer: " + result);
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSMQQueueSharingGroup",
                        "IDML Processing Error: {}", msg);
            }

        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSMQQueueSharingGroup",
                "finish loadMQQueueSharingGroupList in ZOSTASK, count: {}", mqQueueSharingGroupElements.size());
    }

    private void parseSysZOSDB2Subsystem(List<Object> db2SubsystemElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSDB2Subsystem",
                "Running parseSysZOSDB2Subsystem with params - isMeta: {}", isMeta);
        for (Object db2SubsystemElement : db2SubsystemElements) {
            SysZOSDB2Subsystem sysZOSDB2Subsystem = (SysZOSDB2Subsystem) db2SubsystemElement;
            // Compatibility handling for the DeltaBooks
            if (sysZOSDB2Subsystem.getLabel() == null) {
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSDB2Subsystem",
                        String.format("Skipped the sysZOSDB2Subsystem %s because missing the label field.",
                                sysZOSDB2Subsystem.getId()));
                continue;
            }
            commonUtils.setValueAtIndex(ciIdentifierList, 6, db2DataSharingGroupName);
            Object result = db2ElementLoader.loadDB2Subsystem(sysZOSDB2Subsystem, zosPrefixId,
                    scanDate, isMeta, ciIdentifierList);

            if (result instanceof DB2SubsystemMeta) {
                DB2SubsystemMeta db2Subsystem = (DB2SubsystemMeta) result;
                if (db2Subsystem != null) {
                    pipeCounter.uniqueCount("DB2SubsystemMeta", sysZOSDB2Subsystem.getId());
                } else {
                    pipeCounter.addFailedDlaId(sysZOSDB2Subsystem.getId());
                }
            } else if (result instanceof DB2Subsystem) {
                DB2Subsystem db2Subsystem = (DB2Subsystem) result;
                if (db2Subsystem != null) {
                    db2SubSystemName = db2Subsystem.getSubSystemName();
                    db2SubSystemId = db2Subsystem.getDlaId();
                    pipeCounter.uniqueCount("DB2Subsystem", sysZOSDB2Subsystem.getId());
                } else {
                    pipeCounter.addFailedDlaId(sysZOSDB2Subsystem.getId());
                }
            } else {
                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                        "Unexpected return type from loadDB2Subsystem: " + result);
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSDB2Subsystem", "IDML Processing Error: {}",
                        msg);
            }

        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSDB2Subsystem",
                "finish loadDB2SubsystemList in ZOSTASK, count: {}", db2SubsystemElements.size());
    }

    private void parseAppDbDb2Db2StoredProcedure(List<Object> db2StoredProcedureElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseAppDbDb2Db2StoredProcedure",
                "Running parseAppDbDb2Db2StoredProcedure with params - isMeta: {}", isMeta);
        if (isMeta) {
            HashMap<String, List<String>> res = db2ElementLoader
                    .loadDB2StoredProcedureList(db2StoredProcedureElementList, zosPrefixId, scanDate, isMeta,
                            ciIdentifierList, db2SubSystemName, db2SubSystemId);
            pipeCounter.addFailedDlaIds(res.get("failedIds"));
            pipeCounter.uniqueCount("DB2StoredProcedureMeta", res.get("successIds"));
        } else {
            commonUtils.setValueAtIndex(ciIdentifierList, 6, db2DataSharingGroupName);
            HashMap<String, List<String>> res = db2ElementLoader
                    .loadDB2StoredProcedureList(db2StoredProcedureElementList, zosPrefixId, scanDate, isMeta,
                            ciIdentifierList, db2SubSystemName, db2SubSystemId);
            pipeCounter.addFailedDlaIds(res.get("failedIds"));
            pipeCounter.uniqueCount("DB2StoredProcedure", res.get("successIds"));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseAppDbDb2Db2StoredProcedure",
                "finish loadDB2StoredProcedureList in ZOSTASK, count: {}", db2StoredProcedureElementList.size());
    }

    private void parseSysZOSDB2DataSharingGroup(Object db2DataSharingGroupElement, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSDB2DataSharingGroup",
                "Running parseSysZOSDB2DataSharingGroup with params - isMeta: {}", isMeta);
        SysZOSDB2DataSharingGroup sysZOSDB2DataSharingGroup = (SysZOSDB2DataSharingGroup) db2DataSharingGroupElement;
        Object result = db2ElementLoader
                .loadDB2DataSharingGroup(sysZOSDB2DataSharingGroup, "", scanDate, isMeta);

        if (result instanceof DB2DataSharingGroupMeta) {
            DB2DataSharingGroupMeta db2DataSharingGroup = (DB2DataSharingGroupMeta) result;
            if (db2DataSharingGroup != null) {
                pipeCounter.uniqueCount("DB2DataSharingMetaGroup", sysZOSDB2DataSharingGroup.getId());
            } else {
                pipeCounter.addFailedDlaId(sysZOSDB2DataSharingGroup.getId());
            }
        } else if (result instanceof DB2DataSharingGroup) {

            DB2DataSharingGroup db2DataSharingGroup = (DB2DataSharingGroup) result;
            if (db2DataSharingGroup != null) {
                db2DataSharingGroupName = db2DataSharingGroup.getName();
                pipeCounter.uniqueCount("DB2DataSharingGroup", sysZOSDB2DataSharingGroup.getId());
            } else {
                pipeCounter.addFailedDlaId(sysZOSDB2DataSharingGroup.getId());
            }
        }
    }

    private void parseCicsSit(String dlaId, String name, String content, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseCicsSit",
                "Running parseCicsSit with params - isMeta: {}", isMeta);
        Object result = zReportFileLoader.loadCICSSit(dlaId, name, content, zosPrefixId,
                scanDate, isMeta);

        if (result instanceof CICSSystemInitTableMeta) {
            CICSSystemInitTableMeta cicsSit = (CICSSystemInitTableMeta) result;
            if (cicsSit == null) {
                pipeCounter.addFailedDlaId(dlaId);
            } else {
                pipeCounter.uniqueCount("CICS_SIT", dlaId);
            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseCicsSit",
                    String.format("finish loadCICSSit from ZReportFile %s in ZOSTASK", dlaId));

        } else if (result instanceof CICSSystemInitTable) {
            CICSSystemInitTable cicsSit = (CICSSystemInitTable) result;
            if (cicsSit == null) {
                pipeCounter.addFailedDlaId(dlaId);
            } else {
                pipeCounter.uniqueCount("CICS_SIT", dlaId);
            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseCicsSit",
                    String.format("finish loadCICSSit from ZReportFile %s in ZOSTASK", dlaId));
        } else {
            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                    "Unexpected return type from loadCICSSit: " + result);
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseCicsSit", "IDML Processing Error: {}", msg);
        }
    }

    private void parseCicsSitOverrides(String dlaId, String name, String content, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseCicsSitOverrides",
                "Running parseCicsSitOverrides with params - isMeta: {}", isMeta);
        Object result = zReportFileLoader.loadCICSSitOverrides(dlaId, name, content,
                zosPrefixId, scanDate, isMeta);

        if (result instanceof CICSSystemInitTableOverrideMeta) {
            CICSSystemInitTableOverrideMeta cicsSitO = (CICSSystemInitTableOverrideMeta) result;
            if (cicsSitO == null) {
                pipeCounter.addFailedDlaId(dlaId);
            } else {
                pipeCounter.uniqueCount("CICS_SIT_Overrides", dlaId);
            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseCicsSitOverrides",
                    String.format("finish loadCICSSitOverrides ZReportFile %s in ZOSTASK.", dlaId));

        } else if (result instanceof CICSSystemInitTableOverride) {
            CICSSystemInitTableOverride cicsSitO = (CICSSystemInitTableOverride) result;
            if (cicsSitO == null) {
                pipeCounter.addFailedDlaId(dlaId);
            } else {
                pipeCounter.uniqueCount("CICS_SIT_Overrides", dlaId);
            }
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseCicsSitOverrides",
                    String.format("finish loadCICSSitOverrides ZReportFile %s in ZOSTASK.", dlaId));

        } else {
            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                    "Unexpected return type from loadCICSSitOverrides: " + result);
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseCicsSitOverrides", "IDML Processing Error: {}", msg);
        }
    }

    private void parseSysZOSCICSRegion(List<Object> cicsRegionElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSCICSRegion",
                "Running parseSysZOSCICSRegion with params - isMeta: {}", isMeta);
        for (Object cicsRegionElement : cicsRegionElements) {

            if (cicsRegionElement instanceof SysZOSCICSRegion) {
                SysZOSCICSRegion sysZOSCICSRegion = (SysZOSCICSRegion) cicsRegionElement;
                // Compatibility handling for the DeltaBooks

                if (sysZOSCICSRegion.getLabel() == null) {
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSCICSRegion",
                            String.format("Skipped the sysZOSCICSRegion %s because missing the label field.",
                                    sysZOSCICSRegion.getId()));
                    continue;
                }
                Object result = elementLoader.loadCICSRegion(sysZOSCICSRegion, zosPrefixId, scanDate, isMeta,
                        ciIdentifierList);

                if (result instanceof CICSRegionMeta) {
                    CICSRegionMeta cicsRegionMeta = (CICSRegionMeta) result;
                    if (cicsRegionMeta != null) {
                        pipeCounter.uniqueCount("CICSRegionMeta", cicsRegionMeta.getDlaId());
                    } else {
                        pipeCounter.addFailedDlaId(sysZOSCICSRegion.getId());
                    }
                } else if (result instanceof CICSRegion) {
                    CICSRegion cICSRegion = (CICSRegion) result;
                    if (cICSRegion != null) {
                        pipeCounter.uniqueCount("CICSRegion", cICSRegion.getDlaId());
                    } else {
                        pipeCounter.addFailedDlaId(sysZOSCICSRegion.getId());
                    }
                } else {
                    String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                            "Unexpected return type from loadCICSRegion: " + result);
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSCICSRegion", "IDML Processing Error: {}",
                            msg);
                }
            } else {
                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                        "An item is not a valid SysZOSCICSRegion: " + cicsRegionElement
                                + ", Please check the IDML book with filePath: "
                                + currentFileElementExtractor.filePath);
                LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSCICSRegion", "IDML Processing Error: {}", msg);
                errorStore.addError(msg);
            }
        }
    }

    private void parseNetUdpPort(List<Object> udpPortElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseNetUdpPort",
                "Running parseNetUdpPort with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = zosTaskLoader.loadUdpPortList(udpPortElements, zosPrefixId, scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("UdpPort", udpPortElements.size() - ids.size());
        } else {
            List<String> ids = zosTaskLoader.loadUdpPortList(udpPortElements, zosPrefixId, scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("UdpPort", udpPortElements.size() - ids.size());
        }
    }

    private void parseNetTcpPort(List<Object> tcpPortElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseNetTcpPort",
                "Running parseNetTcpPort with params - isMeta: {}", isMeta);
        List<String> ids = zosTaskLoader.loadTcpPortList(tcpPortElements, zosPrefixId, scanDate, isMeta);
        pipeCounter.addFailedDlaIds(ids);
        pipeCounter.count("TcpPort", tcpPortElements.size() - ids.size());
    }

    private void parseAppProcessPool(List<Object> processPoolElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseAppProcessPool",
                "Running parseAppProcessPool with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = zosTaskLoader.loadProcessPoolList(processPoolElements, zosPrefixId, scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("ProcessPoolMeta", processPoolElements.size() - ids.size());
        } else {
            List<String> ids = zosTaskLoader.loadProcessPoolList(processPoolElements, zosPrefixId, scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("ProcessPool", processPoolElements.size() - ids.size());
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseAppProcessPool",
                "finish loadProcessPoolList in ZOSTASK, count: {}", processPoolElements.size());
    }

    private void parseSysZOSAddressSpace(List<Object> addressSpaceElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSAddressSpace",
                "Running parseSysZOSAddressSpace with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = zosTaskLoader.loadAddressSpaceList(addressSpaceElements, zosPrefixId, scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("AddressSpaceMeta", addressSpaceElements.size() - ids.size());
        } else {
            List<String> ids = zosTaskLoader.loadAddressSpaceList(addressSpaceElements, zosPrefixId, scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("AddressSpace", addressSpaceElements.size() - ids.size());
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSAddressSpace",
                "finish loadAddressSpaceList in ZOSTASK, count: {}", addressSpaceElements.size());
    }

    private void parseAppDbDb2Db2Table(List<Object> appDB2TableElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseAppDbDb2Db2Table",
                "Running parseAppDbDb2Db2Table with params - isMeta: {}", isMeta);
        if (isMeta) {
            List<String> ids = db2ElementLoader.loadDB2TableList(appDB2TableElementList, zosPrefixId, scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("Db2TableMeta", appDB2TableElementList.size() - ids.size());
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseAppDbDb2Db2Table",
                    "finish loadDB2Table from AppDbDb2Db2Table sections, count: {}", appDB2TableElementList.size());
        } else {
            List<String> ids = db2ElementLoader.loadDB2TableList(appDB2TableElementList, zosPrefixId, scanDate, isMeta);
            pipeCounter.addFailedDlaIds(ids);
            pipeCounter.count("Db2Table", appDB2TableElementList.size() - ids.size());
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseAppDbDb2Db2Table",
                    "finish loadDB2Table from AppDbDb2Db2Table sections, count: {}", appDB2TableElementList.size());
        }
    }

    private void parseSysZOSCICSPlex(List<Object> sysZOSCICSPlexElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSCICSPlex",
                "Running parseSysZOSCICSPlex with params - isMeta: {}", isMeta);
        for (Object sysZOSCICSPlexElement : sysZOSCICSPlexElementList) {
            if (sysZOSCICSPlexElement instanceof SysZOSCICSPlex) {

                SysZOSCICSPlex sysZOSCICSPlex = (SysZOSCICSPlex) sysZOSCICSPlexElement;
                Object result = elementLoader.loadCICSPlex(sysZOSCICSPlex, zosPrefixId, scanDate, isMeta);

                // Check the type of the result and handle accordingly
                if (result instanceof CICSPlexMeta) {
                    CICSPlexMeta cicsPlexMeta = (CICSPlexMeta) result;
                    handleResult(cicsPlexMeta, sysZOSCICSPlex, "CICSPlexMeta");
                } else if (result instanceof CICSPlex) {
                    CICSPlex cicsPlex = (CICSPlex) result;
                    handleResult(cicsPlex, sysZOSCICSPlex, "CICSPlex");
                } else {
                    String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                            "Unexpected return type from loadCICSPlex: " + result);
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSCICSPlex", "IDML Processing Error: {}",
                            msg);
                }
            } else {
                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                        "An item is not a valid sysZOSCICSPlexElement: " + sysZOSCICSPlexElement
                                + ", Please check the IDML book with filePath: "
                                + currentFileElementExtractor.filePath);
                LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSCICSPlex", "IDML Processing Error: {}", msg);
                errorStore.addError(msg);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSCICSPlex", "finish loadCICSPlex, count: {}",
                sysZOSCICSPlexElementList.size());
    }

    private void parseSysZOSDB2Conn(List<Object> sysZOSDB2ConnElementList, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSDB2Conn",
                "Running parseSysZOSDB2Conn with params - isMeta: {}", isMeta);
        for (Object sysZOSDB2ConnElement : sysZOSDB2ConnElementList) {
            SysZOSDB2Conn sysZOSDB2Conn = (SysZOSDB2Conn) sysZOSDB2ConnElement;
            Object result = layer4CICSLoader.loadCICSDB2Conn(sysZOSDB2Conn, zosPrefixId, scanDate, isMeta);

            if (result instanceof CICSDB2ConnMeta) {
                CICSDB2ConnMeta cicsdb2Conn = (CICSDB2ConnMeta) result;
                if (cicsdb2Conn == null) {
                    pipeCounter.addFailedDlaId(sysZOSDB2Conn.getId());
                } else {
                    pipeCounter.count("CICSDB2ConnMeta", 1);
                    layer4CICSLoader.checkDb2Subsystem4Conn(sysZOSDB2Conn, zosPrefixId, scanDate, isMeta);
                    layer4CICSLoader.checkCicsRegion4Conn(sysZOSDB2Conn, zosPrefixId, scanDate, isMeta);
                    layer4CICSLoader.checkCICSProgram4Conn(sysZOSDB2Conn, zosPrefixId, scanDate, isMeta);
                }

            } else if (result instanceof CICSDB2Conn) {
                CICSDB2Conn cicsdb2Conn = (CICSDB2Conn) result;
                if (cicsdb2Conn == null) {
                    pipeCounter.addFailedDlaId(sysZOSDB2Conn.getId());
                } else {
                    pipeCounter.count("CICSDB2Conn", 1);
                    layer4CICSLoader.checkDb2Subsystem4Conn(sysZOSDB2Conn, zosPrefixId, scanDate, isMeta);
                    layer4CICSLoader.checkCicsRegion4Conn(sysZOSDB2Conn, zosPrefixId, scanDate, isMeta);
                    layer4CICSLoader.checkCICSProgram4Conn(sysZOSDB2Conn, zosPrefixId, scanDate, isMeta);
                }
            } else {
                String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                        "Unexpected return type from loadCICSDB2Conn: " + result);
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSDB2Conn", "IDML Processing Error: {}", msg);
            }
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSDB2Conn", "finish loadCICSDB2Conn, count: {}",
                sysZOSDB2ConnElementList.size());
    }

    private void parseNetBindAddress(List<Object> bindAddressElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseNetBindAddress",
                "Running parseNetBindAddress with params - isMeta: {}", isMeta);
        if (isMeta) {
            HashMap<String, List<String>> res = zosTaskLoader.loadBindAddressList(bindAddressElements, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(res.get("failedIds"));
            pipeCounter.uniqueCount("BindAddressMeta", res.get("successIds"));
        } else {
            HashMap<String, List<String>> res = zosTaskLoader.loadBindAddressList(bindAddressElements, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(res.get("failedIds"));
            pipeCounter.uniqueCount("BindAddress", res.get("successIds"));
        }

    }

    private void parseNetIpInterface(List<Object> ipInterfaceElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseNetIpInterface",
                "Running parseNetIpInterface with params - isMeta: {}", isMeta);
        if (isMeta) {
            HashMap<String, List<String>> res = zosTaskLoader.loadIpInterfaceList(ipInterfaceElements, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(res.get("failedIds"));
            pipeCounter.uniqueCount("IpInterfaceMeta", res.get("successIds"));
        } else {
            HashMap<String, List<String>> res = zosTaskLoader.loadIpInterfaceList(ipInterfaceElements, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(res.get("failedIds"));
            pipeCounter.uniqueCount("IpInterface", res.get("successIds"));
        }

    }

    private void parseNetIpAddress(List<Object> ipAddressElements, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseNetIpAddress",
                "Running parseNetIpAddress with params - isMeta: {}", isMeta);
        if (isMeta) {
            HashMap<String, List<String>> res = zosTaskLoader.loadIpAddressList(ipAddressElements, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(res.get("failedIds"));
            pipeCounter.uniqueCount("IpAddressMeta", res.get("successIds"));
        } else {
            HashMap<String, List<String>> res = zosTaskLoader.loadIpAddressList(ipAddressElements, zosPrefixId,
                    scanDate, isMeta);
            pipeCounter.addFailedDlaIds(res.get("failedIds"));
            pipeCounter.uniqueCount("IpAddress", res.get("successIds"));
        }
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseNetIpAddress", "finish loadIpAddressList, count: {}",
                ipAddressElements.size());
    }

    private void parseNetFqdn(NetFqdn netFqdn, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseNetFqdn",
                "Running parseNetFqdn with params - isMeta: {}", isMeta);
        Object result = zosTaskLoader.loadFqdn(netFqdn, zosPrefixId, scanDate, isMeta);

        if (result instanceof FqdnMeta) {
            FqdnMeta fqdn = (FqdnMeta) result;
            if (fqdn != null) {
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseNetFqdn", "finish loadFqdn, count: 1");
                pipeCounter.uniqueCount("FqdnMeta", netFqdn.getId());
            } else {
                pipeCounter.addFailedDlaId(netFqdn.getId());
            }
        } else if (result instanceof Fqdn) {
            Fqdn fqdn = (Fqdn) result;
            if (fqdn != null) {
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseNetFqdn", "finish loadFqdn, count: 1");
                pipeCounter.uniqueCount("FQDN", netFqdn.getId());
            } else {
                pipeCounter.addFailedDlaId(netFqdn.getId());
            }
        } else {
            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                    "Unexpected return type from loadFqdn: " + result);
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseNetFqdn", "IDML Processing Error: {}", msg);
        }
    }

    private void parseSysZOSSysplex(SysZOSSysplex sysZOSSysplex, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseSysZOSSysplex",
                "Running parseSysZOSSysplex with params - isMeta: {}", isMeta);
        Object result = elementLoader.loadSysplex(sysZOSSysplex, "", scanDate, isMeta);

        if (result instanceof SysplexMeta) {
            SysplexMeta sysplexMeta = (SysplexMeta) result;
            if (sysplexMeta != null) {
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSSysplex", "finish loadSysplex: {}",
                        sysZOSSysplex.getId());
                pipeCounter.uniqueCount("SysplexMeta", sysZOSSysplex.getId());
            } else {
                pipeCounter.addFailedDlaId(sysZOSSysplex.getId());
            }
        } else if (result instanceof Sysplex) {
            Sysplex sysplex = (Sysplex) result;
            if (sysplex != null) {
                commonUtils.setValueAtIndex(ciIdentifierList, 3, sysplex.getName());
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "parseSysZOSSysplex", "finish loadSysplex: {}",
                        sysZOSSysplex.getId());
                pipeCounter.uniqueCount("Sysplex", sysZOSSysplex.getId());
            } else {
                pipeCounter.addFailedDlaId(sysZOSSysplex.getId());
            }
        } else {
            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                    "Unexpected return type from loadSysplex: " + result);
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseSysZOSSysplex", "IDML Processing Error: {}", msg);
        }
    }

    private void parseCFComputerSystem(SysZOSZSeriesComputerSystem sysZOSZSeriesComputerSystem, boolean isMeta) {
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "parseCFComputerSystem",
                "Running parseCFComputerSystem with params - isMeta: {}", isMeta);
        // use the global cpcPrefixId to parse those CFComputerSystems
        Object result = elementLoader.loadCFComputerSystem(sysZOSZSeriesComputerSystem,
                cpcPrefixId, scanDate, isMeta);
        // Check the type of the result and handle accordingly
        if (result instanceof CFComputerSystemMeta) {
            CFComputerSystemMeta cFComputerSystemMeta = (CFComputerSystemMeta) result;
            if (cFComputerSystemMeta != null) {
                pipeCounter.count("CFComputerSystemMeta", 1);
            } else {
                pipeCounter.addFailedDlaId(sysZOSZSeriesComputerSystem.getId());
            }
        } else if (result instanceof CFComputerSystem) {
            CFComputerSystem cFComputerSystem = (CFComputerSystem) result;
            if (cFComputerSystem != null) {
                pipeCounter.count("CFComputerSystem", 1);
            } else {
                pipeCounter.addFailedDlaId(sysZOSZSeriesComputerSystem.getId());
            }
        } else {
            String msg = MessageFormat.format(MsgTemp.get(pipelineErrorCode), "Sansa",
                    "Unexpected return type from loadCFComputerSystem: " + result);
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "parseCFComputerSystem", "IDML Processing Error: {}", msg);
        }
    }

    private void handleResult(Object result, SysZOSCICSPlex sysZOSCICSPlex, String entityType) {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "handleResult", "Running handleResult");
        if (result == null) {
            pipeCounter.addFailedDlaId(sysZOSCICSPlex.getId());
        } else {
            pipeCounter.count(entityType, 1);
        }
    }

    private void init() {
        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "init", "Running init");
        repos = JPAManager.getJPAManager().getRepos();

        ZSeriesComputerRepository zSeriesComputerRepository = (ZSeriesComputerRepository) repos.get("zSeriesComputer");
        CFComputerSystemRepository cfComputerSystemRepository = (CFComputerSystemRepository) repos
                .get("cfComputerSystem");
        LPARRepository lparRepository = (LPARRepository) repos.get("lpar");
        CFLPARRepository cfLparRepository = (CFLPARRepository) repos.get("cfLpar");
        PRSMLPARRepository prsmlparRepository = (PRSMLPARRepository) repos.get("prsmLpar");
        ZOSRepository zOSRepository = (ZOSRepository) repos.get("zos");
        SysplexRepository sysplexRepository = (SysplexRepository) repos.get("sysplex");
        ZSubSystemRepository zSubSystemRepository = (ZSubSystemRepository) repos.get("zSubSystem");
        CICSRegionRepository cicsRegionRepository = (CICSRegionRepository) repos.get("cicsRegion");
        CICSSystemInitTableRepository cicsSystemInitTableRepository = (CICSSystemInitTableRepository) repos
                .get("cicsSystemInitTable");
        CICSSystemInitTableOverrideRepository cicsSystemInitTableOverrideRepository = (CICSSystemInitTableOverrideRepository) repos
                .get("cicsSystemInitTableOverride");
        CICSProgramRepository cicsProgramRepository = (CICSProgramRepository) repos.get("cicsProgram");
        CICSTransactionRepository cicsTransactionRepository = (CICSTransactionRepository) repos.get("cicsTransaction");
        CICSFileRepository cicsFileRepository = (CICSFileRepository) repos.get("cicsFile");
        DB2DataSharingGroupRepository db2dataSharingGroupRepository = (DB2DataSharingGroupRepository) repos
                .get("db2dataSharingGroup");
        DB2SubsystemRepository db2SubsystemRepository = (DB2SubsystemRepository) repos.get("db2Subsystem");
        DB2DatabaseRepository db2DatabaseRepository = (DB2DatabaseRepository) repos.get("db2Database");
        DB2TableSpaceRepository db2TableSpaceRepository = (DB2TableSpaceRepository) repos.get("db2TableSpace");
        DB2TableRepository db2TableRepository = (DB2TableRepository) repos.get("db2Table");
        DB2BufferPoolRepository db2BufferPoolRepository = (DB2BufferPoolRepository) repos.get("db2BufferPool");
        DB2StoredProcedureRepository db2StoredProcedureRepository = (DB2StoredProcedureRepository) repos
                .get("db2StoredProcedure");
        CICSDB2ConnRepository cicsdb2ConnRepository = (CICSDB2ConnRepository) repos.get("cicsdb2Conn");
        CICSPlexRepository cicsPlexRepository = (CICSPlexRepository) repos.get("cicsPlex");
        RelationshipRepository relationshipRepository = (RelationshipRepository) repos.get("relationship");
        AddressSpaceRepository addressSpaceRepository = (AddressSpaceRepository) repos.get("addressSpace");
        BindAddressRepository bindAddressRepository = (BindAddressRepository) repos.get("bindAddress");
        FqdnRepository fqdnRepository = (FqdnRepository) repos.get("fqdn");
        IpAddressRepository ipAddressRepository = (IpAddressRepository) repos.get("ipAddress");
        IpInterfaceRepository ipInterfaceRepository = (IpInterfaceRepository) repos.get("ipInterface");
        ProcessPoolRepository processPoolRepository = (ProcessPoolRepository) repos.get("processPool");
        TcpPortRepository tcpPortRepository = (TcpPortRepository) repos.get("tcpPort");
        UdpPortRepository udpPortRepository = (UdpPortRepository) repos.get("udpPort");
        IdmlOperationTimeRepository idmlOperationTimeRepository = (IdmlOperationTimeRepository) repos
                .get("idmlOperationTime");
        MQAliasQueueRepository mqAliasQueueRepository = (MQAliasQueueRepository) repos.get("mqAliasQueue");
        MQAuthInfoRepository mqAuthInfoRepository = (MQAuthInfoRepository) repos.get("mqAuthInfo");
        MQBufferPoolRepository mqBufferPoolRepository = (MQBufferPoolRepository) repos.get("mqBufferPool");
        MQClientConnectionChannelRepository mqClientConnectionChannelRepository = (MQClientConnectionChannelRepository) repos
                .get("mqClientConnectionChannel");
        MQClusterReceiverChannelRepository mqClusterReceiverChannelRepository = (MQClusterReceiverChannelRepository) repos
                .get("mqClusterReceiverChannel");
        MQClusterSenderChannelRepository mqClusterSenderChannelRepository = (MQClusterSenderChannelRepository) repos
                .get("mqClusterSenderChannel");
        MQLocalQueueRepository mqLocalQueueRepository = (MQLocalQueueRepository) repos.get("mqLocalQueue");
        MQModelQueueRepository mqModelQueueRepository = (MQModelQueueRepository) repos.get("mqModelQueue");
        MQReceiverChannelRepository mqReceiverChannelRepository = (MQReceiverChannelRepository) repos
                .get("mqReceiverChannel");
        MQRemoteQueueRepository mqRemoteQueueRepository = (MQRemoteQueueRepository) repos.get("mqRemoteQueue");
        MQSenderChannelRepository mqSenderChannelRepository = (MQSenderChannelRepository) repos.get("mqSenderChannel");
        MQServerConnectionChannelRepository mqServerConnectionChannelRepository = (MQServerConnectionChannelRepository) repos
                .get("mqServerConnectionChannel");
        MQSubsystemRepository mqSubsystemRepository = (MQSubsystemRepository) repos.get("mqSubsystem");
        IMSSubsystemRepository imsSubsystemRepository = (IMSSubsystemRepository) repos.get("imsSubsystem");
        IMSDatabaseRepository imsDatabaseRepository = (IMSDatabaseRepository) repos.get("imsDatabase");
        IMSProgramRepository imsProgramRepository = (IMSProgramRepository) repos.get("imsProgram");
        IMSTransactionRepository imsTransactionRepository = (IMSTransactionRepository) repos.get("imsTransaction");
        IMSSysplexGroupRepository imsSysplexGroupRepository = (IMSSysplexGroupRepository) repos.get("imsSysplexGroup");
        MQQueueSharingGroupRepository mqQueueSharingGroupRepository = (MQQueueSharingGroupRepository) repos
                .get("mqQueueSharingGroup");
        RelationshipServiceNowRepository relationshipServiceNowRepository = (RelationshipServiceNowRepository) repos
                .get("relationshipServiceNow");

        ZSeriesComputerMetaRepository zSeriesComputerMetaRepository = (ZSeriesComputerMetaRepository) repos
                .get("zSeriesComputerMeta");
        CFComputerSystemMetaRepository cfComputerSystemMetaRepository = (CFComputerSystemMetaRepository) repos
                .get("cfComputerSystemMeta");
        LPARMetaRepository lparMetaRepository = (LPARMetaRepository) repos.get("lparMeta");
        CFLPARMetaRepository cfLparMetaRepository = (CFLPARMetaRepository) repos.get("cfLparMeta");
        PRSMLPARMetaRepository prsmlparMetaRepository = (PRSMLPARMetaRepository) repos.get("prsmLparMeta");
        ZOSMetaRepository zOSMetaRepository = (ZOSMetaRepository) repos.get("zosMeta");
        SysplexMetaRepository sysplexMetaRepository = (SysplexMetaRepository) repos.get("sysplexMeta");
        ZSubSystemMetaRepository zSubSystemMetaRepository = (ZSubSystemMetaRepository) repos.get("zSubSystemMeta");
        CICSRegionMetaRepository cicsRegionMetaRepository = (CICSRegionMetaRepository) repos.get("cicsRegionMeta");
        CICSSystemInitTableMetaRepository cicsSystemInitTableMetaRepository = (CICSSystemInitTableMetaRepository) repos
                .get("cicsSystemInitTableMeta");
        CICSSystemInitTableOverrideMetaRepository cicsSystemInitTableOverrideMetaRepository = (CICSSystemInitTableOverrideMetaRepository) repos
                .get("cicsSystemInitTableOverrideMeta");
        CICSProgramMetaRepository cicsProgramMetaRepository = (CICSProgramMetaRepository) repos.get("cicsProgramMeta");
        CICSTransactionMetaRepository cicsTransactionMetaRepository = (CICSTransactionMetaRepository) repos
                .get("cicsTransactionMeta");
        CICSFileMetaRepository cicsFileMetaRepository = (CICSFileMetaRepository) repos.get("cicsFileMeta");
        DB2DataSharingGroupMetaRepository db2dataSharingGroupMetaRepository = (DB2DataSharingGroupMetaRepository) repos
                .get("db2dataSharingGroupMeta");
        DB2SubsystemMetaRepository db2SubsystemMetaRepository = (DB2SubsystemMetaRepository) repos
                .get("db2SubsystemMeta");
        DB2DatabaseMetaRepository db2DatabaseMetaRepository = (DB2DatabaseMetaRepository) repos.get("db2DatabaseMeta");
        DB2TableSpaceMetaRepository db2TableSpaceMetaRepository = (DB2TableSpaceMetaRepository) repos
                .get("db2TableSpaceMeta");
        DB2TableMetaRepository db2TableMetaRepository = (DB2TableMetaRepository) repos.get("db2TableMeta");
        DB2BufferPoolMetaRepository db2BufferPoolMetaRepository = (DB2BufferPoolMetaRepository) repos
                .get("db2BufferPoolMeta");
        DB2StoredProcedureMetaRepository db2StoredProcedureMetaRepository = (DB2StoredProcedureMetaRepository) repos
                .get("db2StoredProcedureMeta");
        CICSDB2ConnMetaRepository cicsdb2ConnMetaRepository = (CICSDB2ConnMetaRepository) repos.get("cicsdb2ConnMeta");
        CICSPlexMetaRepository cicsPlexMetaRepository = (CICSPlexMetaRepository) repos.get("cicsPlexMeta");
        RelationshipMetaRepository relationshipMetaRepository = (RelationshipMetaRepository) repos
                .get("relationshipMeta");
        AddressSpaceMetaRepository addressSpaceMetaRepository = (AddressSpaceMetaRepository) repos
                .get("addressSpaceMeta");
        BindAddressMetaRepository bindAddressMetaRepository = (BindAddressMetaRepository) repos.get("bindAddressMeta");
        FqdnMetaRepository fqdnMetaRepository = (FqdnMetaRepository) repos.get("fqdnMeta");
        IpAddressMetaRepository ipAddressMetaRepository = (IpAddressMetaRepository) repos.get("ipAddressMeta");
        IpInterfaceMetaRepository ipInterfaceMetaRepository = (IpInterfaceMetaRepository) repos.get("ipInterfaceMeta");
        ProcessPoolMetaRepository processPoolMetaRepository = (ProcessPoolMetaRepository) repos.get("processPoolMeta");
        TcpPortMetaRepository tcpPortMetaRepository = (TcpPortMetaRepository) repos.get("tcpPortMeta");
        UdpPortMetaRepository udpPortMetaRepository = (UdpPortMetaRepository) repos.get("udpPortMeta");
        MQAliasQueueMetaRepository mqAliasQueueMetaRepository = (MQAliasQueueMetaRepository) repos
                .get("mqAliasQueueMeta");
        MQAuthInfoMetaRepository mqAuthInfoMetaRepository = (MQAuthInfoMetaRepository) repos.get("mqAuthInfoMeta");
        MQBufferPoolMetaRepository mqBufferPoolMetaRepository = (MQBufferPoolMetaRepository) repos
                .get("mqBufferPoolMeta");
        MQClientConnectionChannelMetaRepository mqClientConnectionChannelMetaRepository = (MQClientConnectionChannelMetaRepository) repos
                .get("mqClientConnectionChannelMeta");
        MQClusterReceiverChannelMetaRepository mqClusterReceiverChannelMetaRepository = (MQClusterReceiverChannelMetaRepository) repos
                .get("mqClusterReceiverChannelMeta");
        MQClusterSenderChannelMetaRepository mqClusterSenderChannelMetaRepository = (MQClusterSenderChannelMetaRepository) repos
                .get("mqClusterSenderChannelMeta");
        MQLocalQueueMetaRepository mqLocalQueueMetaRepository = (MQLocalQueueMetaRepository) repos
                .get("mqLocalQueueMeta");
        MQModelQueueMetaRepository mqModelQueueMetaRepository = (MQModelQueueMetaRepository) repos
                .get("mqModelQueueMeta");
        MQReceiverChannelMetaRepository mqReceiverChannelMetaRepository = (MQReceiverChannelMetaRepository) repos
                .get("mqReceiverChannelMeta");
        MQRemoteQueueMetaRepository mqRemoteQueueMetaRepository = (MQRemoteQueueMetaRepository) repos
                .get("mqRemoteQueueMeta");
        MQSenderChannelMetaRepository mqSenderChannelMetaRepository = (MQSenderChannelMetaRepository) repos
                .get("mqSenderChannelMeta");
        MQServerConnectionChannelMetaRepository mqServerConnectionChannelMetaRepository = (MQServerConnectionChannelMetaRepository) repos
                .get("mqServerConnectionChannelMeta");
        MQSubsystemMetaRepository mqSubsystemMetaRepository = (MQSubsystemMetaRepository) repos.get("mqSubsystemMeta");
        IMSSubsystemMetaRepository imsSubsystemMetaRepository = (IMSSubsystemMetaRepository) repos
                .get("imsSubsystemMeta");
        IMSDatabaseMetaRepository imsDatabaseMetaRepository = (IMSDatabaseMetaRepository) repos.get("imsDatabaseMeta");
        IMSProgramMetaRepository imsProgramMetaRepository = (IMSProgramMetaRepository) repos.get("imsProgramMeta");
        IMSTransactionMetaRepository imsTransactionMetaRepository = (IMSTransactionMetaRepository) repos
                .get("imsTransactionMeta");
        IMSSysplexGroupMetaRepository imsSysplexGroupMetaRepository = (IMSSysplexGroupMetaRepository) repos
                .get("imsSysplexGroupMeta");
        MQQueueSharingGroupMetaRepository mqQueueSharingGroupMetaRepository = (MQQueueSharingGroupMetaRepository) repos
                .get("mqQueueSharingGroupMeta");
        RelationshipServiceNowMetaRepository relationshipServiceNowMetaRepository = (RelationshipServiceNowMetaRepository) repos
                .get("relationshipServiceNowMeta");

        this.elementLoader = new ElementLoader(zSeriesComputerRepository, cfComputerSystemRepository, lparRepository,
                cfLparRepository, prsmlparRepository, zOSRepository, sysplexRepository, zSubSystemRepository,
                cicsRegionRepository, cicsPlexRepository, relationshipRepository, zSeriesComputerMetaRepository,
                cfComputerSystemMetaRepository, lparMetaRepository, cfLparMetaRepository,
                prsmlparMetaRepository, zOSMetaRepository, sysplexMetaRepository, zSubSystemMetaRepository,
                cicsRegionMetaRepository, cicsPlexMetaRepository, relationshipMetaRepository);
        this.zReportFileLoader = new ZReportFileLoader(cicsSystemInitTableRepository,
                cicsSystemInitTableOverrideRepository, cicsProgramRepository,
                cicsTransactionRepository, cicsFileRepository, relationshipRepository,
                cicsSystemInitTableMetaRepository,
                cicsSystemInitTableOverrideMetaRepository, cicsProgramMetaRepository,
                cicsTransactionMetaRepository, cicsFileMetaRepository, relationshipMetaRepository);
        this.layer4CICSLoader = new Layer4CICSLoader(cicsProgramRepository, cicsTransactionRepository,
                cicsFileRepository, cicsdb2ConnRepository,
                cicsRegionRepository, db2SubsystemRepository, cicsProgramMetaRepository, cicsTransactionMetaRepository,
                cicsFileMetaRepository, cicsdb2ConnMetaRepository,
                cicsRegionMetaRepository, db2SubsystemMetaRepository);
        this.db2ElementLoader = new DB2ElementLoader(db2dataSharingGroupRepository, db2SubsystemRepository,
                db2DatabaseRepository, db2TableSpaceRepository, db2TableRepository, db2BufferPoolRepository,
                db2StoredProcedureRepository,
                db2dataSharingGroupMetaRepository, db2SubsystemMetaRepository,
                db2DatabaseMetaRepository, db2TableSpaceMetaRepository, db2TableMetaRepository,
                db2BufferPoolMetaRepository,
                db2StoredProcedureMetaRepository);
        this.relationshipLoader = new RelationshipLoader(relationshipRepository, relationshipMetaRepository);
        this.zosTaskLoader = new ZOSTaskLoader(addressSpaceRepository, bindAddressRepository, fqdnRepository,
                ipAddressRepository, ipInterfaceRepository, processPoolRepository, tcpPortRepository, udpPortRepository,
                addressSpaceMetaRepository, bindAddressMetaRepository, fqdnMetaRepository,
                ipAddressMetaRepository, ipInterfaceMetaRepository, processPoolMetaRepository, tcpPortMetaRepository,
                udpPortMetaRepository);
        this.operationTimeLoader = new OperationTimeLoader(idmlOperationTimeRepository);
        this.mqElementLoader = new MQElementLoader(mqSubsystemRepository, mqAliasQueueRepository, mqAuthInfoRepository,
                mqBufferPoolRepository,
                mqLocalQueueRepository, mqModelQueueRepository, mqRemoteQueueRepository,
                mqClientConnectionChannelRepository,
                mqClusterReceiverChannelRepository, mqClusterSenderChannelRepository, mqReceiverChannelRepository,
                mqSenderChannelRepository, mqServerConnectionChannelRepository, mqQueueSharingGroupRepository,
                mqSubsystemMetaRepository, mqAliasQueueMetaRepository, mqAuthInfoMetaRepository,
                mqBufferPoolMetaRepository, mqLocalQueueMetaRepository, mqModelQueueMetaRepository,
                mqRemoteQueueMetaRepository,
                mqClientConnectionChannelMetaRepository, mqClusterReceiverChannelMetaRepository,
                mqClusterSenderChannelMetaRepository, mqReceiverChannelMetaRepository,
                mqSenderChannelMetaRepository, mqServerConnectionChannelMetaRepository,
                mqQueueSharingGroupMetaRepository);
        this.imsElementLoader = new IMSElementLoader(imsSubsystemRepository, imsDatabaseRepository,
                imsProgramRepository, imsTransactionRepository, imsSysplexGroupRepository, imsSubsystemMetaRepository,
                imsDatabaseMetaRepository,
                imsProgramMetaRepository, imsTransactionMetaRepository, imsSysplexGroupMetaRepository);
        this.relationshipServiceNowLoader = new RelationshipServiceNowLoader(relationshipServiceNowRepository,
                relationshipServiceNowMetaRepository);
    }

    public String getCpcPrefixId() {
        return this.cpcPrefixId;
    }

    public String getZosPrefixId() {
        return this.zosPrefixId;
    }

    public String getSmfId() {
        return this.smfId;
    }

    public String getLparName() {
        return this.lparName;
    }

    public List<ZSubSystem> getZSubSystemList() {
        return this.zSubSystemList;
    }

}
