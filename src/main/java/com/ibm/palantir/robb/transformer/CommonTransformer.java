/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021, 2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.robb.transformer;

import com.ibm.palantir.catelyn.config.ProcessCompiler;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.transformer.AbstractTransformer;
import com.ibm.palantir.robb.util.ResolveName;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversalSource;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

public class CommonTransformer extends AbstractTransformer {

    private ArrayList<HashMap<String, Object>> transformList;
    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = ProcessCompiler.class.getSimpleName();
    private static final String REPO_NAME = "Robb";

    public CommonTransformer() {
        this.transformList = new ArrayList();
    }

    public void transformerADData(ResultSet resultSet, String tableName, String idName, ArrayList idList,
            GraphTraversalSource g) {
        String graphName = ResolveName.resolveVertexProperty(idName);
        try {
            while (resultSet.next()) {
                int id = resultSet.getInt(idName);
                if (!g.V().hasLabel(tableName).has(graphName, id).hasNext() && idList.contains(id)) {
                    addNewRecord(resultSet, idName);
                }
            }
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPO_NAME, CLASSNAME, "transformerADData", e.toString());
        }
    }

    private void addNewRecord(ResultSet resultSet, String idName) throws SQLException {
        ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
        HashMap<String, Object> resultValue = new HashMap<String, Object>();
        for (int i = 1; i < resultSetMetaData.getColumnCount() + 1; i++) {
            String key = ResolveName.resolveVertexProperty(resultSetMetaData.getColumnLabel(i));
            Object value = resultSet.getObject(resultSetMetaData.getColumnName(i));
            resultValue.put(key, value);
            if ((key.contains("name") || key.contains("Name")) && value != null) {
                resultValue.put("displayName", value);
            }
        }
        resultValue.put("id", resultSet.getInt(idName));
        this.transformList.add(resultValue);
    }

    public ArrayList<HashMap<String, Object>> getTransformList() {
        return this.transformList;
    }
}
