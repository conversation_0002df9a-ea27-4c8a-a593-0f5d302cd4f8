/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.robb.transformer;

import java.sql.ResultSet;
import java.util.ArrayList;

import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversal;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversalSource;
import org.apache.tinkerpop.gremlin.structure.Vertex;

import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.transformer.AbstractTransformer;

public class ReferenceTransformer extends AbstractTransformer {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String REPO_NAME = "Eddard";
    private static final String CLASSNAME = ReferenceTransformer.class.getSimpleName();

    private ArrayList<Integer> CallingList;
    private ArrayList<Integer> CalledList;

    public ReferenceTransformer() {
        this.CallingList = new ArrayList();
        this.CalledList = new ArrayList();
    }

    public void transformerData(ResultSet resultSet, String programName, GraphTraversalSource g) {

        GraphTraversal<Vertex, Vertex> program = g.V().hasLabel(programName);

        try {
            while (resultSet.next()) {
                int id = resultSet.getInt("CallingID");
                if (program.has("programID", id).hasNext()) {
                    CallingList.add(resultSet.getInt("CallingID"));
                    CalledList.add(resultSet.getInt("CalledID"));
                }
            }
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPO_NAME, CLASSNAME, "transformerADData", e.toString());
        }
    }

    public ArrayList getCallingList() {
        return this.CallingList;
    }

    public ArrayList getCalledList() {
        return this.CalledList;
    }
}
