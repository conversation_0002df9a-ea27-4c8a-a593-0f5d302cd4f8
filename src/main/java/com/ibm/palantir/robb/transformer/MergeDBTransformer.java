/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.robb.transformer;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversal;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversalSource;
import org.apache.tinkerpop.gremlin.structure.Vertex;

import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.transformer.AbstractTransformer;
import com.ibm.palantir.robb.util.ResolveName;

public class MergeDBTransformer extends AbstractTransformer {

    private ArrayList<HashMap<String, Object>> transformList;
    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String REPO_NAME = "Eddard";
    private static final String CLASSNAME = AbstractTransformer.class.getSimpleName();

    public MergeDBTransformer() {
        this.transformList = new ArrayList();
    }

    public void transformerADData(ResultSet resultSet, GraphTraversalSource g) {

        GraphTraversal<Vertex, Vertex> cicsProgram = g.V().hasLabel("Program");

        try {
            while (resultSet.next()) {
                String name = resultSet.getString("ProgramName");
                if (cicsProgram.has("name", name).hasNext() && !resultSet.getString("ProgramTypeID").equals("-1")) {
                    addNewRecord(resultSet);
                }
            }
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPO_NAME, CLASSNAME, "transformerADData", e.toString());
        }
    }

    private void addNewRecord(ResultSet resultSet) throws SQLException {
        ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
        HashMap<String, Object> resultValue = new HashMap();
        for (int i = 1; i < resultSetMetaData.getColumnCount() + 1; i++) {
            String key = ResolveName.resolveVertexProperty(resultSetMetaData.getColumnLabel(i));
            Object value = resultSet.getObject(resultSetMetaData.getColumnName(i));
            resultValue.put(key, value);
            if ((key.contains("name") || key.contains("Name")) && value != null) {
                resultValue.put("displayName", value);
            }
        }
        this.transformList.add(resultValue);
    }

    public ArrayList<HashMap<String, Object>> getTransformList() {
        return this.transformList;
    }
}
