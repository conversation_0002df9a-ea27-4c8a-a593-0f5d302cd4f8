/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.robb.util;

import java.text.MessageFormat;

public class ADReferenceSQLBuilder {

    public void getADConfiguration() {
    }

    public String getSQL(String ResourceID, String ResourceTable, String ResrouceType) {
        String sqlString = MessageFormat.format("SELECT dbo.Programs.ProgramID AS [CallingID], ResourceTable.{0} AS [CalledID] " +
                        "FROM dbo.StatementReference INNER JOIN " +
                        "dbo.OccurrencesStmt ON dbo.StatementReference.OccurID = dbo.OccurrencesStmt.OccurID INNER JOIN " +
                        "dbo.Programs ON dbo.OccurrencesStmt.ProgID = dbo.Programs.ProgramID INNER JOIN " +
                        "dbo.{1} AS ResourceTable ON dbo.StatementReference.ResourceID = ResourceTable.{0} " +
                        "WHERE (dbo.StatementReference.ResourceType = {2})",
                ResourceID, ResourceTable, ResrouceType);
        return sqlString;
    }

    public String getProgramSQL(String ResourceID, String ResourceTable, String ResrouceType) {
        String sqlString = MessageFormat.format("SELECT dbo.Programs.ProgramID AS [CallingID], ResourceTable.{0} AS [CalledID] " +
                        "FROM dbo.StatementReference INNER JOIN " +
                        "dbo.OccurrencesStmt ON dbo.StatementReference.OccurID = dbo.OccurrencesStmt.OccurID INNER JOIN " +
                        "dbo.Programs ON dbo.OccurrencesStmt.ProgID = dbo.Programs.ProgramID INNER JOIN " +
                        "dbo.{1} AS ResourceTable ON dbo.StatementReference.ResourceID = ResourceTable.{0} " +
                        "WHERE (dbo.StatementReference.ResourceType = {2}) and (ResourceTable.ProgramTypeID != -1)",
                ResourceID, ResourceTable, ResrouceType);
        return sqlString;
    }

    public String getReferenceSQL(String ResourceTable) {
        return MessageFormat.format("SELECT * FROM dbo.Programs", ResourceTable);
    }
}
