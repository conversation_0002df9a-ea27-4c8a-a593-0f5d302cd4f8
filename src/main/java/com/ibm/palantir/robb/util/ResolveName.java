/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.robb.util;

import java.util.Locale;

public class ResolveName {

    public static String resolveVertexName(String candidateName) {

        if (isCompliantToJavaClassConvention(candidateName))
            return candidateName;

        else {

            // manipulating ResolveName (Java Convention)
            candidateName = toJavaClassConvention(candidateName);

            return candidateName;
        }
    }

    public static String resolveVertexProperty(String candidateName) {

        if (isCompliantToJavaVariableConvention(candidateName))
            return candidateName;

        else {

            // manipulating ResolveName (Java Convention)
            candidateName = toJavaVariableConvention(candidateName);

            return candidateName;
        }
    }

    public static String toJavaClassConvention(String name) {

        // if all chars are uppercase, then ResolveName is transformed in a lowercase version

        boolean allUpperCase = true;
        for (int i = 0; i < name.length(); i++) {
            if (Character.isLowerCase(name.charAt(i))) {
                allUpperCase = false;
                break;
            }
        }

        if (allUpperCase) {
            name = name.toLowerCase(Locale.ENGLISH);
        }

        if (name.contains(" ")) {
            int pos;
            while (name.contains(" ")) {
                pos = name.indexOf(" ");
                name = name.substring(0, pos) + (name.charAt(pos + 1) + "").toUpperCase(Locale.ENGLISH) + name.substring(pos + 2);
            }
        }

        if (name.contains("_")) {
            int pos;
            while (name.contains("_")) {
                pos = name.indexOf("_");
                if (pos < name.length() - 1) {
                    // the '_' char is not in last position
                    name = name.substring(0, pos) + (name.charAt(pos + 1) + "").toUpperCase(Locale.ENGLISH) + name.substring(pos + 2);
                } else {
                    // the '_' char is in last position
                    name = name.substring(0, name.length() - 1);
                }
            }
        }

        if (name.contains("-")) {
            int pos;
            while (name.contains("-")) {
                pos = name.indexOf("-");
                name = name.substring(0, pos) + (name.charAt(pos + 1) + "").toUpperCase(Locale.ENGLISH) + name.substring(pos + 2);
            }
        }

        // First char must be uppercase
        if (Character.isLowerCase(name.charAt(0)))
            name = name.substring(0, 1).toUpperCase(Locale.ENGLISH) + name.substring(1);

        return name;

    }

    public static String toJavaVariableConvention(String name) {

        // if all chars are uppercase, then ResolveName is transformed in a lowercase version

        boolean allUpperCase = true;
        for (int i = 0; i < name.length(); i++) {
            if (Character.isLowerCase(name.charAt(i))) {
                allUpperCase = false;
                break;
            }
        }

        if (allUpperCase) {
            name = name.toLowerCase(Locale.ENGLISH);
        }

        if (name.contains(" ")) {
            int pos;
            while (name.contains(" ")) {
                pos = name.indexOf(" ");
                name = name.substring(0, pos) + (name.charAt(pos + 1) + "").toUpperCase(Locale.ENGLISH) + name.substring(pos + 2);
            }
        }

        if (name.contains("_")) {
            int pos;
            while (name.contains("_")) {
                pos = name.indexOf("_");
                name = name.substring(0, pos) + (name.charAt(pos + 1) + "").toUpperCase(Locale.ENGLISH) + name.substring(pos + 2);
            }
        }

        if (name.contains("-")) {
            int pos;
            while (name.contains("-")) {
                pos = name.indexOf("-");
                name = name.substring(0, pos) + (name.charAt(pos + 1) + "").toUpperCase(Locale.ENGLISH) + name.substring(pos + 2);
            }
        }

        // First char must be lowercase
        if (Character.isUpperCase(name.charAt(0)))
            name = name.substring(0, 1).toLowerCase(Locale.ENGLISH) + name.substring(1);

        return name;
    }

    public static boolean isCompliantToJavaClassConvention(String candidateName) {

        if (!(candidateName.contains(" ") || candidateName.contains("_") || candidateName.contains("-")) && Character
                .isUpperCase(candidateName.charAt(0))) {

            // if all chars are uppercase, then ResolveName is transformed in a lowercase version

            boolean allUpperCase = true;
            for (int i = 0; i < candidateName.length(); i++) {
                if (Character.isLowerCase(candidateName.charAt(i))) {
                    allUpperCase = false;
                    break;
                }
            }

            if (allUpperCase)
                return false;
            else
                return true;
        } else
            return false;
    }

    public static boolean isCompliantToJavaVariableConvention(String candidateName) {

        if (!(candidateName.contains(" ") || candidateName.contains("_") || candidateName.contains("-")) && Character
                .isLowerCase(candidateName.charAt(0)))
            return true;
        else
            return false;

    }
}
