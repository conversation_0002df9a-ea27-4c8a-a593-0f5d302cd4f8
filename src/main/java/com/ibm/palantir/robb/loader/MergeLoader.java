/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.robb.loader;

import com.google.gson.JsonObject;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.connection.OrientDBConnection;
import com.ibm.palantir.robb.util.ResolveName;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversal;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversalSource;
import org.apache.tinkerpop.gremlin.structure.Vertex;

import java.util.ArrayList;
import java.util.HashMap;

public class MergeLoader {


    private GraphTraversalSource g;

    public MergeLoader(String persistID) throws Exception {
        ConfigManager configManager = ConfigManager.getConfigManager();

        JsonObject jsonObject = configManager.searchConfig("persist", persistID);
        OrientDBConnection graphConnection = new OrientDBConnection(jsonObject.get("url").getAsString(), jsonObject.get("user").getAsString(), jsonObject.get("password").getAsString());
        this.g = graphConnection.getTraversal();
    }

    public void addVertex(ArrayList<HashMap<String, Object>> LoaderList, String fromLabelName, String toLabelName, String idName) {

        for (HashMap<String, Object> record : LoaderList) {
            fromLabelName = ResolveName.resolveVertexName(fromLabelName);

            if (!this.g.V().hasLabel(fromLabelName).has(ResolveName.resolveVertexProperty(idName), record.get(idName)).hasNext()) {
                Vertex vertex = this.g.addV(fromLabelName).property("id", record.get(idName)).next();
                for (String key : record.keySet()) {

                    String propertyName = ResolveName.resolveVertexProperty(key);
                    if (record.get(key) != null) {
                        vertex.property(propertyName, record.get(key));
                    }
                }
                GraphTraversal<Vertex, Vertex> linkedG = this.g.V().hasLabel(toLabelName).has("name", record.get("programName"));
                while (linkedG.hasNext()) {
                    Vertex nextOne = linkedG.next();
                    g.addE("define").from(vertex).to(nextOne)
                            .property("source_target", String.format("%s_%s", vertex.value("id").toString(), nextOne.value("id").toString()))
                            .iterate();
                }
            }
        }
    }

    public GraphTraversalSource getG() {
        return g;
    }
}
