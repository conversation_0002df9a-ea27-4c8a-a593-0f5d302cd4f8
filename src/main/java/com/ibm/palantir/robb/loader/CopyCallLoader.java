/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.robb.loader;

import java.util.Iterator;

import org.apache.tinkerpop.gremlin.process.traversal.Path;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversal;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversalSource;
import org.apache.tinkerpop.gremlin.structure.Vertex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CopyCallLoader {

    private static final Logger LOG = LoggerFactory.getLogger(CopyCallLoader.class);

    public void getRing(String startNode, String RelationshipType, GraphTraversalSource g) {
        GraphTraversal path = g.V().hasLabel(startNode).in(RelationshipType).out("define").path();
        addCopyCall(path, g);
        LOG.info("Add " + RelationshipType + " successfully!");
    }

    public void addCopyCall(GraphTraversal path, GraphTraversalSource g) {
        while (path.hasNext()) {
            Path item = (Path) path.next();
            Iterator<Object> item_iterator = item.iterator();
            if (!item_iterator.hasNext()) {
                continue;
            }
            Vertex define_vertex = (Vertex) item_iterator.next();
            Vertex program_vertex = null;
            while (item_iterator.hasNext()) {
                Vertex vertex = (Vertex) item_iterator.next();
                if (vertex.label().equals("Program")) {
                    program_vertex = vertex;
                }
            }
            if (program_vertex != null && define_vertex != null) {
                if (!g.V(program_vertex.id()).out("include").hasId(define_vertex.id()).hasNext()) {
                    g.addE("include").from(program_vertex).to(define_vertex)
                            .property("source_target", String.format("%s_%s", program_vertex.value("id").toString(), define_vertex.value("id").toString()))
                            .iterate();

                }
            }
        }
    }
}
