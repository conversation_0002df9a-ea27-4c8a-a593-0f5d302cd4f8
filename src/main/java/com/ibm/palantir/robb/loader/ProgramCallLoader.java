/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.robb.loader;

import java.util.Iterator;

import org.apache.tinkerpop.gremlin.process.traversal.Path;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversal;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversalSource;
import org.apache.tinkerpop.gremlin.structure.Vertex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ProgramCallLoader {

    private static final Logger LOG = LoggerFactory.getLogger(ProgramCallLoader.class);

    public void getRing(GraphTraversalSource g) {
        GraphTraversal path = g.V().hasLabel("ProgramDefinition").out("define").in("contain").out("contain").in("define").in("ProgramCallDefinition").cyclicPath().path();
        addProgramCall(path, g);
        path = g.V().hasLabel("ProgramDefinition").out("define").in("contain").out("transactionalDependency").out("contain").in("define").in("ProgramCallDefinition").cyclicPath().path();
        addProgramCall(path, g);
        LOG.info("Add ProgramCall successfully!");
    }

    public void addProgramCall(GraphTraversal path, GraphTraversalSource g) {
        while (path.hasNext()) {
            Path item = (Path) path.next();
            Iterator<Object> item_iterator = item.iterator();
            if (!item_iterator.hasNext()) {
                continue;
            }
            Vertex start_vertex = (Vertex) item_iterator.next();
            Vertex end_vertex = null;
            while (item_iterator.hasNext()) {
                end_vertex = (Vertex) item_iterator.next();
            }
            if (start_vertex.id() != end_vertex.id()) {
                continue;
            }
            int num = 0;
            item_iterator = item.iterator();
            Vertex start_program = null;
            Vertex end_program = null;
            while (item_iterator.hasNext()) {
                Vertex vertex = (Vertex) item_iterator.next();
                if (vertex.label().equals("Program")) {
                    num++;
                    if (num == 1) {
                        start_program = vertex;
                    } else if (num == 2) {
                        end_program = vertex;
                    } else {
                        start_program = null;
                        end_program = null;
                        LOG.warn("This path is not correct: " + item);
                    }
                }
            }
            if (start_program != null && end_program != null) {
                if (!g.V(start_program.id()).out("programCall").hasId(end_program.id()).hasNext()) {
                    g.addE("programCall").from(start_program).to(end_program)
                            .property("source_target", String.format("%s_%s", start_program.value("id").toString(), end_program.value("id").toString()))
                            .iterate();
                }
            }
        }
    }

}
