/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.robb.loader;

import org.apache.tinkerpop.gremlin.process.traversal.Path;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversal;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversalSource;
import org.apache.tinkerpop.gremlin.structure.Vertex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Iterator;

public class SQLTableCallLoader {
    private static final Logger LOG = LoggerFactory.getLogger(SQLTableCallLoader.class);


    private HashMap<String, HashMap<String, String>> defineMap = new HashMap<String, HashMap<String, String>>() {
        {
            put("SQLTableDefinition", new HashMap<String, String>() {
                {
                    put("fromKeyName", "tableName");
                    put("toLabelName", "Db2Table");
                    put("toKeyName", "name");
                }
            });
        }
    };


    public void getRing(String startNode, GraphTraversalSource g) {
        GraphTraversal path = g.V().hasLabel(startNode).in("SQLTableCallDefinition").out("define").in("contain").out("contain")
                .out("connect").out("contain").out("contain").out("hasMember").path();
        addSQLTableCall(startNode, path, g);
        LOG.info("Add SQLTableCall successfully!");
    }


    public void addSQLTableCall(String startNode, GraphTraversal path, GraphTraversalSource g) {
        String toLabelName = defineMap.get(startNode).get("toLabelName");
        String fromKeyName = defineMap.get(startNode).get("fromKeyName");
        String toKeyName = defineMap.get(startNode).get("toKeyName");
        while (path.hasNext()) {
            Path item = (Path) path.next();
            Iterator<Object> item_iterator = item.iterator();
            if (!item_iterator.hasNext()) {
                continue;
            }
            Vertex define_vertex = (Vertex) item_iterator.next();
            Vertex program_vertex = null;
            Vertex end_vertex = null;
            while (item_iterator.hasNext()) {
                Vertex vertex = (Vertex) item_iterator.next();
                if (vertex.label().equals("Program")) {
                    program_vertex = vertex;
                }
                end_vertex = vertex;
            }
            if (program_vertex != null && end_vertex != null) {
                if (end_vertex.label().equals(toLabelName) && define_vertex.property(fromKeyName).value().equals(end_vertex.property(toKeyName).value())) {
                    if (!g.V(define_vertex.id()).out("define").hasId(end_vertex.id()).hasNext()) {
                        g.addE("define").from(define_vertex).to(end_vertex)
                                .property("source_target", String.format("%s_%s", define_vertex.value("id").toString(), end_vertex.value("id").toString()))
                                .iterate();
                    }
                    if (!g.V(program_vertex.id()).out("sqlTableCall").hasId(end_vertex.id()).hasNext()) {
                        g.addE("sqlTableCall").from(program_vertex).to(end_vertex)
                                .property("source_target", String.format("%s_%s", program_vertex.value("id").toString(), end_vertex.value("id").toString()))
                                .iterate();
                    }
                }
            }
        }
    }
}
