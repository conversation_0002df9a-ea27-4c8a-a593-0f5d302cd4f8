/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.robb.pipeline;

import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.extractor.SQLServerExtractor;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.robb.loader.MergeLoader;
import com.ibm.palantir.robb.transformer.MergeDBTransformer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class AddProgramDefinition extends AbstractPipeline {
    private static final Logger LOG = LoggerFactory.getLogger(AddProgramDefinition.class);

    public static void main(String[] args) throws Exception {
        ConfigManager.getConfigManager("couchbase", "localhost", "Administrator", "123456");

        PipelineConf pipelineConf = new PipelineConf();
        pipelineConf.setConfig("config");
        List extractList = new ArrayList<Integer>();
        // Make sure the first extract id is the correct source type
        extractList.add(0);
        pipelineConf.setExtract(extractList);
        List persistList = new ArrayList<Integer>();
        // Make sure the first persist id is the correct source type
        persistList.add(0);
        pipelineConf.setPersist(persistList);
        AddProgramDefinition addSource = new AddProgramDefinition();
        addSource.run(pipelineConf);
    }

    public String addProgramDefinition(PipelineConf pipelineConf) throws Exception {

        String sql = "SELECT * FROM dbo.Programs";
        String extractID = pipelineConf.getExtract().get(0).toString();
        String persistID = pipelineConf.getPersist().get(0).toString();

        SQLServerExtractor extractor = new SQLServerExtractor(extractID);
        ResultSet resultSet = extractor.getResultSet(sql);

        MergeDBTransformer transformer = new MergeDBTransformer();
        MergeLoader loader = new MergeLoader(persistID);


        transformer.transformerADData(resultSet, loader.getG());

        loader.addVertex(transformer.getTransformList(), "ProgramDefinition", "Program", "programID");
        return "Add ProgramDefinition success!";
    }

    public String run(PipelineConf pipelineConf) throws Exception {
        return addProgramDefinition(pipelineConf);
    }
}
