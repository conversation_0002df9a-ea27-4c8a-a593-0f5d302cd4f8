/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.robb.pipeline;

import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.extractor.SQLServerExtractor;
import com.ibm.palantir.catelyn.loader.GremlinLoader;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.robb.loader.CopyCallLoader;
import com.ibm.palantir.robb.loader.ProgramCallLoader;
import com.ibm.palantir.robb.loader.SQLTableCallLoader;
import com.ibm.palantir.robb.transformer.ReferenceTransformer;
import com.ibm.palantir.robb.util.ADReferenceSQLBuilder;
import com.ibm.palantir.robb.util.ResolveName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


public class StatementReference extends AbstractPipeline {

    private static final Logger LOG = LoggerFactory.getLogger(StatementReference.class);
    private HashMap<String, HashMap<String, String>> typeMap = new HashMap<>();

    public StatementReference() {
        HashMap<String, String> ProgramCallDefinition = new HashMap<>();
        ProgramCallDefinition.put("ResourceID", "ProgramID");
        ProgramCallDefinition.put("ResourceTable", "Programs");
        ProgramCallDefinition.put("ResourceType", "5");
        ProgramCallDefinition.put("GraphName", "ProgramDefinition");
        typeMap.put("ProgramCallDefinition", ProgramCallDefinition);

        HashMap<String, String> FileCallDefinition = new HashMap<>();
        FileCallDefinition.put("ResourceID", "FileID");
        FileCallDefinition.put("ResourceTable", "Files");
        FileCallDefinition.put("ResourceType", "9");
        FileCallDefinition.put("GraphName", "FileDefinition");
        typeMap.put("FileCallDefinition", FileCallDefinition);

        HashMap<String, String> TransactionCallDefinition = new HashMap<>();
        TransactionCallDefinition.put("ResourceID", "ResourceID");
        TransactionCallDefinition.put("ResourceTable", "Resources");
        TransactionCallDefinition.put("ResourceType", "14");
        TransactionCallDefinition.put("GraphName", "TransactionDefinition");
        typeMap.put("TransactionCallDefinition", TransactionCallDefinition);

        HashMap<String, String> SQLTableCallDefinition = new HashMap<>();
        SQLTableCallDefinition.put("ResourceID", "SqlTableID");
        SQLTableCallDefinition.put("ResourceTable", "SQLTables");
        SQLTableCallDefinition.put("ResourceType", "1");
        SQLTableCallDefinition.put("GraphName", "SQLTableDefinition");
        typeMap.put("SQLTableCallDefinition", SQLTableCallDefinition);

        HashMap<String, String> CopyIncludeDefinition = new HashMap<>();
        CopyIncludeDefinition.put("ResourceID", "ResourceID");
        CopyIncludeDefinition.put("ResourceTable", "Resources");
        CopyIncludeDefinition.put("ResourceType", "13");
        CopyIncludeDefinition.put("GraphName", "Copy");
        typeMap.put("CopyIncludeDefinition", CopyIncludeDefinition);

        HashMap<String, String> PLICopyIncludeDefinition = new HashMap<>();
        PLICopyIncludeDefinition.put("ResourceID", "ResourceID");
        PLICopyIncludeDefinition.put("ResourceTable", "Resources");
        PLICopyIncludeDefinition.put("ResourceType", "67");
        PLICopyIncludeDefinition.put("GraphName", "PLICopy");
        typeMap.put("PLICopyIncludeDefinition", PLICopyIncludeDefinition);

        HashMap<String, String> AssemblerCopyIncludeDefinition = new HashMap<>();
        AssemblerCopyIncludeDefinition.put("ResourceID", "ResourceID");
        AssemblerCopyIncludeDefinition.put("ResourceTable", "Resources");
        AssemblerCopyIncludeDefinition.put("ResourceType", "99");
        AssemblerCopyIncludeDefinition.put("GraphName", "AssemblerCopy");
        typeMap.put("AssemblerCopyIncludeDefinition", AssemblerCopyIncludeDefinition);
    }

    public static void main(String[] args) throws Exception {
        ConfigManager.getConfigManager("couchbase", "localhost", "Administrator", "123456");

        PipelineConf pipelineConf = new PipelineConf();
        pipelineConf.setParameter("ProgramCallDefinition");
        List extractList = new ArrayList<Integer>();
        // Make sure the first extract id is the correct source type
        extractList.add(0);
        pipelineConf.setExtract(extractList);
        List persistList = new ArrayList<Integer>();
        // Make sure the first persist id is the correct source type
        persistList.add(0);
        pipelineConf.setPersist(persistList);
        StatementReference statementReference = new StatementReference();
        statementReference.addStatementReference(pipelineConf);
    }


    public String addStatementReference(PipelineConf pipelineConf) throws Exception {

        String extractID = pipelineConf.getExtract().get(0).toString();
        String persistID = pipelineConf.getPersist().get(0).toString();

        SQLServerExtractor extractor = new SQLServerExtractor(extractID);
        ReferenceTransformer transformer = new ReferenceTransformer();
        GremlinLoader loader = new GremlinLoader(persistID);

        ADReferenceSQLBuilder sqlBuilder = new ADReferenceSQLBuilder();
        String RelationshipType = pipelineConf.getParameter();
        if (RelationshipType == null) {
            String errCode = ErrorCode.PluginNullParamError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Robb", "StatementReference");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
        if (!typeMap.containsKey(RelationshipType)) {
            String errCode = ErrorCode.PluginParamFormatError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Robb", "StatementReference");
            LOG.error(msg);
            throw new ServiceException(errCode, msg);
        }
        HashMap<String, String> type = typeMap.get(RelationshipType);
        String resourceID = type.get("ResourceID");
        String resourceTable = type.get("ResourceTable");
        String resourceType = type.get("ResourceType");
        String sql = sqlBuilder.getSQL(resourceID, resourceTable, resourceType);


        String graphResourceTable = type.get("GraphName");
        String graphResourceID = ResolveName.resolveVertexProperty(resourceID);

        if (RelationshipType.equals("ProgramCallDefinition")) {
            sql = sqlBuilder.getProgramSQL(resourceID, resourceTable, resourceType);
        }

        System.out.println(sql);

        ResultSet resultSet = extractor.getResultSet(sql);

        transformer.transformerData(resultSet, "ProgramDefinition", loader.getG());


        ArrayList<Integer> calledList = transformer.getCalledList();
        ArrayList<Integer> callingList = transformer.getCallingList();

        AddTable addResourceTable = new AddTable();
        addResourceTable.addTableFromDB(resourceTable, resourceID, calledList, graphResourceTable, pipelineConf, loader.getG());


        for (int i = 0; i < calledList.size(); i++) {
            loader.addEdge("ProgramDefinition", "programID", callingList.get(i), graphResourceTable, graphResourceID, calledList.get(i), RelationshipType);
        }
        if (RelationshipType.equals("ProgramCallDefinition")) {
            ProgramCallLoader programCallLoader = new ProgramCallLoader();
            programCallLoader.getRing(loader.getG());
        }
        if (RelationshipType.equals("SQLTableCallDefinition")) {
            SQLTableCallLoader sqlTableCallLoader = new SQLTableCallLoader();
            sqlTableCallLoader.getRing(graphResourceTable, loader.getG());
        }
        if (RelationshipType.equals("CopyIncludeDefinition") || RelationshipType.equals("PLICopyIncludeDefinition") || RelationshipType.equals("AssemblerCopyIncludeDefinition")) {
            CopyCallLoader copyCallLoader = new CopyCallLoader();
            copyCallLoader.getRing(graphResourceTable, RelationshipType, loader.getG());
        }
        return "Add Statement Reference Success!";
    }

    public String run(PipelineConf pipelineConf) throws Exception {
        return addStatementReference(pipelineConf);
    }
}
