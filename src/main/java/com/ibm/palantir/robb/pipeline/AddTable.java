/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.robb.pipeline;

import com.ibm.palantir.catelyn.extractor.SQLServerExtractor;
import com.ibm.palantir.catelyn.loader.GremlinLoader;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.robb.transformer.CommonTransformer;
import com.ibm.palantir.robb.util.ResolveName;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversalSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.text.MessageFormat;
import java.util.ArrayList;

public class AddTable extends AbstractPipeline {

    private static final Logger LOG = LoggerFactory.getLogger(AddTable.class);

    public void addTableFromDB(String tableName, String tableID, ArrayList idList, PipelineConf pipelineConf) throws Exception {
        String sql = MessageFormat.format("SELECT * FROM dbo.{0}", tableName);

        String extractID = pipelineConf.getExtract().get(0).toString();
        String persistID = pipelineConf.getPersist().get(0).toString();

        SQLServerExtractor extractor = new SQLServerExtractor(extractID);
        ResultSet resultSet = extractor.getResultSet(sql);

        CommonTransformer transformer = new CommonTransformer();
        GremlinLoader loader = new GremlinLoader(persistID);

        if (tableName.equals("Programs")) {
            tableName = "ProgramDefinition";
        } else {
            tableName = ResolveName.resolveVertexName(tableName);
        }


        transformer.transformerADData(resultSet, tableName, tableID, idList, loader.getG());

        loader.addVertex(transformer.getTransformList(), tableName);
    }

    public void addTableFromDB(String tableName, String tableID, ArrayList idList, String graphResourceTable, PipelineConf pipelineConf, GraphTraversalSource g) throws Exception {
        String sql = MessageFormat.format("SELECT * FROM dbo.{0}", tableName);

        String extractID = pipelineConf.getExtract().get(0).toString();
        SQLServerExtractor extractor = new SQLServerExtractor(extractID);
        ResultSet resultSet = extractor.getResultSet(sql);

        CommonTransformer transformer = new CommonTransformer();
        GremlinLoader loader = new GremlinLoader(g);

        transformer.transformerADData(resultSet, graphResourceTable, tableID, idList, loader.getG());

        loader.addVertex(transformer.getTransformList(), graphResourceTable);
    }
}
