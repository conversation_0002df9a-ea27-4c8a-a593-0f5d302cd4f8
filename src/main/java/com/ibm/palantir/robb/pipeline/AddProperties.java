/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.robb.pipeline;

import java.util.ArrayList;
import java.util.List;

import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.robb.task.SubsystemTask;

public class AddProperties {

    public static void main(String[] args) throws Exception {
        ConfigManager.getConfigManager("couchbase", "localhost", "Administrator", "123456");

        PipelineConf pipelineConf = new PipelineConf();
        pipelineConf.setConfig("config");
        List extractList = new ArrayList<Integer>();
        // Make sure the first extract id is the correct source type
        extractList.add(0);
        pipelineConf.setExtract(extractList);
        List persistList = new ArrayList<Integer>();
        // Make sure the first persist id is the correct source type
        persistList.add(0);
        pipelineConf.setPersist(persistList);
        AddProperties addSubsystemPort = new AddProperties();
        addSubsystemPort.run(pipelineConf);
    }

    private String addSubsystemPort(PipelineConf pipelineConf) throws Exception {
        String extractID = pipelineConf.getExtract().get(0).toString();
        String persistID = pipelineConf.getPersist().get(0).toString();
        SubsystemTask subsystemTask = new SubsystemTask(persistID);
        subsystemTask.getIP();
        return "add Subsystem access_via successfully!";
    }

    public String run(PipelineConf pipelineConf) throws Exception {
        return addSubsystemPort(pipelineConf);
    }
}
