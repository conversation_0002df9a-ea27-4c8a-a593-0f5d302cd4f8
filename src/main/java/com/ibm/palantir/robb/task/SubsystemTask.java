/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.robb.task;

import com.google.gson.JsonObject;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.connection.OrientDBConnection;
import org.apache.tinkerpop.gremlin.process.traversal.Path;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversal;
import org.apache.tinkerpop.gremlin.process.traversal.dsl.graph.GraphTraversalSource;
import org.apache.tinkerpop.gremlin.structure.Vertex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class SubsystemTask {

    private static final Logger LOG = LoggerFactory.getLogger(SubsystemTask.class);

    private GraphTraversalSource g;

    public SubsystemTask(String persistID) throws Exception {
        ConfigManager configManager = ConfigManager.getConfigManager();

        JsonObject jsonObject = configManager.searchConfig("persist", persistID);
        OrientDBConnection graphConnection = new OrientDBConnection(jsonObject.get("url").getAsString(), jsonObject.get("user").getAsString(), jsonObject.get("password").getAsString());
        this.g = graphConnection.getTraversal();
    }


    public void getIP() {
        getIP(g);
    }

    public void getIP(GraphTraversalSource g) {
        GraphTraversal path = g.V().hasLabel("DB2Subsystem", "CICSRegion").out("federates", "uses").out("uses").out("bindsAsPrimary").path();
        while (path.hasNext()) {
            Path item = (Path) path.next();
            if (item.size() != 4) {
                continue;
            }
            Vertex start_vertex = item.get(0);
            Vertex bindAddress_vertex = item.get(2);
            Vertex ipAddress_vertex = item.get(3);
            String port = bindAddress_vertex.property("portNumber").value().toString();
            String ip = ipAddress_vertex.property("label").value().toString();
            String accessed_via = ip + ":" + port;
            if (start_vertex.keys().contains("accessed_via")) {
                List access_list = (List) start_vertex.property("accessed_via").value();
                if (!access_list.contains(accessed_via)) {
                    access_list.add(accessed_via);
                    start_vertex.property("accessed_via", access_list);
                }
            } else {
                List access_list = new ArrayList();
                access_list.add(accessed_via);
                start_vertex.property("accessed_via", access_list);
            }
        }
        LOG.info("Add accessed_via successfully!");
    }


}
