/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.pipeline;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.xml.namespace.QName;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import org.junit.Ignore;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TemporaryFolder;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import static org.powermock.api.mockito.PowerMockito.doNothing;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;
import static org.powermock.api.mockito.PowerMockito.whenNew;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import com.ibm.palantir.catelyn.jaxb.CDMERSpecification;
import com.ibm.palantir.catelyn.jaxb.Create;
import com.ibm.palantir.catelyn.jaxb.Idml;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSProgram;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSRegion;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSTransaction;
import com.ibm.palantir.catelyn.jaxb.SysZOSLPAR;
import com.ibm.palantir.catelyn.jaxb.SysZOSSysplex;
import com.ibm.palantir.catelyn.jaxb.SysZOSZOS;
import com.ibm.palantir.catelyn.jaxb.SysZOSZReportFile;
import com.ibm.palantir.catelyn.jaxb.SysZOSZSeriesComputerSystem;
import com.ibm.palantir.catelyn.jaxb.Virtualizes;
import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.LPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Sysplex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZOS;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSubSystem;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSFileRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSRegionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableOverrideRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.LPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.SysplexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZOSRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSeriesComputerRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSubSystemRepository;
import com.ibm.palantir.sansa.extractor.ElementExtractor;
import com.ibm.palantir.sansa.extractor.GetXMLFile;
import com.ibm.palantir.sansa.extractor.IdmlExtractor;
import com.ibm.palantir.sansa.loader.ElementLoader;
import com.ibm.palantir.sansa.loader.Layer4CICSLoader;
import com.ibm.palantir.sansa.loader.ZReportFileLoader;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.Unmarshaller;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ JAXBContext.class })
public class PopulateDLATest {

    @Rule
    public TemporaryFolder tmpFolder = new TemporaryFolder();
    @Mock
    ElementLoader elementLoader;
    @Mock
    Layer4CICSLoader layer4CICSLoader;
    @Mock
    ZReportFileLoader zReportFileLoader;
    @Mock
    GetXMLFile getXMLFile;
    @Mock
    IdmlExtractor idmlExtractor;
    @Mock
    ElementExtractor elementExtractor;
    @Mock
    FileInputStream fileInputStream;
    @Mock
    MockMultipartFile mockMultipartFile;
    @Mock
    MultipartFile multipartFile;
    @Mock
    InputStream inputStream;
    @Mock
    ZSeriesComputerRepository zSeriesComputerRepository;
    @Mock
    LPARRepository lparRepository;
    @Mock
    ZOSRepository zOSRepository;
    @Mock
    SysplexRepository sysplexRepository;
    @Mock
    ZSubSystemRepository zSubSystemRepository;
    @Mock
    CICSRegionRepository cicsRegionRepository;
    @Mock
    CICSSystemInitTableRepository cicsSystemInitTableRepository;
    @Mock
    CICSSystemInitTableOverrideRepository cicsSystemInitTableOverrideRepository;
    @Mock
    CICSProgramRepository cicsProgramRepository;
    @Mock
    CICSTransactionRepository cicsTransactionRepository;
    @Mock
    CICSFileRepository cicsFileRepository;
    @Mock
    Idml idml;
    @Mock
    Create create;
    @Mock
    CDMERSpecification specification;
    @Mock
    SysZOSLPAR sysZOSLPAR;
    @Mock
    LPAR lpar;
    @Mock
    SysZOSZSeriesComputerSystem sysZOSZSeriesComputerSystem;
    @Mock
    Virtualizes virtualizes;
    @Mock
    SysZOSZOS sysZOSZOS;
    @Mock
    ZOS zos;
    @Mock
    SysZOSSysplex sysZOSSysplex;
    @Mock
    Sysplex sysplex;
    @Mock
    SysZOSCICSRegion sysZOSCICSRegion;
    @Mock
    ZSubSystem zSubSystem;
    @Mock
    CICSRegion cicsRegion;
    @Mock
    SysZOSZReportFile zoszReportFile;
    @Mock
    SysZOSCICSProgram sysZOSCICSProgram;
    @Mock
    SysZOSCICSTransaction sysZOSCICSTransaction;
    @Mock
    Unmarshaller jaxbUnmarshaller;

    @Mock
    QName qName;
    private PopulateDLA tester = new PopulateDLA();
    // private ElementExtractor elementExtractor = new ElementExtractor();
    private List<JAXBElement<?>> jaxbElements = new ArrayList<>();
    private HashMap<String, Object> repos = new HashMap<>();

    private void prepareLoader() throws Exception {

        repos.put("zSeriesComputer", zSeriesComputerRepository);
        repos.put("lpar", lparRepository);
        repos.put("zos", zOSRepository);
        repos.put("sysplex", sysplexRepository);
        repos.put("zSubSystem", zSubSystemRepository);
        repos.put("cicsRegion", cicsRegionRepository);
        repos.put("cicsSystemInitTable", cicsSystemInitTableRepository);
        repos.put("cicsSystemInitTableOverride", cicsSystemInitTableOverrideRepository);
        repos.put("cicsProgram", cicsProgramRepository);
        repos.put("cicsTransaction", cicsTransactionRepository);
        repos.put("cicsFile", cicsFileRepository);
        // when(repos.get("zSeriesComputer")).thenReturn(zSeriesComputerRepository);
        // when(repos.get("zoslpar")).thenReturn(zoslparRepository);
        // when(repos.get("zos")).thenReturn(zOSRepository);
        // when(repos.get("zSubSystem")).thenReturn(zSubSystemRepository);
        // when(repos.get("cicsRegion")).thenReturn(cicsRegionRepository);
        // when(repos.get("cicsSystemInitTable")).thenReturn(cicsSystemInitTableRepository);
        // when(repos.get("cicsSystemInitTableOverride")).thenReturn(cicsSystemInitTableOverrideRepository);
        // when(repos.get("cicsProgram")).thenReturn(cicsProgramRepository);
        // when(repos.get("cicsTransaction")).thenReturn(cicsTransactionRepository);
        // when(repos.get("cicsFile")).thenReturn(cicsFileRepository);

        whenNew(ElementLoader.class).withArguments(zSeriesComputerRepository, lparRepository, zOSRepository,
                sysplexRepository, zSubSystemRepository,
                cicsRegionRepository).thenReturn(elementLoader);
        whenNew(ZReportFileLoader.class).withArguments(cicsSystemInitTableRepository,
                cicsSystemInitTableOverrideRepository, cicsProgramRepository,
                cicsTransactionRepository, cicsFileRepository).thenReturn(zReportFileLoader);
        whenNew(Layer4CICSLoader.class).withArguments(cicsProgramRepository, cicsTransactionRepository)
                .thenReturn(layer4CICSLoader);
        when(JPAManager.getJPAManager().getRepos()).thenReturn(repos);

    }

    private void prepareIdml() throws Exception {
        Idml.OperationSet operationSet = mock(Idml.OperationSet.class);
        List<Idml.OperationSet> operationSetList = new ArrayList<>();
        operationSetList.add(operationSet);
        when(idml.getOperationSets()).thenReturn(operationSetList);
        List<Object> objectList = new ArrayList<>();
        objectList.add(create);
        when(operationSet.getCreatesAndModifiesAndDeletes()).thenReturn(objectList);

        List<CDMERSpecification> specifications = new ArrayList<>();
        specifications.add(specification);
        when(create.getCDMERSpecifications()).thenReturn(specifications);
        JAXBElement<?> element1 = new JAXBElement(qName, SysZOSLPAR.class, sysZOSLPAR);
        jaxbElements.add(element1);
        JAXBElement<?> element2 = new JAXBElement(qName, SysZOSZReportFile.class, zoszReportFile);
        jaxbElements.add(element2);
        when(specification.getManagedElementsAndRelationships()).thenReturn(jaxbElements);
        // elementExtractor.managedElementsAndRelationships = jaxbElements;
    }

    private void mockPopulateDlaFromIdml() {

        when(elementExtractor.getElementByClassName(SysZOSLPAR.class.getName())).thenReturn(sysZOSLPAR);
        // doReturn(sysZOSLPAR).when(elementExtractor).getElementByClassName(SysZOSLPAR.class.getName());
        doNothing().when(elementLoader).loadLpar(sysZOSLPAR, "", false, new ArrayList<>());
        when(elementLoader.loadSysplex(sysZOSSysplex, "53231323", "", false)).thenReturn(sysplex);

        // when(elementExtractor.getElementByClassName(SysZOSZSeriesComputerSystem.class.getName())).thenReturn(sysZOSZSeriesComputerSystem);
        // doReturn(sysZOSZSeriesComputerSystem).when(elementExtractor).getElementByClassName(SysZOSZSeriesComputerSystem.class.getName());
        doNothing().when(elementLoader).loadZSeriesComputer(sysZOSZSeriesComputerSystem, "", false);
        // when(elementExtractor.getElementByClassName(Virtualizes.class.getName())).thenReturn(virtualizes);
        // doReturn(virtualizes).when(elementExtractor).getElementByClassName(Virtualizes.class.getName());
        // doNothing().when(elementLoader).loadVirtualizes(virtualizes);

        // when(elementExtractor.getElementByClassName(SysZOSZOS.class.getName())).thenReturn(sysZOSZOS);
        // doReturn(sysZOSZOS).when(elementExtractor).getElementByClassName(SysZOSZOS.class.getName());
        when(elementLoader.loadZosZos(sysZOSZOS, "53231323", "335335", "", false, new ArrayList<>())).thenReturn(zos);

        // when(elementExtractor.getElementByClassName(SysZOSCICSRegion.class.getName())).thenReturn(sysZOSCICSRegion);
        // doReturn(sysZOSCICSRegion).when(elementExtractor).getElementByClassName(SysZOSCICSRegion.class.getName());
        // when(elementLoader.loadZSubSystemFromCICSRegion(sysZOSCICSRegion)).thenReturn(zSubSystem);
        when(elementLoader.loadCICSRegion(sysZOSCICSRegion, "53231323", "", false, new ArrayList<>())).thenReturn(cicsRegion);

        List<Object> CICSProgramElmentList = new ArrayList<>();
        CICSProgramElmentList.add(sysZOSCICSProgram);
        List<Object> CICSTransactionElementList = new ArrayList<>();
        CICSTransactionElementList.add(sysZOSCICSTransaction);

        List<SysZOSZReportFile> zosReportFileList = new ArrayList<>();
        when(zoszReportFile.getFixedPath()).thenReturn("CICS_SIT");
        when(zoszReportFile.getFixedPath()).thenReturn("CICS_SIT_Overrides");
        when(zoszReportFile.getFixedPath()).thenReturn("Programs");
        when(zoszReportFile.getFixedPath()).thenReturn("Files");
        when(zoszReportFile.getFixedPath()).thenReturn("Transactions");
        zosReportFileList.add(zoszReportFile);
        doNothing().when(zReportFileLoader).loadCICSSit("123", "abc", "", "53231323", "", false);
        zosReportFileList.add(zoszReportFile);
        doNothing().when(zReportFileLoader).loadCICSSitOverrides("123", "abc", "", "53231323", "", false);
        zosReportFileList.add(zoszReportFile);
        doNothing().when(zReportFileLoader).loadPrograms("", cicsRegion, "123", "", false);
        zosReportFileList.add(zoszReportFile);
        doNothing().when(zReportFileLoader).loadCICSFiles("", cicsRegion, "123", "", false);
        zosReportFileList.add(zoszReportFile);
        doNothing().when(zReportFileLoader).loadTransactions("", cicsRegion, "123", "", false);

        doNothing().when(layer4CICSLoader).loadCICSTransactionList(CICSTransactionElementList, "53231323", "", false, new ArrayList<>(),"","");
        doNothing().when(layer4CICSLoader).loadCICSProgramList(CICSProgramElmentList, "53231323", "", false);

    }

    @Test
    @Ignore
    public void testRunFileNotFound() {
        try {
            mockStatic(JPAManager.class);
            JPAManager jpaManager = mock(JPAManager.class);
            Whitebox.setInternalState(JPAManager.class, "jpaManager", jpaManager);
            when(jpaManager.getRepos()).thenReturn(repos);
            tester.runMain(null, "", "", "", "");
        } catch (Exception e) {
            System.out.println(e.toString());
            assertTrue(e instanceof FileNotFoundException);
        }
    }

    @Test
    @Ignore
    public void testRun() throws Exception {
        prepareLoader();

        File file = tmpFolder.newFile("test.xml");
        whenNew(FileInputStream.class).withArguments(file).thenReturn(fileInputStream);
        // TODO: Not effective
        // want multipartFile but get mockMultipartFile,
        // mockMultipartFile.getInputStream = null and then the idml = null
        // whenNew(MockMultipartFile.class).withArguments(file.getName(),
        // file.getName(), "application/xml",
        // fileInputStream).thenReturn(multipartFile);
        whenNew(MockMultipartFile.class)
                .withArguments(file.getName(), file.getName(), "application/xml", fileInputStream)
                .thenReturn(mockMultipartFile);
        when(getXMLFile.getFile("test.xml", "", "", "")).thenReturn(multipartFile);

        mockStatic(JAXBContext.class);
        JAXBContext context = mock(JAXBContext.class);
        when(JAXBContext.newInstance(Idml.class)).thenReturn(context);
        when(context.createUnmarshaller()).thenReturn(jaxbUnmarshaller);

        when(multipartFile.getInputStream()).thenReturn(inputStream);
        when(jaxbUnmarshaller.unmarshal(fileInputStream)).thenReturn(idml);
        when(idmlExtractor.extractIdml(mockMultipartFile, "")).thenReturn(idml);

        prepareIdml();
        mockPopulateDlaFromIdml();

        assertEquals("It's ok!", tester.runMain(null, file.getPath(), "", "", ""));
    }
}