/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.loader;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;
import static org.powermock.api.mockito.PowerMockito.whenNew;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2BufferPool;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2Database;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2StoredProcedure;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2Table;
import com.ibm.palantir.catelyn.jaxb.AppDbDb2Db2TableSpace;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2DataSharingGroup;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2Subsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2BufferPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2DataSharingGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Database;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2StoredProcedure;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Subsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Table;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2TableSpace;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2BufferPoolMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2BufferPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2DataSharingGroupMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DataSharingGroupRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2DatabaseMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2StoredProcedureMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2StoredProcedureRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2SubsystemMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2SubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2TableMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.DB2TableSpaceMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2TableSpaceRepository;

@RunWith(PowerMockRunner.class)
@PrepareForTest({})
public class DB2ElementLoaderTest {

    private DB2DataSharingGroupRepository db2DataSharingGroupRepository = mock(DB2DataSharingGroupRepository.class);
    private DB2SubsystemRepository db2SubsystemRepository = mock(DB2SubsystemRepository.class);
    private DB2DatabaseRepository db2DatabaseRepository = mock(DB2DatabaseRepository.class);
    private DB2TableSpaceRepository db2TableSpaceRepository = mock(DB2TableSpaceRepository.class);
    private DB2TableRepository db2TableRepository = mock(DB2TableRepository.class);
    private DB2BufferPoolRepository db2BufferPoolRepository = mock(DB2BufferPoolRepository.class);
    private DB2StoredProcedureRepository db2StoredProcedureRepository = mock(DB2StoredProcedureRepository.class);

    private DB2DataSharingGroupMetaRepository db2DataSharingGroupMetaRepository = mock(
            DB2DataSharingGroupMetaRepository.class);
    private DB2SubsystemMetaRepository db2SubsystemMetaRepository = mock(DB2SubsystemMetaRepository.class);
    private DB2DatabaseMetaRepository db2DatabaseMetaRepository = mock(DB2DatabaseMetaRepository.class);
    private DB2TableSpaceMetaRepository db2TableSpaceMetaRepository = mock(DB2TableSpaceMetaRepository.class);
    private DB2TableMetaRepository db2TableMetaRepository = mock(DB2TableMetaRepository.class);
    private DB2BufferPoolMetaRepository db2BufferPoolMetaRepository = mock(DB2BufferPoolMetaRepository.class);
    private DB2StoredProcedureMetaRepository db2StoredProcedureMetaRepository = mock(
            DB2StoredProcedureMetaRepository.class);

    private DB2ElementLoader db2ElementLoader = new DB2ElementLoader(db2DataSharingGroupRepository,
            db2SubsystemRepository,
            db2DatabaseRepository, db2TableSpaceRepository, db2TableRepository, db2BufferPoolRepository,
            db2StoredProcedureRepository,
            db2DataSharingGroupMetaRepository, db2SubsystemMetaRepository,
            db2DatabaseMetaRepository, db2TableSpaceMetaRepository, db2TableMetaRepository,
            db2BufferPoolMetaRepository,
            db2StoredProcedureMetaRepository);

    @Test
    public void testLoadDB2DataSharingGroup() throws Exception {
        SysZOSDB2DataSharingGroup sysZOSDB2DataSharingGroup = mock(SysZOSDB2DataSharingGroup.class);
        when(sysZOSDB2DataSharingGroup.getId()).thenReturn("123");
        DB2DataSharingGroup db2DataSharingGroup = mock(DB2DataSharingGroup.class);
        whenNew(DB2DataSharingGroup.class).withNoArguments().thenReturn(db2DataSharingGroup);
        doReturn(db2DataSharingGroup).when(db2DataSharingGroupRepository).save(db2DataSharingGroup);

        db2ElementLoader.loadDB2DataSharingGroup(sysZOSDB2DataSharingGroup, "53231323", "", false);
    }

    @Test
    public void testLoadDB2Subsystem() throws Exception {
        SysZOSDB2Subsystem sysZOSDB2Subsystem = mock(SysZOSDB2Subsystem.class);
        when(sysZOSDB2Subsystem.getId()).thenReturn("123");
        DB2Subsystem db2Subsystem = mock(DB2Subsystem.class);
        whenNew(DB2Subsystem.class).withNoArguments().thenReturn(db2Subsystem);
        doReturn(db2Subsystem).when(db2SubsystemRepository).save(db2Subsystem);

        db2ElementLoader.loadDB2Subsystem(sysZOSDB2Subsystem, "53231323", "", false, new ArrayList<>() );
    }

    @Test
    public void testLoadDB2DatabaseList() throws Exception {
        List<Object> objectList = new ArrayList<>();
        AppDbDb2Db2Database appDbDb2Db2Database = mock(AppDbDb2Db2Database.class);
        objectList.add(appDbDb2Db2Database);
        when(appDbDb2Db2Database.getId()).thenReturn("123");
        DB2Database db2Database = mock(DB2Database.class);
        whenNew(DB2Database.class).withNoArguments().thenReturn(db2Database);
        doReturn(db2Database).when(db2DatabaseRepository).save(db2Database);

        db2ElementLoader.loadDB2DatabaseList(objectList, "53231323", "", false, new ArrayList<>(),"","");
    }

    @Test
    public void testLoadDB2TableSpaceList() throws Exception {
        List<Object> objectList = new ArrayList<>();
        AppDbDb2Db2TableSpace appDbDb2Db2TableSpace = mock(AppDbDb2Db2TableSpace.class);
        objectList.add(appDbDb2Db2TableSpace);
        when(appDbDb2Db2TableSpace.getId()).thenReturn("DSN8S12D-DSN8D12A-DSNDBHG-ZPETPLX2-Db2TableSpace");
        DB2TableSpace db2TableSpace = mock(DB2TableSpace.class);
        whenNew(DB2TableSpace.class).withNoArguments().thenReturn(db2TableSpace);
        doReturn(db2TableSpace).when(db2TableSpaceRepository).save(db2TableSpace);

        db2ElementLoader.loadDB2TableSpaceList(objectList, "53231323", "", false);
    }

    @Test
    public void testLoadDB2TableList() throws Exception {
        List<Object> objectList = new ArrayList<>();
        AppDbDb2Db2Table appDbDb2Db2Table = mock(AppDbDb2Db2Table.class);
        objectList.add(appDbDb2Db2Table);
        when(appDbDb2Db2Table.getId()).thenReturn("123");
        DB2Table db2Table = mock(DB2Table.class);
        whenNew(DB2Table.class).withNoArguments().thenReturn(db2Table);
        doReturn(db2Table).when(db2TableRepository).save(db2Table);

        db2ElementLoader.loadDB2TableList(objectList, "53231323", "", false);
    }

    @Test
    public void testLoadDB2BufferPoolList() throws Exception {
        List<Object> objectList = new ArrayList<>();
        AppDbDb2Db2BufferPool appDbDb2Db2BufferPool = mock(AppDbDb2Db2BufferPool.class);
        objectList.add(appDbDb2Db2BufferPool);
        when(appDbDb2Db2BufferPool.getId()).thenReturn("BP0-DSNDB01-DSNDBXG-ZPETPLX2-Db2BufferPool");
        DB2BufferPool db2BufferPool = mock(DB2BufferPool.class);
        whenNew(DB2BufferPool.class).withNoArguments().thenReturn(db2BufferPool);
        doReturn(db2BufferPool).when(db2BufferPoolRepository).save(db2BufferPool);

        db2ElementLoader.loadDB2BufferPoolList(objectList, "53231323", "", false);
    }

    @Test
    public void testLoadDB2StoredProcedureList() throws Exception {
        List<Object> objectList = new ArrayList<>();
        AppDbDb2Db2StoredProcedure appDbDb2Db2StoredProcedure = mock(AppDbDb2Db2StoredProcedure.class);
        objectList.add(appDbDb2Db2StoredProcedure);
        when(appDbDb2Db2StoredProcedure.getId()).thenReturn("ADB2RCP-DC1GRP-LPAR400J-Db2StoredProcedure");
        DB2StoredProcedure db2StoredProcedure = mock(DB2StoredProcedure.class);
        whenNew(DB2StoredProcedure.class).withNoArguments().thenReturn(db2StoredProcedure);
        doReturn(db2StoredProcedure).when(db2StoredProcedureRepository).save(db2StoredProcedure);

        db2ElementLoader.loadDB2StoredProcedureList(objectList, "53231323", "", false, new ArrayList<>(),"","");
    }
}