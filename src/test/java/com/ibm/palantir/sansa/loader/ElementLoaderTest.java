/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.loader;

import java.util.ArrayList;

import org.junit.Test;
import org.junit.runner.RunWith;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;
import static org.powermock.api.mockito.PowerMockito.whenNew;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.ibm.palantir.catelyn.jaxb.SysZOSCICSPlex;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSRegion;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2Subsystem;
import com.ibm.palantir.catelyn.jaxb.SysZOSLPAR;
import com.ibm.palantir.catelyn.jaxb.SysZOSSysplex;
import com.ibm.palantir.catelyn.jaxb.SysZOSZOS;
import com.ibm.palantir.catelyn.jaxb.SysZOSZSeriesComputerSystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CFComputerSystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CFLPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSPlex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.LPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.PRSMLPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Sysplex;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZOS;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSeriesComputer;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSubSystem;
import com.ibm.palantir.catelyn.jpa.repository.dla.CFComputerSystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CFLPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSPlexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSRegionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.LPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.PRSMLPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.SysplexRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZOSRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSeriesComputerRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSubSystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CFComputerSystemMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CFLPARMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSPlexMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.CICSRegionMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.LPARMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.PRSMLPARMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.RelationshipMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.SysplexMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.ZOSMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.ZSeriesComputerMetaRepository;
import com.ibm.palantir.catelyn.jpa.repository.meta.ZSubSystemMetaRepository;

@RunWith(PowerMockRunner.class)
@PrepareForTest({})
public class ElementLoaderTest {

    private ZSeriesComputerRepository zSeriesComputerRepository = mock(ZSeriesComputerRepository.class);
    private CFComputerSystemRepository cfComputerSystemRepository = mock(CFComputerSystemRepository.class);
    private LPARRepository lparRepository = mock(LPARRepository.class);
    private CFLPARRepository cfLparRepository = mock(CFLPARRepository.class);
    private PRSMLPARRepository prsmLparRepository = mock(PRSMLPARRepository.class);
    private ZOSRepository zosRepository = mock(ZOSRepository.class);
    private SysplexRepository sysplexRepository = mock(SysplexRepository.class);
    private ZSubSystemRepository zSubSystemRepository = mock(ZSubSystemRepository.class);
    private CICSRegionRepository cicsRegionRepository = mock(CICSRegionRepository.class);
    private CICSPlexRepository cicsPlexRepository = mock(CICSPlexRepository.class);
    private RelationshipRepository relationshipRepository = mock(RelationshipRepository.class);

    private final ZSeriesComputerMetaRepository zSeriesComputerMetaRepository = mock(
            ZSeriesComputerMetaRepository.class);
    private final CFComputerSystemMetaRepository cfComputerSystemMetaRepository = mock(
            CFComputerSystemMetaRepository.class);
    private final LPARMetaRepository lparMetaRepository = mock(LPARMetaRepository.class);
    private final CFLPARMetaRepository cfLparMetaRepository = mock(CFLPARMetaRepository.class);
    private final PRSMLPARMetaRepository prsmLparMetaRepository = mock(PRSMLPARMetaRepository.class);
    private final ZOSMetaRepository zosMetaRepository = mock(ZOSMetaRepository.class);
    private final SysplexMetaRepository sysplexMetaRepository = mock(SysplexMetaRepository.class);
    private final ZSubSystemMetaRepository zSubSystemMetaRepository = mock(ZSubSystemMetaRepository.class);
    private final CICSRegionMetaRepository cicsRegionMetaRepository = mock(CICSRegionMetaRepository.class);
    private final CICSPlexMetaRepository cicsPlexMetaRepository = mock(CICSPlexMetaRepository.class);
    private final RelationshipMetaRepository relationshipMetaRepository = mock(RelationshipMetaRepository.class);

    private ElementLoader elementLoader = new ElementLoader(zSeriesComputerRepository, cfComputerSystemRepository,
            lparRepository,
            cfLparRepository, prsmLparRepository, zosRepository, sysplexRepository, zSubSystemRepository,
            cicsRegionRepository, cicsPlexRepository, relationshipRepository, zSeriesComputerMetaRepository,
            cfComputerSystemMetaRepository, lparMetaRepository, cfLparMetaRepository,
            prsmLparMetaRepository, zosMetaRepository, sysplexMetaRepository, zSubSystemMetaRepository,
            cicsRegionMetaRepository, cicsPlexMetaRepository, relationshipMetaRepository);

    @Test
    public void testLoadZSeriesComxputer() throws Exception {
        SysZOSZSeriesComputerSystem zoszSeriesComputerSystem = mock(SysZOSZSeriesComputerSystem.class);
        when(zoszSeriesComputerSystem.getId()).thenReturn("123");
        ZSeriesComputer zSeriesComputer = mock(ZSeriesComputer.class);
        whenNew(ZSeriesComputer.class).withNoArguments().thenReturn(zSeriesComputer);
        doReturn(zSeriesComputer).when(zSeriesComputerRepository).save(zSeriesComputer);

        elementLoader.loadZSeriesComputer(zoszSeriesComputerSystem, "", false);
    }

    @Test
    public void testLoadLoadCFComputerSystem() throws Exception {
        SysZOSZSeriesComputerSystem zoszSeriesComputerSystem = mock(SysZOSZSeriesComputerSystem.class);
        when(zoszSeriesComputerSystem.getId()).thenReturn("123");
        CFComputerSystem cfComputerSystem = mock(CFComputerSystem.class);
        whenNew(CFComputerSystem.class).withNoArguments().thenReturn(cfComputerSystem);
        doReturn(cfComputerSystem).when(cfComputerSystemRepository).save(cfComputerSystem);

        elementLoader.loadCFComputerSystem(zoszSeriesComputerSystem, "", "", false);
    }

    @Test
    public void testLoadZosLpar() throws Exception {
        SysZOSLPAR sysZOSLPAR = mock(SysZOSLPAR.class);
        when(sysZOSLPAR.getId()).thenReturn("123");
        LPAR lpar = mock(LPAR.class);
        whenNew(LPAR.class).withNoArguments().thenReturn(lpar);
        when(lparRepository.save(lpar)).thenReturn(lpar);

        elementLoader.loadLpar(sysZOSLPAR, "", false, new ArrayList<>());
    }

    @Test
    public void testLoadCFLpar() throws Exception {
        SysZOSLPAR sysZOSLPAR = mock(SysZOSLPAR.class);
        when(sysZOSLPAR.getId()).thenReturn("123");
        CFLPAR cfLpar = mock(CFLPAR.class);
        whenNew(CFLPAR.class).withNoArguments().thenReturn(cfLpar);
        when(cfLparRepository.save(cfLpar)).thenReturn(cfLpar);

        elementLoader.loadCFLpar(sysZOSLPAR, "53231323", "", false);
    }

    @Test
    public void testLoadPRSMLpar() throws Exception {
        SysZOSLPAR sysZOSLPAR = mock(SysZOSLPAR.class);
        when(sysZOSLPAR.getId()).thenReturn("123");
        PRSMLPAR prsmLpar = mock(PRSMLPAR.class);
        whenNew(PRSMLPAR.class).withNoArguments().thenReturn(prsmLpar);
        when(prsmLparRepository.save(prsmLpar)).thenReturn(prsmLpar);

        elementLoader.loadPRSMLpar(sysZOSLPAR, "", false);
    }

    @Test
    public void testLoadSysplex() throws Exception {
        SysZOSSysplex sysZOSSysplex = mock(SysZOSSysplex.class);
        when(sysZOSSysplex.getId()).thenReturn("123");
        Sysplex sysplex = mock(Sysplex.class);
        whenNew(Sysplex.class).withNoArguments().thenReturn(sysplex);
        doReturn(sysplex).when(sysplexRepository).save(sysplex);

        elementLoader.loadSysplex(sysZOSSysplex, "53231323", "", false);
    }

    @Test
    public void testLoadZosZos() throws Exception {
        SysZOSZOS zoszos = mock(SysZOSZOS.class);
        when(zoszos.getId()).thenReturn("123");
        ZOS zos = mock(ZOS.class);
        whenNew(ZOS.class).withNoArguments().thenReturn(zos);
        doReturn(zos).when(zosRepository).save(zos);

        elementLoader.loadZosZos(zoszos, "53231323", "335335", "", false, new ArrayList<>());
    }

    @Test
    public void testLoadZSubSystemFromCICSRegion() throws Exception {
        SysZOSCICSRegion sysZOSCICSRegion = mock(SysZOSCICSRegion.class);
        when(sysZOSCICSRegion.getId()).thenReturn("123-CICSRegion");
        ZSubSystem zSubSystem = mock(ZSubSystem.class);
        whenNew(ZSubSystem.class).withNoArguments().thenReturn(zSubSystem);
        doReturn(zSubSystem).when(zSubSystemRepository).save(zSubSystem);

        elementLoader.loadZSubSystemFromCICSRegion(sysZOSCICSRegion, "53231323", "", false);
    }

    @Test
    public void testLoadZSubsystemFromDB2() throws Exception {
        SysZOSDB2Subsystem sysZOSDB2Subsystem = mock(SysZOSDB2Subsystem.class);
        when(sysZOSDB2Subsystem.getId()).thenReturn("123-DB2Subsystem");
        ZSubSystem zSubSystem = mock(ZSubSystem.class);
        whenNew(ZSubSystem.class).withNoArguments().thenReturn(zSubSystem);
        doReturn(zSubSystem).when(zSubSystemRepository).save(zSubSystem);

        elementLoader.loadZSubSystemFromDB2(sysZOSDB2Subsystem, "53231323", "", false);
    }

    @Test
    public void testLoadCICSRegion() throws Exception {
        SysZOSCICSRegion sysZOSCICSRegion = mock(SysZOSCICSRegion.class);
        when(sysZOSCICSRegion.getId()).thenReturn("123");
        CICSRegion cicsRegion = mock(CICSRegion.class);
        whenNew(CICSRegion.class).withNoArguments().thenReturn(cicsRegion);
        doReturn(cicsRegion).when(cicsRegionRepository).save(cicsRegion);

        elementLoader.loadCICSRegion(sysZOSCICSRegion, "53231323", "", false, new ArrayList<>());
    }

    @Test
    public void testLoadCICSPlex() throws Exception {
        SysZOSCICSPlex sysZOSCICSPlex = mock(SysZOSCICSPlex.class);
        when(sysZOSCICSPlex.getId()).thenReturn("abc");
        CICSPlex cicsPlex = mock(CICSPlex.class);
        whenNew(CICSPlex.class).withNoArguments().thenReturn(cicsPlex);
        doReturn(cicsPlex).when(cicsPlexRepository).save(cicsPlex);

        elementLoader.loadCICSPlex(sysZOSCICSPlex, "53231323", "", false);
    }

}