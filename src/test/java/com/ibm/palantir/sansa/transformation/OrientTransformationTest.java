/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.transformation;

import java.util.Optional;

import static org.junit.Assert.assertEquals;
import org.junit.Test;
import org.junit.runner.RunWith;
import static org.mockito.ArgumentMatchers.anyString;
import org.mockito.Mock;
import static org.powermock.api.mockito.PowerMockito.doNothing;
import static org.powermock.api.mockito.PowerMockito.when;
import static org.powermock.api.mockito.PowerMockito.whenNew;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSFile;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTable;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTableOverride;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSTransaction;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2BufferPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2DataSharingGroup;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Database;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Subsystem;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Table;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2TableSpace;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.LPAR;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZOS;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSeriesComputer;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ZSubSystem;
import com.ibm.palantir.sansa.transfromation.OrientTransformation;
import com.orientechnologies.orient.core.db.ODatabaseSession;
import com.orientechnologies.orient.core.db.OrientDB;
import com.orientechnologies.orient.core.db.OrientDBConfig;
import com.orientechnologies.orient.core.record.OVertex;
import com.orientechnologies.orient.core.sql.executor.OResult;
import com.orientechnologies.orient.core.sql.executor.OResultSet;

@RunWith(PowerMockRunner.class)
@PrepareForTest({})
public class OrientTransformationTest {

    private OrientTransformation orientTransformation;

    @Mock
    private OrientDB orientDB;
    @Mock
    private OrientDBConfig orientDBConfig;
    @Mock
    private ODatabaseSession db;
    @Mock
    private OResultSet rs;
    @Mock
    private OResult or;
    @Mock
    private OVertex node;
    @Mock
    private ZSeriesComputer zSeriesComputer;
    @Mock
    private LPAR lpar;
    @Mock
    private ZOS zos;
    @Mock
    private ZSubSystem zSubSystem;
    @Mock
    private CICSRegion cicsRegion;
    @Mock
    private CICSSystemInitTable cicsSit;
    @Mock
    private CICSSystemInitTableOverride cicsSitOveride;
    @Mock
    private CICSProgram cicsProgram;
    @Mock
    private CICSTransaction cicsTransaction;
    @Mock
    private CICSFile cicsFile;
    @Mock
    private DB2DataSharingGroup db2DataSharingGroup;
    @Mock
    private DB2Subsystem db2Subsystem;
    @Mock
    private DB2Database db2Database;
    @Mock
    private DB2TableSpace db2TableSpace;
    @Mock
    private DB2BufferPool db2BufferPool;
    @Mock
    private DB2Table db2Table;

    private void prepare() throws Exception {
        whenNew(OrientDBConfig.class).withNoArguments().thenReturn(orientDBConfig);
        whenNew(OrientDB.class).withArguments("localhost", orientDBConfig).thenReturn(orientDB);
        when(orientDB.open(anyString(), anyString(), anyString())).thenReturn(db);

        orientTransformation = new OrientTransformation(db);
    }

    private void getNode() throws Exception {
        prepare();

        when(db.query(anyString(), anyString())).thenReturn(rs);

        when(rs.hasNext()).thenReturn(true).thenReturn(false);
        when(rs.next()).thenReturn(or);
        when(or.isVertex()).thenReturn(true);
        Optional optional = Optional.of(node);
        when(or.getVertex()).thenReturn(optional);
        doNothing().when(rs).close();

    }

    private void getNode2() throws Exception {
        prepare();

        when(db.query(anyString(), anyString(), anyString())).thenReturn(rs);

        when(rs.hasNext()).thenReturn(true).thenReturn(false);
        when(rs.next()).thenReturn(or);
        when(or.isVertex()).thenReturn(true);
        Optional optional = Optional.of(node);
        when(or.getVertex()).thenReturn(optional);
        doNothing().when(rs).close();
    }

    private void getNull() throws Exception {
        prepare();

        when(db.query(anyString(), anyString())).thenReturn(rs);
        when(rs.hasNext()).thenReturn(false);
        doNothing().when(rs).close();
    }

    private void getNull2() throws Exception {
        prepare();

        when(db.query(anyString(), anyString(), anyString())).thenReturn(rs);
        when(rs.hasNext()).thenReturn(false);
        doNothing().when(rs).close();
    }

    @Test
    public void testCreateZSeriesComputer() throws Exception {
        getNull2();

        when(zSeriesComputer.getDlaId()).thenReturn("SYSZD4-ZSeriesComputerSystem");
        when(db.newVertex("ZSeriesComputerSystem")).thenReturn(node);
        doNothing().when(node).setProperty("id", "SYSZD4-ZSeriesComputerSystem");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateZSeriesComputer(zSeriesComputer));
    }

    @Test
    public void testUpdateZSeriesComputer() throws Exception {
        getNode2();

        when(zSeriesComputer.getDlaId()).thenReturn("SYSZD4-ZSeriesComputerSystem");
        when(zSeriesComputer.getName()).thenReturn("abc");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateZSeriesComputer(zSeriesComputer));
    }

    @Test
    public void testCreateZLpar() throws Exception {
        getNull2();

        when(lpar.getDlaId()).thenReturn("PRSM2-LPAR");
        when(db.newVertex("LPAR")).thenReturn(node);
        doNothing().when(node).setProperty("id", "PRSM2-LPAR");
        when(lpar.getLparName()).thenReturn(null);
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateLpar(lpar));
    }

    @Test
    public void testUpateZLpar() throws Exception {
        getNode2();

        when(lpar.getDlaId()).thenReturn("PRSM2-LPAR");
        when(lpar.getLparName()).thenReturn("abc");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateLpar(lpar));
    }

    @Test
    public void testCreateZos() throws Exception {
        getNull2();

        when(zos.getDlaId()).thenReturn("SYSG-ZOS");
        when(db.newVertex("ZOS")).thenReturn(node);
        doNothing().when(node).setProperty("id", "SYSG-ZOS");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateZos(zos));
    }

    @Test
    public void testUpdateZos() throws Exception {
        getNode2();

        when(zos.getDlaId()).thenReturn("SYSG-ZOS");
        when(zos.getOsName()).thenReturn("abc");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateZos(zos));
    }

    @Test
    public void testCreateZSubSystem() throws Exception {
        getNull2();

        when(zSubSystem.getDlaId()).thenReturn("123-ZSubsystem");
        when(zSubSystem.getSubSystemName()).thenReturn("abc");
        when(db.newVertex("ZSubsystem")).thenReturn(node);
        doNothing().when(node).setProperty("id", "123-ZSubsystem");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateZSubSystem(zSubSystem));
    }

    @Test
    public void testUpdateZSubSystem() throws Exception {
        getNode2();

        when(zSubSystem.getDlaId()).thenReturn("123-ZSubsystem");
        when(zSubSystem.getSubSystemName()).thenReturn("abc");
        when(zSubSystem.getSubSystemVersion()).thenReturn("1.0.0");
        doNothing().when(node).setProperty("version", "1.0.0");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateZSubSystem(zSubSystem));
    }

    @Test
    public void testCreateCICSRegion() throws Exception {
        getNull2();

        when(cicsRegion.getDlaId()).thenReturn("CICSCWA1-SYS-CICSRegion");
        when(cicsRegion.getRegionName()).thenReturn("abc");
        when(db.newVertex("CICSRegion")).thenReturn(node);
        doNothing().when(node).setProperty("id", "CICSCWA1-SYS-CICSRegion");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateCICSRegion(cicsRegion));
    }

    @Test
    public void testUpdateCICSRegion() throws Exception {
        getNode2();

        when(cicsRegion.getDlaId()).thenReturn("CICSCWA1-SYS-CICSRegion");
        doNothing().when(node).setProperty("id", "CICSCWA1-SYS-CICSRegion");
        when(cicsRegion.getRegionName()).thenReturn("abc");
        when(cicsRegion.getApplicationId()).thenReturn("456");
        doNothing().when(node).setProperty("applId", "456");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateCICSRegion(cicsRegion));
    }

    @Test
    public void testCreateCICSit() throws Exception {
        getNull2();

        when(cicsSit.getDlaId()).thenReturn("CICSCWA1-SYS-CICSRegion-CICS_SIT-ZReportFile");
        when(db.newVertex("CICSSit")).thenReturn(node);
        doNothing().when(node).setProperty("id", "CICSCWA1-SYS-CICSRegion-CICS_SIT-ZReportFile");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateCICSSit(cicsSit));
    }

    @Test
    public void testUpdateCICSSit() throws Exception {
        getNode2();

        when(cicsSit.getDlaId()).thenReturn("123");
        when(cicsSit.getSitApplicationId()).thenReturn("456");
        doNothing().when(node).setProperty("applID", "456");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateCICSSit(cicsSit));
    }

    @Test
    public void testCreateCICSSitOverrides() throws Exception {
        getNull2();

        when(cicsSitOveride.getDlaId()).thenReturn("CICSCWA1-SYS-CICSRegion-CICS_SIT_Overrides-ZReportFile");
        when(db.newVertex("CICSSitOverrides")).thenReturn(node);
        doNothing().when(node).setProperty("id", "CICSCWA1-SYS-CICSRegion-CICS_SIT_Overrides-ZReportFile");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateCICSSitOverrides(cicsSitOveride));
    }

    @Test
    public void testsUpdateCICSSitOverrides() throws Exception {
        getNode2();

        when(cicsSitOveride.getDlaId()).thenReturn("123");
        when(cicsSitOveride.getSitApplicationId()).thenReturn("456");
        doNothing().when(node).setProperty("applID", "456");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateCICSSitOverrides(cicsSitOveride));
    }

    @Test
    public void testCreateCICSProgram() throws Exception {
        getNull2();

        when(cicsProgram.getDlaId()).thenReturn("CSQBCRMH-CICSCWA1-SYS-CICSProgram");
        when(cicsProgram.getProgramName()).thenReturn("abc");
        when(db.newVertex("Program")).thenReturn(node);
        doNothing().when(node).setProperty("id", "CSQBCRMH-CICSCWA1-SYS-CICSProgram");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateCICSProgram(cicsProgram));
    }

    @Test
    public void testUpdateCICSProgram() throws Exception {
        getNode2();

        when(cicsProgram.getDlaId()).thenReturn("123");
        when(cicsProgram.getProgramDescription()).thenReturn("it's ok");
        doNothing().when(node).setProperty("description", "it's ok");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateCICSProgram(cicsProgram));
    }

    @Test
    public void testCreateCICSTransaction() throws Exception {
        getNull2();

        when(cicsTransaction.getDlaId()).thenReturn("CKAM-CICSCWA1-SYS-CICSTransaction");
        when(cicsTransaction.getTransactionName()).thenReturn("abc");
        when(db.newVertex("CICSTransaction")).thenReturn(node);
        doNothing().when(node).setProperty("id", "CKAM-CICSCWA1-SYS-CICSTransaction");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateCICSTransaction(cicsTransaction));
    }

    @Test
    public void testUpdateCICSTransaction() throws Exception {
        getNode2();

        when(cicsTransaction.getDlaId()).thenReturn("123");
        when(cicsTransaction.getTransactionName()).thenReturn("abc");
        when(cicsTransaction.getDataKey()).thenReturn("dataKey");
        doNothing().when(node).setProperty("dataKey", "dataKey");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateCICSTransaction(cicsTransaction));
    }

    @Test
    public void testCreateCICSFile() throws Exception {
        getNull2();

        when(cicsFile.getDlaId()).thenReturn("DFHCMACD-CICSCWA1-SYS-CICSFile");
        when(cicsFile.getFileName()).thenReturn("abc");
        when(db.newVertex("CICSFile")).thenReturn(node);
        doNothing().when(node).setProperty("id", "DFHCMACD-CICSCWA1-SYS-CICSFile");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateCICSFile(cicsFile));
    }

    @Test
    public void testUpdateCICSFile() throws Exception {
        getNode2();

        when(cicsFile.getDlaId()).thenReturn("123");
        when(cicsFile.getFileName()).thenReturn("abc");
        when(cicsFile.getDatasetName()).thenReturn("bcdefg");
        doNothing().when(node).setProperty("datasetName", "bcdefg");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateCICSFile(cicsFile));
    }

    @Test
    public void tesCreateDb2DataSharingGroup() throws Exception {
        getNull2();

        when(db2DataSharingGroup.getDlaId()).thenReturn("DA1GRP-LPAR400J-DB2DataSharingGroup");
        when(db2DataSharingGroup.getName()).thenReturn("abc");
        when(db.newVertex("DB2DataSharingGroup")).thenReturn(node);
        doNothing().when(node).setProperty("id", "DA1GRP-LPAR400J-DB2DataSharingGroup");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateDb2DataSharingGroup(db2DataSharingGroup));
    }

    @Test
    public void testUpdateDb2DataSharingGroup() throws Exception {
        getNode2();

        when(db2DataSharingGroup.getDlaId()).thenReturn("123");
        when(db2DataSharingGroup.getName()).thenReturn("abc");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateDb2DataSharingGroup(db2DataSharingGroup));
    }

    @Test
    public void testCreateDb2Subsystem() throws Exception {
        getNull2();

        when(db2Subsystem.getDlaId()).thenReturn("DA1D-SYS-DB2Subsystem");
        when(db2Subsystem.getSubSystemName()).thenReturn("abc");
        when(db.newVertex("DB2Subsystem")).thenReturn(node);
        doNothing().when(node).setProperty("id", "DA1D-SYS-DB2Subsystem");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateDb2Subsystem(db2Subsystem));
    }

    @Test
    public void testUpdateDb2Subsystem() throws Exception {
        getNode2();

        when(db2Subsystem.getDlaId()).thenReturn("123");
        when(db2Subsystem.getSubSystemName()).thenReturn("abc");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateDb2Subsystem(db2Subsystem));
    }

    @Test
    public void testCreateDb2Database() throws Exception {
        getNull2();

        when(db2Database.getDlaId()).thenReturn("DSNDB01-DA1GRP-LPAR400J-Db2Database");
        when(db2Database.getName()).thenReturn("abc");
        when(db.newVertex("Db2Database")).thenReturn(node);
        doNothing().when(node).setProperty("id", "DSNDB01-DA1GRP-LPAR400J-Db2Database");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateDb2Database(db2Database));
    }

    @Test
    public void testUpdateDb2Database() throws Exception {
        getNode2();

        when(db2Database.getDlaId()).thenReturn("123");
        when(db2Database.getName()).thenReturn("abc");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateDb2Database(db2Database));
    }

    @Test
    public void testCreateDb2TableSpace() throws Exception {
        getNull2();

        when(db2TableSpace.getDlaId()).thenReturn("DBD01-DSNDB01-DA1GRP-LPAR400J-Db2TableSpace");
        when(db2TableSpace.getName()).thenReturn("abc");
        when(db.newVertex("Db2TableSpace")).thenReturn(node);
        doNothing().when(node).setProperty("id", "DBD01-DSNDB01-DA1GRP-LPAR400J-Db2TableSpace");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateDb2TableSpace(db2TableSpace));
    }

    @Test
    public void testUpdateDb2TableSpace() throws Exception {
        getNode2();

        when(db2TableSpace.getDlaId()).thenReturn("123");
        when(db2TableSpace.getName()).thenReturn("abc");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateDb2TableSpace(db2TableSpace));
    }

    @Test
    public void testCreateDb2BufferPool() throws Exception {
        getNull2();

        when(db2BufferPool.getDlaId()).thenReturn("BP0-DSNDB06-DA1GRP-LPAR400J-Db2BufferPool");
        when(db2BufferPool.getName()).thenReturn("abc");
        when(db.newVertex("Db2BufferPool")).thenReturn(node);
        doNothing().when(node).setProperty("id", "BP0-DSNDB06-DA1GRP-LPAR400J-Db2BufferPool");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateDb2BufferPool(db2BufferPool));
    }

    @Test
    public void testUpdateDb2BufferPool() throws Exception {
        getNode2();

        when(db2BufferPool.getDlaId()).thenReturn("123");
        when(db2BufferPool.getName()).thenReturn("abc");
        doNothing().when(node).setProperty("name", "abc");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateDb2BufferPool(db2BufferPool));
    }

    @Test
    public void testCreateDb2Table() throws Exception {
        getNull2();

        when(db2Table.getDlaId()).thenReturn("CLAIM-GENATS07-DC1V-SYS-Db2Table");
        when(db2Table.getName()).thenReturn("CLAIM");
        when(db.newVertex("Db2Table")).thenReturn(node);
        doNothing().when(node).setProperty("id", "CLAIM-GENATS07-DC1V-SYS-Db2Table");
        doNothing().when(node).setProperty("name", "CLAIM");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateDb2Table(db2Table));
    }

    @Test
    public void testUpdateDb2Table() throws Exception {
        getNode2();

        when(db2Table.getDlaId()).thenReturn("CLAIM-GENATS07-DC1V-SYS-Db2Table");
        when(db2Table.getName()).thenReturn("CLAIM");
        doNothing().when(node).setProperty("name", "CLAIM");
        when(node.save()).thenReturn(node);

        assertEquals(node, orientTransformation.createOrUpdateDb2Table(db2Table));
    }
}