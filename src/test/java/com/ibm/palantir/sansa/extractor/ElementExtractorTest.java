/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.extractor;

import com.ibm.palantir.catelyn.jaxb.*;
import org.junit.Test;

import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ElementExtractorTest {

    ElementExtractor elementExtractor = new ElementExtractor();

    private Idml idml = mock(Idml.class);
    private List<JAXBElement<?>> jaxbElements = new ArrayList<>();
    private SysZOSLPAR sysZOSLPAR = mock(SysZOSLPAR.class);
    private SysZOSZReportFile zoszReportFile = mock(SysZOSZReportFile.class);
    private Create create = mock(Create.class);
    private CDMERSpecification specification = mock(CDMERSpecification.class);

    private void prepare() {
        Idml.OperationSet operationSet = mock(Idml.OperationSet.class);
        List<Idml.OperationSet> operationSetList = new ArrayList<>();
        operationSetList.add(operationSet);
        when(idml.getOperationSets()).thenReturn(operationSetList);
        List<Object> objectList = new ArrayList<>();
        objectList.add(create);
        when(operationSet.getCreatesAndModifiesAndDeletes()).thenReturn(objectList);

        List<CDMERSpecification> specifications = new ArrayList<>();
        specifications.add(specification);
        when(create.getCDMERSpecifications()).thenReturn(specifications);
        elementExtractor.isEmpty = false;
    }

    private void setJaxbElementsOne() {
        QName qName = mock(QName.class);
        JAXBElement<?> element = new JAXBElement(qName, SysZOSLPAR.class, sysZOSLPAR);
        jaxbElements.add(element);
        when(specification.getManagedElementsAndRelationships()).thenReturn(jaxbElements);
        elementExtractor.managedElementsAndRelationships = jaxbElements;
    }

    private void setJaxbElementsTwo() {
        QName qName = mock(QName.class);
        JAXBElement<?> element = new JAXBElement(qName, SysZOSZReportFile.class, zoszReportFile);
        when(zoszReportFile.getId()).thenReturn("CICS55-IPO1-CICSRegion-Programs-ZReportFile");
        jaxbElements.add(element);
        when(specification.getManagedElementsAndRelationships()).thenReturn(jaxbElements);
        elementExtractor.managedElementsAndRelationships = jaxbElements;
    }

    @Test
    public void testResolveIdml() {
        prepare();

        when(specification.getManagedElementsAndRelationships()).thenReturn(jaxbElements);
        elementExtractor.resolveIdml(idml);
    }

    @Test
    public void testGetElementByClassName() {
        prepare();
        setJaxbElementsOne();

        String className = SysZOSLPAR.class.getName();
        Object res = elementExtractor.getElementByClassName(className);
        assertEquals(res, sysZOSLPAR);

        assertNull(elementExtractor.getElementByClassName("aaa"));
    }

    @Test
    public void testGetElementListByClassName() {
        prepare();
        setJaxbElementsOne();

        String className = SysZOSLPAR.class.getName();
        List<Object> res = elementExtractor.getElementListByClassName(className);
        assertEquals(res.get(0), sysZOSLPAR);

        List<Object> except = new ArrayList<>();
        assertEquals(except, elementExtractor.getElementListByClassName("aaa"));
    }

    @Test
    public void testGetZReportFiles() {
        prepare();
        setJaxbElementsTwo();

        List<SysZOSZReportFile> res = elementExtractor.getZReportFiles("CICS");
        assertEquals(res.get(0), zoszReportFile);
    }
}