/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.extractor;

import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TemporaryFolder;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;

import static org.junit.Assert.*;
import static org.powermock.api.mockito.PowerMockito.*;


@RunWith(PowerMockRunner.class)
@PrepareForTest({FileInputStream.class, MockMultipartFile.class})
public class GetXMLFileTest {

    @Rule
    public TemporaryFolder tmpFolder = new TemporaryFolder();
    private GetXMLFile getXMLFile = new GetXMLFile();

    @Test
    public void testRun() {
        String res = getXMLFile.run("123");
        assertEquals("to be called by other pipeline!", res);
    }

    @Test
    public void testGetFileNull() {
        assertNull(getXMLFile.getFile("", "", "", ""));
    }

    @Test
    public void testGetFileRemote() throws Exception {

        File file = tmpFolder.newFile("test.xml");
        FileInputStream fileInputStream = mock(FileInputStream.class);
        whenNew(FileInputStream.class).withArguments(file).thenReturn(fileInputStream);

        MultipartFile res = getXMLFile.getFile(file.getPath(), "", "", "");
        assertTrue(res instanceof MockMultipartFile);

    }

    @Test
    public void testGetFileLocal() throws Exception {
        File file = tmpFolder.newFile("test.xml");
        mockStatic(FileInputStream.class);
        FileInputStream fileInputStream = mock(FileInputStream.class);
        whenNew(FileInputStream.class).withArguments(file).thenReturn(fileInputStream);

        mockStatic(MockMultipartFile.class);
        MockMultipartFile multipartFile = mock(MockMultipartFile.class);
        whenNew(MockMultipartFile.class).withArguments(file.getName(), file.getName(), "application/xml", fileInputStream).thenReturn(multipartFile);


//        getXMLFile.getFile(file.getPath(), true);
        assertTrue(getXMLFile.getFile(file.getPath(), "", "", "") instanceof MockMultipartFile);
    }

}