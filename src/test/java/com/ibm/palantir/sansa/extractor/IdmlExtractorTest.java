/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.extractor;

import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.jaxb.Idml;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.web.multipart.MultipartFile;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.powermock.api.mockito.PowerMockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({JAXBContext.class})
public class IdmlExtractorTest {


    @Mock
    Unmarshaller jaxbUnmarshaller;
    @Mock
    MultipartFile file;
    @Mock
    InputStream inputStream;
    @Mock
    Idml idml;


    private void commonOperation() throws Exception {
        mockStatic(JAXBContext.class);
        JAXBContext context = mock(JAXBContext.class);
        when(JAXBContext.newInstance(Idml.class)).thenReturn(context);
        when(context.createUnmarshaller()).thenReturn(jaxbUnmarshaller);
    }

    @Test
    public void testExtractIdml() throws Exception {

        commonOperation();
        when(file.getInputStream()).thenReturn(inputStream);
        when(jaxbUnmarshaller.unmarshal(inputStream)).thenReturn(idml);

        IdmlExtractor tester = new IdmlExtractor();
        Idml result = tester.extractIdml(file, "");
        assertEquals(idml, result);
    }

    @Test
    public void testExtractIdmlIOException() throws Exception {

        commonOperation();
        doThrow(new IOException()).when(file).getInputStream();


        IdmlExtractor tester = new IdmlExtractor();
        try {
            tester.extractIdml(file, "");
        } catch (ServiceException e) {
            String errCode = ErrorCode.PluginIOError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "extractIdml");
            assertTrue(e.getMessage().equals(msg));
        }
    }

    @Test
    public void testExtractIdmlJAXBException() throws Exception {

        commonOperation();
        when(file.getInputStream()).thenReturn(inputStream);
        doThrow(new JAXBException("empty")).when(jaxbUnmarshaller).unmarshal(inputStream);

        IdmlExtractor tester = new IdmlExtractor();
        try {
            tester.extractIdml(file, "");
        } catch (ServiceException e) {
            String errCode = ErrorCode.PluginJAXBError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "extractIdml");
            assertTrue(e.getMessage().equals(msg));
        }
    }

}