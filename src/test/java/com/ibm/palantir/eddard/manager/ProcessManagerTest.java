package com.ibm.palantir.eddard.manager;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.config.ProcessCompiler;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;
import com.ibm.palantir.eddard.utils.ResponseBuilder;

public class ProcessManagerTest {

    @Mock
    private PipelineManager pipelineManager;

    @Mock
    private PipelineConf pipelineConf;

    @Mock
    ProcessCompiler processCompiler;

    @InjectMocks
    private ProcessManager processManager;

    @Mock
    ConfigManager configManager;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);

    }

    @Test
    public void processCompilerTest() throws Exception {
        String process = "PopulateDLA";
        String parameter = "param1";

        Field f = ConfigManager.class.getDeclaredField("configManager");
        f.setAccessible(true);
        f.set(null, configManager);

        List<PipelineConf> mockPlineList = new ArrayList<>();
        mockPlineList.add(pipelineConf);

        when(processCompiler.getProcesses(Mockito.anyString())).thenReturn(mockPlineList);

        when(pipelineManager.run(Mockito.any())).thenReturn("Success");

        ResponseBuilder response = processManager.processCompiler(process, parameter);

        String expected = "The PopulateDLA process has completed.";

        assertNotNull(response);
        assertEquals(expected, response.getMessage());
    }
}
