package com.ibm.palantir.eddard.manager;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class ConfigPropertyTest {
    @InjectMocks
    private ConfigProperty configProperty;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void setAndGetTypeTest() {
        String expectedType = "valueType";

        configProperty.setType(expectedType);

        assertEquals(expectedType, configProperty.getType());
    }

    @Test
    public void setAndGetUrlTest() {
        String expectedUrl = "valueUrl";

        configProperty.setUrl(expectedUrl);

        assertEquals(expectedUrl, configProperty.getUrl());
    }

    @Test
    public void setAndGetUserNameTest() {
        String expectedUserName = "valueUserName";

        configProperty.setUsername(expectedUserName);

        assertEquals(expectedUserName, configProperty.getUsername());
    }

    @Test
    public void setAndGetPasswordTest() {
        String expectedPassword = "valuePassword";

        configProperty.setPassword(expectedPassword);

        assertEquals(expectedPassword, configProperty.getPassword());
    }

}
