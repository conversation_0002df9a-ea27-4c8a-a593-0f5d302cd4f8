package com.ibm.palantir.eddard.manager;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.exception.ErrorCode;


public class PipelineManagerTest {

    @Mock
    PluginClassLoader pluginClassLoader;

    @InjectMocks
    private PipelineManager pipelineManager;
    
    private Map<String, PluginClassLoader> pluginMap;

    @Mock
    File file;

    @Before
    public void setup(){
        MockitoAnnotations.initMocks(this);
        
    }

    @SuppressWarnings("unchecked")
	@Test
    public void addLoaderTest() throws Exception{
        
        Method addLoaderMethod = PipelineManager.class.getDeclaredMethod("addLoader", String.class, PluginClassLoader.class);
        addLoaderMethod.setAccessible(true);

        addLoaderMethod.invoke(pipelineManager, "test", pluginClassLoader);
        
        Field pluginMapField = PipelineManager.class.getDeclaredField("pluginMap");
        pluginMapField.setAccessible(true);

        pluginMap = (Map<String, PluginClassLoader>) pluginMapField.get(pipelineManager);

        assertTrue(pluginMap.containsKey("test"));
        assertEquals(pluginClassLoader, pluginMap.get("test"));

        addLoaderMethod.invoke(pipelineManager, "test", pluginClassLoader);

        assertTrue(pluginMap.containsKey("test0"));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getLoaderTest() throws Exception {
        Method addLoaderMethod = PipelineManager.class.getDeclaredMethod("addLoader", String.class, PluginClassLoader.class);
        addLoaderMethod.setAccessible(true);
        addLoaderMethod.invoke(pipelineManager, "test", pluginClassLoader);
        Field pluginMapField = PipelineManager.class.getDeclaredField("pluginMap");
        pluginMapField.setAccessible(true);
        pluginMap = (Map<String, PluginClassLoader>) pluginMapField.get(pipelineManager);

        Method getLoaderMethod = PipelineManager.class.getDeclaredMethod("getLoader", String.class);
        getLoaderMethod.setAccessible(true);

        PluginClassLoader result = (PluginClassLoader) getLoaderMethod.invoke(pipelineManager, "test");
        assertEquals(pluginClassLoader, result);

        try {
            getLoaderMethod.invoke(pipelineManager, "nonexistent");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof ServiceException);
            ServiceException se = (ServiceException) e.getCause();
            assertEquals(ErrorCode.PluginNotLoaded.getCodeStr(), se.getErrorCode());
            assertTrue(se.getMessage().contains("nonexistent"));
        }
    }

    @SuppressWarnings("unchecked")
    @Test
    public void getPluginNameTest() throws Exception {
        Field pluginMapField = PipelineManager.class.getDeclaredField("pluginMap");
        pluginMapField.setAccessible(true);
        pluginMap = (Map<String, PluginClassLoader>) pluginMapField.get(pipelineManager);

        Method getPluginName = PipelineManager.class.getDeclaredMethod("getPluginName", File.class);
        getPluginName.setAccessible(true);

        when(file.getName()).thenReturn("sansa-1.4.0.jar");
        try {
            getPluginName.invoke(pipelineManager, file);
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof ServiceException);
            ServiceException se = (ServiceException) e.getCause();
            assertEquals(ErrorCode.PluginNameError.getCodeStr(), se.getErrorCode());
            assertTrue(se.getMessage().contains("nonexistent"));
        }
    }
}
