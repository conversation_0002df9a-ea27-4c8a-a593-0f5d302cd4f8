package com.ibm.palantir.eddard.storage;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class StoragePropertiesTest {
    

    @InjectMocks
    private StorageProperties storageProps;
    
    @Before
    public void setup(){
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void setLocationTest(){        
        storageProps.setLocation("sample");
        assertEquals("sample", storageProps.getLocation());
    }
}
