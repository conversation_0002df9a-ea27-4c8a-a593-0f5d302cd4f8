package com.ibm.palantir.eddard.config;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.http.converter.json.GsonHttpMessageConverter;

import com.google.gson.Gson;

public class GsonHttpMessageConverterConfigurationTest {

    @InjectMocks
    private GsonHttpMessageConverterConfiguration gsonHttpMessageConverterConfiguration;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void gsonHttpMessageConverterTest() {
        Gson gson = new Gson();

        GsonHttpMessageConverter converter = gsonHttpMessageConverterConfiguration.gsonHttpMessageConverter(gson);

        assertNotNull(converter);

        assertEquals(gson, converter.getGson());
    }

}
