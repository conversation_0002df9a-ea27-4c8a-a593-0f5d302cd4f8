package com.ibm.palantir.eddard.config;

import static org.mockito.Mockito.*;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;

import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public class MethodFilterTest {
        
    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private FilterChain filterChain;

    @Mock
    private Environment env;

    @InjectMocks
    private MethodFilter methodFilter;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void doFilterInternal_AllowListMismatch_Test() throws Exception {
        
        when(env.getProperty(Mockito.anyString())).thenReturn("allowedserver.com");

        when(request.getServerName()).thenReturn("wrongserver.com");

        methodFilter.doFilterInternal(request, response, filterChain);

        verify(response).sendError(HttpServletResponse.SC_PRECONDITION_FAILED, "Mismatch ServerName");
    }

    @Test
    public void doFilterInternal_BlockOptionsMethod_Test() throws Exception {

        when(request.getMethod()).thenReturn("OPTIONS");

        methodFilter.doFilterInternal(request, response, filterChain);

        verify(response).sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
    }

    @Test
    public void doFilterInternal_BlockDeleteMethod_Test() throws Exception {

        when(request.getMethod()).thenReturn("DELETE");


        methodFilter.doFilterInternal(request, response, filterChain);

        verify(response).sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
    }
}
