package com.ibm.palantir.eddard.utils;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class ResponseBuilderTest {
    
    @InjectMocks
    ResponseBuilder responseBuilder;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        responseBuilder = new ResponseBuilder<>("200", "Success", "Test Data");
    }

    @Test
    public void constructorWithAllParametersTest() {
        ResponseBuilder<String> result = new ResponseBuilder<>("404", "Not Found", "Error Data");

        assertEquals("404", result.getStatusCode());
        assertEquals("Not Found", result.getMessage());
        assertEquals("Error Data", result.getData());
    }

    @Test
    public void constructorWithMessageAndDataTest() {
        ResponseBuilder<String> result = new ResponseBuilder<>("Success", "Some Data");

        assertEquals("0", result.getStatusCode());
        assertEquals("Success", result.getMessage());
        assertEquals("Some Data", result.getData());
    }

    @Test
    public void constructorWithEmptyMessageTest() {
        ResponseBuilder<String> result = new ResponseBuilder<>("", "Some Data");

        assertEquals("0", result.getStatusCode());
        assertEquals("ok", result.getMessage());
        assertEquals("Some Data", result.getData());
    }

    @Test
    public void setResponseBuilderTest() {

        responseBuilder.setResponseBuilder("500", "Internal Server Error", "Error Data");

        assertEquals("500", responseBuilder.getStatusCode());
        assertEquals("Internal Server Error", responseBuilder.getMessage());
        assertEquals("Error Data", responseBuilder.getData());
    }

    @Test
    public void setDataTest() {
        String expected = "SomeData";
        responseBuilder.setData("SomeData");
        assertEquals(expected, responseBuilder.getData());
    }
}
