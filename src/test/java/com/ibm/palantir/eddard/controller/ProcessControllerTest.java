package com.ibm.palantir.eddard.controller;

import org.junit.Before;
import org.junit.Test;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;

import java.lang.reflect.Field;

import org.mockito.MockitoAnnotations;

import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.eddard.manager.ProcessManager;
import com.ibm.palantir.eddard.utils.ResponseBuilder;

public class ProcessControllerTest {

    @Mock
    private ProcessManager processManager;

    @Mock
    private ConfigManager configManager;

    @InjectMocks
    private ProcessController processController;

    @Before
    public void setup(){
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void processTest() throws Exception{
        //given
        ResponseBuilder expected = new ResponseBuilder<>();
        Field field = ConfigManager.class.getDeclaredField("configManager");
        field.setAccessible(true);
        field.set(null, configManager);

        expected.setMessage("The PopulateDLA process has completed.");
        expected.setStatusCode("0");
        System.out.println("expected: " + expected.getMessage());
        when(processManager.processCompiler(Mockito.any(), Mockito.any())).thenReturn(expected);

        //when
        ResponseBuilder actualFirst = processController.process("PopulateDLA", "CICST11D-20250210115915625001");
        ResponseBuilder actualSecond = processController.process("PopulateDLA");

        //then
        assertAll(
            ()->{
                assertEquals(expected.getStatusCode(), actualFirst.getStatusCode());
                assertEquals(expected.getMessage(), actualFirst.getMessage());
                assertEquals(expected.getData(), actualFirst.getData());
            },
            ()->{
                assertEquals(expected.getStatusCode(), actualSecond.getStatusCode());
                assertEquals(expected.getMessage(), actualSecond.getMessage());
                assertEquals(expected.getData(), actualSecond.getData());
            }
        );
    }

    @Test
    public void processWithCharLimitTest() throws Exception{
        //given
        ResponseBuilder expected = new ResponseBuilder<>();

        Field field = ConfigManager.class.getDeclaredField("configManager");
        field.setAccessible(true);
        field.set(null, configManager);

        expected.setMessage("The PopulateDLA process has completed.");
        expected.setStatusCode("0");
        System.out.println("expected: " + expected.getMessage());
        
        //when
        when(processManager.processCompiler(Mockito.any(), Mockito.any())).thenReturn(expected);
        String parameter = "This is a very long parameter that exceeds the length limit";
        ResponseBuilder actual = processController.process("PopulateDLA", parameter);

        //then
        assertEquals(expected.getMessage(), actual.getMessage());
        assertEquals(expected.getStatusCode(), actual.getStatusCode());
        assertEquals(expected.getData(), actual.getData());
    }

    @Test
    public void postProcessTest() throws Exception{
        ResponseBuilder expected = new ResponseBuilder<>();

        Field field = ConfigManager.class.getDeclaredField("configManager");
        field.setAccessible(true);
        field.set(null, configManager);

        expected.setMessage("The PopulateDLA process has completed.");
        expected.setStatusCode("0");
        System.out.println("expected: " + expected.getMessage());
        when(processManager.processCompiler(Mockito.any(), Mockito.any())).thenReturn(expected);

        ResponseBuilder actual = processController.postProcess("PopulateDLA", "CICST11D-20250210115915625001");
        System.out.println(actual);

        assertEquals(expected.getMessage(), actual.getMessage());
        assertEquals(expected.getStatusCode(), actual.getStatusCode());
        assertEquals(expected.getData(), actual.getData());
    }

    @Test
    public void postProcessWithCharLimitTest() throws Exception{
        ResponseBuilder expected = new ResponseBuilder<>();
        Field field = ConfigManager.class.getDeclaredField("configManager");
        field.setAccessible(true);
        field.set(null, configManager);

        expected.setMessage("The PopulateDLA process has completed.");
        expected.setStatusCode("0");
        System.out.println("expected: " + expected.getMessage());
        when(processManager.processCompiler(Mockito.any(), Mockito.any())).thenReturn(expected);

        String parameter = "This is a very long parameter that exceeds the length limit";
        ResponseBuilder actual = processController.postProcess("PopulateDLA", parameter);

        assertEquals(expected.getMessage(), actual.getMessage());
        assertEquals(expected.getStatusCode(), actual.getStatusCode());
        assertEquals(expected.getData(), actual.getData());
    }
}
