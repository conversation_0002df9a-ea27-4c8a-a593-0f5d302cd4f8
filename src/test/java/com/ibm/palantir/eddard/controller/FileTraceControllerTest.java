package com.ibm.palantir.eddard.controller;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import org.junit.Before;
import org.junit.Test;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import com.ibm.palantir.catelyn.exception.ServiceException;

import com.ibm.palantir.catelyn.jpa.entity.manage.FileTrace;
import com.ibm.palantir.eddard.dto.FileTraceDTO;
import com.ibm.palantir.eddard.service.FileTraceService;
import com.ibm.palantir.eddard.utils.ResponseBuilder;

public class FileTraceControllerTest {

    @InjectMocks
    private FileTraceController fileTraceController;

    @Mock
    private FileTraceService fileTraceService;

    String fileName;
    String sysplexName;
    String datasetName;
    String memberName;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        this.fileName = "TESTER";
        this.sysplexName = "PLEX1";
        this.datasetName = "runtime/disco_demo1/idml/";
        this.memberName = "TESTER";
    }

    @Test
    public void addFileTraceTest() throws Exception {
        FileTrace fileTrace = new FileTrace();
        String filePath = "20241119100821209001/TESTER-20241119150824262001";

        fileTrace.setId(1L);
        fileTrace.setFileName(fileName);
        fileTrace.setFilePath(filePath);
        fileTrace.setSysplexName(sysplexName);
        fileTrace.setDatasetName(datasetName);
        fileTrace.setMemberName(memberName);

        when(fileTraceService.addFileTrace(
                Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyString()))
                .thenReturn(fileTrace);
        ResponseEntity<String> expected = new ResponseEntity<>(HttpStatus.OK);
        ResponseEntity<String> actual = fileTraceController.addFileTrace(
                fileName,
                "20241114135701441001/TESTER-20241114185703477001",
                sysplexName,
                datasetName,
                memberName);
        assertEquals(expected, actual);
    }

    @Test
    public void getFileTraceByFilePathTest() throws Exception {
        FileTrace fileTrace = new FileTrace();
        String filePath = "20241119100821209001/TESTER-20241119150824262001";

        fileTrace.setId(1L);
        fileTrace.setFileName(fileName);
        fileTrace.setFilePath(filePath);
        fileTrace.setSysplexName(sysplexName);
        fileTrace.setDatasetName(datasetName);
        fileTrace.setMemberName(memberName);

        when(fileTraceService.getFileTraceByFilePath(Mockito.anyString())).thenReturn(fileTrace);
        ResponseEntity<FileTraceDTO> expected = new ResponseEntity<>(new FileTraceDTO(fileTrace), HttpStatus.OK);

        ResponseEntity<FileTraceDTO> actual = fileTraceController.getFileTraceByFilePath(filePath);

        assertEquals(expected.getBody().getFileName(), actual.getBody().getFileName());
        assertEquals(expected.getBody().getFilePath(), actual.getBody().getFilePath());
        assertEquals(expected.getStatusCode(), HttpStatus.OK);
    }

    @Test
    public void getFileTraceListTest() throws Exception {
        String sort = "asc";
        Integer offset = 0;
        Integer limit = 20;

        List<FileTraceDTO> fileTraceDTOS = new ArrayList<FileTraceDTO>();

        fileTraceDTOS.add(new FileTraceDTO(new Object[] {
                3L,
                "20241114133209930001/TESTER-20241114183212130001",
                "2024-11-14 18:50:49.084381",
                datasetName,
                "<EMAIL>",
                "2024-11-14 18:50:50.18511",
                fileName,
                "20241114135047827001/TESTER-20241114185049077001",
                memberName,
                sysplexName
        }));

        fileTraceDTOS.add(new FileTraceDTO(new Object[] {
                2L,
                "20241114135047827001/TESTER-20241114185049077001",
                "2024-11-14 18:57:03.486585",
                datasetName,
                "<EMAIL>",
                "2024-11-14 18:57:04.586312",
                fileName,
                "20241114135701441001/TESTER-20241114185703477001",
                memberName,
                sysplexName
        }));

        fileTraceDTOS.add(new FileTraceDTO(new Object[] {
                1L,
                null,
                "2024-11-14 20:47:07.251083",
                datasetName,
                null,
                null,
                fileName,
                "20241114154704976001/TESTER-20241114204707244001",
                memberName,
                sysplexName
        }));

        when(fileTraceService.queryFileTraceList(
                Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyString(),
                Mockito.anyInt(),
                Mockito.anyInt()))
                .thenReturn(fileTraceDTOS);

        ResponseEntity<List<FileTraceDTO>> expected = new ResponseEntity<>(fileTraceDTOS, HttpStatus.OK);

        ResponseEntity<List<FileTraceDTO>> actual = fileTraceController.getFileTraceList(fileName, sysplexName,
                datasetName, memberName, sort, offset, limit);

        assertEquals(expected, actual);
    }

    @Test
    public void getFileTraceList_SortNull_Test() throws Exception {
        String sort = "";
        Integer offset = 0;
        Integer limit = 20;

        Exception exception = assertThrows(Exception.class, () -> {
            fileTraceController.getFileTraceList(fileName, sysplexName, datasetName, memberName, sort, offset, limit);
        });

        assertEquals("Invalid sort direction:" + sort, exception.getMessage());
    }

    @Test
    public void getFileTraceList_LimitNegative_Test() {
        String sort = "asc";
        Integer offset = 0;
        Integer limit = -1;

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            fileTraceController.getFileTraceList(fileName, sysplexName, datasetName, memberName, sort, offset, limit);
        });

        assertEquals("Limit must be a positive integer", exception.getMessage());

    }

    @Test
    public void getFileTraceList_OffsetNegative_Test() {
        String sort = "asc";
        Integer offset = -1;
        Integer limit = 20;

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            fileTraceController.getFileTraceList(fileName, sysplexName, datasetName, memberName, sort, offset, limit);
        });

        assertEquals("Offset must be not a negative integer", exception.getMessage());
    }

    @Test
    public void getFileTraceList_AllValuesNull_Test() {
        NullPointerException thrown = assertThrows(NullPointerException.class, () -> {
            fileTraceController.getFileTraceList(null, null, null, null, null, null, null);
        });

        assertNotNull(thrown.getMessage());
        assertTrue(thrown.getMessage().contains("equalsIgnoreCase"));
        assertTrue(thrown.getMessage().contains("sort"));
    }

    @Test
    public void sort_ValidAsc_Test() {
        String sort = "asc";

        ResponseEntity<List<FileTraceDTO>> response = assertDoesNotThrow(
                () -> fileTraceController.getFileTraceList("file", "sysplex", "dataset", "member", sort, 0, 10),
                "Exception should not be thrown for valid sort direction 'asc'");

        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void sort_ValidDesc_Test() {
        String sort = "desc";

        ResponseEntity<List<FileTraceDTO>> response = assertDoesNotThrow(
                () -> fileTraceController.getFileTraceList("file", "sysplex", "dataset", "member", sort, 0, 10),
                "Exception should not be thrown for valid sort direction 'desc'");

        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void sort_InvalidDirection_Test() {
        String sort = "invalid";

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            fileTraceController.getFileTraceList("file", "sysplex", "dataset", "member", sort, 0, 10);
        }, "Expected IllegalArgumentException to be thrown for invalid sort direction");

        assertEquals("Invalid sort direction:" + sort, exception.getMessage());

    }

    @Test
    public void handleError_ServiceException_Test() {
        ServiceException serviceException = new ServiceException("9001", "Service Exception");

        ResponseBuilder response = assertDoesNotThrow(
                () -> fileTraceController.handleConfigurationControllerError(serviceException));

        assertNotNull(response);
        assertEquals("9001", response.getStatusCode());
    }

    @Test
    public void handleError_OtherException_Test() {
        Exception otherException = new Exception("Generic Exception");

        ResponseBuilder response = assertDoesNotThrow(
                () -> fileTraceController.handleConfigurationControllerError(otherException));

        assertNotNull(response);
        assertEquals("9001", response.getStatusCode());
    }
}
