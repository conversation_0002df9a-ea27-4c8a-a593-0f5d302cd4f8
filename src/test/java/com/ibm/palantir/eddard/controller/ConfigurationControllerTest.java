/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.eddard.controller;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.lang.reflect.Field;

import org.mockito.MockitoAnnotations;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ibm.palantir.catelyn.config.ConfigManager;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.eddard.utils.ResponseBuilder;

public class ConfigurationControllerTest {

    @Mock
    private ConfigManager configManager;

    @InjectMocks
    private ConfigurationController configurationController;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void handleConfigurationControllerError_ServiceException_Test() {
        ServiceException serviceException = new ServiceException("9001", "Service Exception");

        ResponseBuilder response = configurationController.handleConfigurationControllerError(serviceException);

        assertNotNull(response);
        assertEquals("9001", response.getStatusCode());

    }

    @Test
    public void handleConfigurationControllerError_OtherException_Test() {
        Exception otherException = new Exception("Generic Exception");

        ResponseBuilder response = configurationController.handleConfigurationControllerError(otherException);
        assertNotNull(response);
        assertEquals("9001", response.getStatusCode());
        assertTrue(response.getMessage().contains("ConfigurationController"));
        assertTrue(response.getMessage().contains("Generic Exception"));
    }

    @Test
    public void showPayloadTest() throws Exception {
        String type = "process";
        JsonObject jsonObject = JsonParser.parseString("{\n" +
                "            \"id\": \"1\",\n" +
                "            \"process\": \"PopulateDLA\",\n" +
                "            \"version\": \"process version\",\n" +
                "            \"timestamp\": \"0\",\n" +
                "            \"schedule\":{},\n" +
                "            \"invoke\":[\n" +
                "                {\n" +
                "                    \"version\": \"plugin version\",\n" +
                "                    \"type\":\"pipeline\",\n" +
                "                    \"plugin\": \"sansa\",\n" +
                "                    \"timestamp\": \"0\",\n" +
                "                    \"pipeline\": \"PopulateDLA\",\n" +
                "                    \"exctract\":[], \n" +
                "                    \"persist\":[]\n" +
                "                }\n" +
                "            ]\n" +
                "        }").getAsJsonObject();

        List<Map<String, Object>> mockResult = new ArrayList<>();

        Map<String, Object> map = new HashMap<>();
        map.put("process", jsonObject);

        mockResult.add(map);
        Field field = ConfigManager.class.getDeclaredField("configManager");
        field.setAccessible(true);
        field.set(null, configManager);

        when(configManager.searchType(Mockito.anyString())).thenReturn(mockResult);

        ResponseBuilder expected = new ResponseBuilder<>("Get config success!", mockResult);

        ResponseBuilder actual = configurationController.showPayload(type);

        assertNotNull(actual);
        assertEquals(expected.getMessage(), actual.getMessage());
        assertEquals(expected.getData(), actual.getData());
    }

}
