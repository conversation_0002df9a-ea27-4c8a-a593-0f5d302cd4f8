package com.ibm.palantir.eddard.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.JSONObject;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.ibm.palantir.eddard.service.ManageService;

public class ManageControllerTest {

    @Mock
    private ManageService manageService;

    @InjectMocks
    private ManageController manageController;

    @Before
    public void setup() {
        // Initialize mocks
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getJCLRecordTest() throws Exception {
        List<String> jobNames = List.of("JC14DX2LD", "JGMJJQTT0.MYPROC", "JZZXOTTZF");

        Map<String, Object> response = new HashMap<>();
        response.put("status", "OK");

        Map<String, Object> body = new HashMap<>();
        body.put("total_number_of_records", 3);
        body.put("records_offset", 3);
        body.put("records_returned", 3);
        body.put("distintJobCount", 3);
        body.put("records", jobNames);
        body.put("next", "");

        response.put("body", new JSONObject(body));

        when(this.manageService.getJCLRecord(Mockito.anyString(), Mockito.anyLong(), Mockito.anyLong())).thenReturn(response);

        ResponseEntity<Object> expected = new ResponseEntity<>(response, HttpStatus.OK);
        ResponseEntity<Object> actual = manageController.getJCLRecord("%", "0", "0");

        assertEquals(HttpStatus.OK, actual.getStatusCode());
        assertEquals(expected, actual);
    }

    @Test
    public void getCINumberTest() throws Exception {
        Map<String, Integer> resultMap = Map.of("public.cics_transaction", 45300, "public.db2_database", 1125,
                "public.db2_data_sharing_group", 5);
        when(manageService.getCINumber()).thenReturn(resultMap);
        ResponseEntity<Object> expected = new ResponseEntity<>(resultMap, HttpStatus.OK);
        ResponseEntity<Object> actual = manageController.getCINumber();
        assertEquals(expected, actual);
    }

}