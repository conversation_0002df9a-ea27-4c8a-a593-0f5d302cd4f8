package com.ibm.palantir.eddard.controller;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

public class SwaggerHomePageControllerTest {
    private MockMvc mvc;

    @InjectMocks
    private SwaggerHomePageController swaggerHomePageController;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mvc = MockMvcBuilders.standaloneSetup(swaggerHomePageController).build();
    }
    
    @Test
    public void swaggerHomeTest() throws Exception{
        mvc.perform(get("/"))
        .andExpect(status().is3xxRedirection())
        .andExpect(MockMvcResultMatchers.header().string("location", "swagger-ui/index.html"));
    }
}
