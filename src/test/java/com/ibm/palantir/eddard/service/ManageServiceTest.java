package com.ibm.palantir.eddard.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.mock;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.junit.After;
import org.junit.Rule;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;
import org.springframework.test.util.ReflectionTestUtils;

import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.eddard.service.ManageService.PaginatedResponse;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;

public class ManageServiceTest {
    @Rule
    public MockitoRule rule = MockitoJUnit.rule();
    @Mock
    private JPAManager jpaManager;

    @Mock
    private EntityManager entityManager;

    @InjectMocks
    private ManageService manageService;

    private MockedStatic<JPAManager> jpaManagerMockedStatic;

    @Mock
    private Query query;

    @BeforeEach
    public void setup() {
        entityManager = mock(EntityManager.class);
        query = mock(Query.class);
        MockitoAnnotations.initMocks(this);
    }

    @After
    public void tearDown() {
        if (jpaManagerMockedStatic != null) {
            jpaManagerMockedStatic.close();
        }
    }

    @Test
    public void getCINumberTest() throws Exception {
        JPAManager jpaManager = mock(JPAManager.class);
        jpaManagerMockedStatic = Mockito.mockStatic(JPAManager.class);
        jpaManagerMockedStatic.when(JPAManager::getJPAManager).thenReturn(jpaManager);
        when(jpaManager.getEntityManager()).thenReturn(entityManager);

        when(entityManager.createNativeQuery(Mockito.anyString())).thenReturn(query);
        when(query.setParameter(Mockito.anyString(), Mockito.anyList())).thenReturn(query);
        when(query.getResultList()).thenReturn(queryResult());
        Map<String, Integer> actual = manageService.getCINumber();
        assertEquals(1, actual.size());

    }

    @Test
    public void getJCLRecordTest() throws Exception {
        JPAManager jpaManager = mock(JPAManager.class);
        jpaManagerMockedStatic = Mockito.mockStatic(JPAManager.class);
        jpaManagerMockedStatic.when(JPAManager::getJPAManager).thenReturn(jpaManager);

        when(jpaManager.getEntityManager()).thenReturn(entityManager);
        when(entityManager.createNativeQuery(Mockito.anyString())).thenReturn(query);
        when(query.getSingleResult()).thenReturn(jclQueryResult().size());
        when(query.getResultList()).thenReturn(jclQueryResult());
        ReflectionTestUtils.setField(manageService, "BATCH_SIZE", 5000);

        Map<String, Object> actual = manageService.getJCLRecord("%",0L, 0L);

        assertEquals("OK", actual.get("status"));

        PaginatedResponse body = (PaginatedResponse) actual.get("body");
        assertNotNull(body);
        assertEquals(5, body.getDistintJobCount());
        assertEquals(jclQueryResult(), body.getRecords());
    }

    private List<String> configuration() {
        List<String> configurationItems = Arrays.asList("cics_region",
                "cics_transaction",
                "db2_database",
                "db2_data_sharing_group",
                "db2_stored_procedure",
                "db2_subsystem",
                "ims_database",
                "ims_sysplex_group",
                "ims_subsystem",
                "ims_transaction",
                "lpar",
                "mq_alias_queue",
                "mq_local_queue",
                "mq_model_queue",
                "mq_queue_sharing_group",
                "mq_remote_queue",
                "mq_subsystem",
                "sysplex",
                "zos",
                "z_series_computer",
                "relationship_service_now");
        return configurationItems;
    }

    private List<Object[]> queryResult() {
        List<Object[]> result = new ArrayList<>();
        Object[] ob = new Object[3];
        ob[0] = "public";
        ob[1] = "cics_transaction";
        ob[2] = 2;
        result.add(ob);
        return result;
    }

    private List<String> jclQueryResult() {
        List<String> result = new ArrayList<>();
        result.add("JobName1");
        result.add("JobName2");
        result.add("JobName3");
        result.add("JobName4");
        result.add("JobName5");
        return result;
    }

}
