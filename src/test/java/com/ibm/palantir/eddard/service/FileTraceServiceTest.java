package com.ibm.palantir.eddard.service;

import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.List;

import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.entity.manage.FileTrace;
import com.ibm.palantir.catelyn.jpa.repository.manage.FileTraceRepository;

import com.ibm.palantir.eddard.dto.FileTraceDTO;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;

public class FileTraceServiceTest {

    @Rule
    public MockitoRule rule = MockitoJUnit.rule();
    @Mock
    private JPAManager jpaManager;

    @Mock
    private EntityManager entityManager;

    @Mock
    private FileTraceRepository fileTraceRepository;

    @InjectMocks
    private FileTraceService fileTraceService;
    
    @Mock
    private Query query;

    private MockedStatic<JPAManager> jpaManagerMockedStatic;

    private String filePath;
    private String datasetName;
    private String fileName;
    private String memberName;
    private String sysplexName;
    private List<Object[]> mockQueryResult;

    @Before
    public void setup(){
        MockitoAnnotations.initMocks(this);
        filePath = "20241114133209930001/TESTER-20241114183212130001";
        datasetName = "runtime/disco_demo1/idml/";
        fileName = "TESTER";
        memberName = "TESTER";
        sysplexName = "PLEX1";
        mockQueryResult = new ArrayList<>();
        entityManager = mock(EntityManager.class);
        query = mock(Query.class);
    }

    @After
    public void tearDown() {
        if (jpaManagerMockedStatic != null) {
            jpaManagerMockedStatic.close();
        }
    }

    @Test
    public void getFileTraceByFilePathTest(){
        FileTrace fileTrace = new FileTrace();
        fileTrace.setId(1L);
        fileTrace.setDatasetName(datasetName);
        fileTrace.setFileName(fileName);
        fileTrace.setFilePath(filePath);
        fileTrace.setMemberName(memberName);
        fileTrace.setSysplexName(sysplexName);

        when(fileTraceRepository.getTraceByFilePath(Mockito.anyString())).thenReturn(fileTrace);
        FileTrace expected = fileTrace;

        FileTrace actual = fileTraceService.getFileTraceByFilePath(filePath);

        assertEquals(expected, actual);
    }

    @Test
    public void addFileTraceTest(){
        FileTrace fileTrace = new FileTrace();
        fileTrace.setFileName(fileName);
        fileTrace.setFilePath(filePath);
        fileTrace.setSysplexName(sysplexName);
        fileTrace.setDatasetName(datasetName);
        fileTrace.setMemberName(memberName);

        when(fileTraceRepository.save(Mockito.any())).thenReturn(fileTrace);
        FileTrace expected = fileTrace;

        FileTrace actual = fileTraceService.addFileTrace(fileName, filePath, sysplexName, datasetName, memberName);

        assertEquals(expected, actual);
    }


    @Test
    public void testQueryFTListWithValidParameters() {

        JPAManager jpaManager = mock(JPAManager.class);
        jpaManagerMockedStatic = Mockito.mockStatic(JPAManager.class);
        jpaManagerMockedStatic.when(JPAManager::getJPAManager).thenReturn(jpaManager);
        when(jpaManager.getEntityManager()).thenReturn(entityManager);

        when(entityManager.createNativeQuery(Mockito.anyString())).thenReturn(query);

        when(query.getResultList()).thenReturn(getQueryResult());

        List<FileTraceDTO> result = fileTraceService.queryFileTraceList("fnameValue", "snameValue", "dNameValue", "mNameValue", "asc", 0, 10);

        assertNotNull(result);
        assertFalse(result.isEmpty());

        assertEquals(1, result.size());
    }

    @Test
    public void testQueryFTListWithNoResults() {
        JPAManager jpaManager = mock(JPAManager.class);
        jpaManagerMockedStatic = Mockito.mockStatic(JPAManager.class);
        jpaManagerMockedStatic.when(JPAManager::getJPAManager).thenReturn(jpaManager);
        when(jpaManager.getEntityManager()).thenReturn(entityManager);

        when(entityManager.createNativeQuery(Mockito.anyString())).thenReturn(query);

        when(query.getResultList()).thenReturn(getQueryResult());

        List<FileTraceDTO> result = fileTraceService.queryFileTraceList("", "", "", "", "asc", 0, 10);

        assertNotNull(result);
        assertFalse(result.isEmpty());

        assertEquals(1, result.size());

    }

    private List<Object[]> getQueryResult() {
        List<Object[]> result = new ArrayList<>();
        Object[] ob = new Object[10];
        ob[0] = 1L;
        ob[1] = "benchmarkBookPath";
        ob[2] = "createdAt";
        ob[3] = "dataset";
        ob[4] = "deltaBookPath";
        ob[5] = "deltaGenerateTime";
        ob[6] = "fileName";
        ob[7] = "filePath";
        ob[8] = "memberName";
        ob[9] = "sysplexName";
        result.add(ob);
        return result;
    }    
}
