######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ZAA (C) Copyright IBM Corp. 2021
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

# Docker Image which is used as foundation for our Docker Image
# to be based off of
FROM localhost/jre:21
USER root



ARG REPO=unknown
ARG COMMIT=unknown

WORKDIR /app

# Copy tar.gz package to /app and uncompress
ADD target/*.tar.gz .

LABEL feature="IBM Z AIOps - Z Resource Discovery Data Service"
LABEL repo=${REPO}
LABEL commit=${COMMIT}

RUN mv zrdds-core* zrdds-core

COPY config/application.properties config/config.json /app/zrdds-core/config/

COPY log_config/log.xml /app/zrdds-core/log_config/

COPY docker/docker-entrypoint.sh /app/
COPY docker/healthcheck.sh /usr/local/bin/

RUN chmod +x docker-entrypoint.sh && \
  mkdir -p /app/zrdds-core/upload-dir && \
  mkdir -p /app/zrdds-core/external-config && \
  groupadd zaiops && \
  useradd -g zaiops discovery && \
  chown -R discovery:zaiops /app

ENV HOME /home/<USER>
ENV CONFIG_PATH /app/zrdds-core/external-config/config.json

USER discovery

HEALTHCHECK --interval=20s --retries=6 \
  CMD /usr/local/bin/healthcheck.sh

ENTRYPOINT ["/app/docker-entrypoint.sh"]
CMD ["java", "-jar", "./eddard.jar"]
