FROM registry.access.redhat.com/ubi8/ubi-minimal:8.10

ARG GCC_VER=see_.buildenv

LABEL feature="IBM Z AIOps - Base Image"

RUN microdnf update && \
    microdnf install -y glibc-langpack-en xz bzip2 wget gzip tar curl make gcc gcc-c++ perl findutils shadow-utils util-linux python3 git diffutils && \
    microdnf clean all && \
    HERE=$( pwd ) && \
    curl -s -L https://mirrors.ibiblio.org/gnu/gcc/gcc-${GCC_VER}/gcc-${GCC_VER}.tar.xz -O && \
    mkdir build && \
    tar xf gcc-${GCC_VER}.tar.xz -C build && \
    # Build gcc \
    cd ${HERE} && \
    cd build/gcc-${GCC_VER} && \
    ./contrib/download_prerequisites && \
    ./configure --enable-languages=c,c++,fortran,go,objc,obj-c++ --disable-multilib && \
    make && \
    # make check && \
    make install

CMD ["bash"]
