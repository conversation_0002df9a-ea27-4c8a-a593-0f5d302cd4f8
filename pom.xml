<?xml version="1.0" encoding="UTF-8"?>
<!-- ************************************************************************** -->
<!-- Licensed Materials - Property of IBM                                       -->
<!-- 5698-ZAA (C) Copyright IBM Corp. 2021, 2025                                -->
<!-- All Rights Reserved                                                        -->
<!-- US Government Users Restricted Rights - Use, duplication or                -->
<!-- disclosure restricted by GSA ADP Schedule Contract with IBM Corp.          -->
<!-- ************************************************************************** -->
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ibm.palantir.robb</groupId>
    <artifactId>robb</artifactId>
    <version>1.4.3</version>
    <name>Robb</name>
    <description>Plugin project</description>

    <properties>
        <java.version>21</java.version>
        <maven.compiler.release>21</maven.compiler.release>
    </properties>
    <repositories>
        <repository>
            <id>clojars</id>
            <url>https://clojars.org/repo/</url>
        </repository>
    </repositories>

    <dependencies>

        <!-- https://mvnrepository.com/artifact/org.apache.ivy/ivy -->
        <dependency>
            <groupId>org.apache.ivy</groupId>
            <artifactId>ivy</artifactId>
            <version>2.5.3</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>33.5.0-jre</version>
        </dependency>


        <!-- https://mvnrepository.com/artifact/com.orientechnologies/orientdb-gremlin -->
        <dependency>
            <groupId>com.orientechnologies</groupId>
            <artifactId>orientdb-gremlin</artifactId>
            <version>3.2.46</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tinkerpop</groupId>
                    <artifactId>gremlin-groovy</artifactId>
                </exclusion>

                    <exclusion>
                        <groupId>commons-lang</groupId>
                        <artifactId>commons-lang</artifactId>
                    </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.tinkerpop/gremlin-groovy -->
        <dependency>
            <groupId>org.apache.tinkerpop</groupId>
            <artifactId>gremlin-groovy</artifactId>
            <version>3.8.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-configuration2</artifactId>
            <version>2.12.0</version> <!-- Safe version -->
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.microsoft.sqlserver/sqljdbc4 -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>sqljdbc4</artifactId>
            <version>4.0</version>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.7.8</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.13.2</version>
        </dependency>
        <dependency>
            <groupId>com.couchbase.client</groupId>
            <artifactId>java-client</artifactId>
            <version>3.10.0</version>
        </dependency>

        <dependency>
            <groupId>com.ibm.palantir.catelyn</groupId>
            <artifactId>catelyn</artifactId>
            <version>1.4.3</version>
            <systemPath>${pom.basedir}/lib/catelyn-1.4.3.jar</systemPath>
            <scope>system</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.20.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.11.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>5.5.0.6356</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                    <archive>
                        <manifest>
                            <mainClass>util.Microseer</mainClass>
                        </manifest>
                    </archive>

                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>