#!/bin/bash
BASEDIR=${PWD}
export ARCH="$( uname -m )"
if [ "${ARCH}" = "x86_64" ]
then
  export OPENJDK_ARCH="x64"
elif [ "${ARCH}" = "arm64" ]
then
  export OPENJDK_ARCH="aarch64"
else
  export OPENJDK_ARCH=${ARCH}
fi
RED=$(tput setaf 1)
GREEN=$(tput setaf 2)
YELLOW=$(tput setaf 3)
BLUE=$(tput setaf 4)
ENDCOLOR=$(tput sgr0)

sed() {
  if [ "$( uname -s )" == "Darwin" ]
  then
    which gsed > /dev/null 2>&1
    if [ $? == 0 ]
    then
      gsed "$@"
    else
      echo "WARNING: Running on MacOS, but no GNU sed found."
    fi
  else
    sed $@
  fi
}

echo ""
set -a
if [ -f ${HOME}/.zoalocal ]
then
  . ${HOME}/.zoalocal
else
  printf "${YELLOW}WARNING: Development configuration file '.zoalocal' not found in directory '${HOME}'.${ENDCOLOR}\n"
fi
if [ -f ${BASEDIR}/.buildenv ]
then
  . ${BASEDIR}/.buildenv
else
  printf "${YELLOW}WARNING: Build configuration file '.buildenv' not found in directory '${BASEDIR}'.${ENDCOLOR}\n"
fi
set +a

echo ""
echo "Setting ARCH tags to '${ARCH}'..."
sed -i -e "s%-x86_64$%-${ARCH}%g" docker-compose-base.yml
JAVATAG_PLUS=`echo ${JAVATAG} | tr '_' '+'`
J21TAG_PLUS=`echo ${J21TAG} | tr '_' '+'`
JAVATAG_ESCAPED=`echo ${JAVATAG} | sed -e "s|_|%2B|g"`
J21TAG_ESCAPED=`echo ${J21TAG} | sed -e "s|_|%2B|g"`
for DFILE in $( python3 ${BASEDIR}/devutils/read-dc-yaml.py -f docker-compose-base.yml )
do
  sed -i -e "s%-x86_64$%-${ARCH}%g" ${DFILE}
  sed -i -e "s%-x86_64\ AS%-${ARCH}\ AS%g" ${DFILE}
  sed -i -e "s%@@JAVATAG@@%${JAVATAG}%g" ${DFILE}
  sed -i -e "s%@@J21TAG@@%${J21TAG}%g" ${DFILE}
  sed -i -e "s%@@OPEN_J9_BUILD@@%${OPEN_J9_BUILD}%g" ${DFILE}
  sed -i -e "s%@@JAVATAG_PLUS@@%${JAVATAG_PLUS}%g" ${DFILE}
  sed -i -e "s%@@J21TAG_PLUS@@%${J21TAG_PLUS}%g" ${DFILE}
  sed -i -e "s|@@JAVATAG_ESCAPED@@|${JAVATAG_ESCAPED}|g" ${DFILE}
  sed -i -e "s|@@J21TAG_ESCAPED@@|${J21TAG_ESCAPED}|g" ${DFILE}
done
echo ""
