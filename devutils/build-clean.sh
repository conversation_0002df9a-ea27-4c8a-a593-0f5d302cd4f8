#!/bin/bash
SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"
export BASEDIR="$( cd "${SCRIPTDIR}/.." && pwd )"
ARCH="$( uname -m )"
cd ${BASEDIR}

sed() {
  if [ "$( uname -s )" == "Darwin" ]
  then
    which gsed > /dev/null 2>&1
    if [ $? == 0 ]
    then
      gsed "$@"
    else
      echo "WARNING: Running on MacOS, but no GNU sed found."
    fi
  else
    sed $@
  fi
}

echo ""
echo "Setting ARCH tags back to 'x86_64'..."
sed -i -e "s%-${ARCH}$%-x86_64%g" docker-compose-base.yml
JAVATAG_PLUS=`echo ${JAVATAG} | tr '_' '+'`
J21TAG_PLUS=`echo ${J21TAG} | tr '_' '+'`
JAVATAG_ESCAPED=`echo ${JAVATAG} | sed -e "s|_|%2B|g"`
J21TAG_ESCAPED=`echo ${J21TAG} | sed -e "s|_|%2B|g"`
for DFILE in $( python3 ${BASEDIR}/devutils/read-dc-yaml.py -f docker-compose-base.yml )
do
  sed -i -e "s%-${ARCH}$%-x86_64%g" ${DFILE}
  sed -i -e "s%-${ARCH}\ AS%-x86_64\ AS%g" ${DFILE}
  sed -i -e "s%${JAVATAG}%@@JAVATAG@@%g" ${DFILE}
  sed -i -e "s%${J21TAG}%@@J21TAG@@%g" ${DFILE}
  sed -i -e "s%${OPEN_J9_BUILD}%@@OPEN_J9_BUILD@@%g" ${DFILE}
  sed -i -e "s%${JAVATAG_PLUS}%@@JAVATAG_PLUS@@%g" ${DFILE}
  sed -i -e "s%${J21TAG_PLUS}%@@J21TAG_PLUS@@%g" ${DFILE}
  sed -i -e "s|${JAVATAG_ESCAPED}|@@JAVATAG_ESCAPED@@|g" ${DFILE}
  sed -i -e "s|${J21TAG_ESCAPED}|@@J21TAG_ESCAPED@@|g" ${DFILE}
done
echo ""
