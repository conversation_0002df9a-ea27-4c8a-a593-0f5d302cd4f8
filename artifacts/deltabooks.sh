#!/bin/sh
###################################################################
#             (C) COPYRIGHT IBM 2012               #
#                      All Rights Reserved                        #
#                                                                 #
#       Licensed Materials - Property of IBM       #
#         Duplication or disclosure prohibited without the        #
#        expressed written consent of the IBM.     #
#                                                                 #
###################################################################

###################################################################
#                          Main                                   #
###################################################################

UsrCmd="$0 $*"
###################################################################
# Get the location of this script.
###################################################################
PROGNAME=`basename $0`
BASE_DIR=`pwd`
RC=0

###################################################################
#Check if we have java installed
###################################################################
JAVA_CMD=java
JAVA_VERSION=`${JAVA_CMD} -version  >/dev/null 2>&1`
if [ "$?" != "0" ]
then
	if [ "${JAVA_HOME}" == "" ]
	then
		echo "The environment variable JAVA_HOME hasn't been defined."
		exit 1
	else
		JAVA_CMD=${JAVA_HOME}/bin/java
	fi
fi

###################################################################
# CLASSPATH
# The following line defines the jars needed for this application
# to run. If you are not using this script make sure you add to the
# CLASSPATH variable the listed jar files.
###################################################################
DELTABOOKS_CLASSPATH="${BASE_DIR}/lib/deltabooks-0.1.jar:${BASE_DIR}/lib/dl_core.jar"

###################################################################
# JAVA HEAP SIZE
# The following line defines the default Heap size for the JVM. By
# default the Heap size have been set to 512MB.
###################################################################
DELTABOOKS_JOPTIONS="-Xms512m -Xmx512m "

${JAVA_CMD} ${DELTABOOKS_JOPTIONS} -classpath ${DELTABOOKS_CLASSPATH} \
com.ibm.tivoli.zosdla.DeltaBooksMain -b ${BASE_DIR} $1 $2 $3 $4 $5 $6 $7 &> ./upload-dir/tmp

#ping www.baidu.com -t 4

RC=$?
if [ "$RC" == "0" ]
then
	RC=0
elif [ 	"$RC" == "2" ]
then
###################################################################
# Usage
###################################################################
	echo "Usage:"
	echo "deltabooks.sh -f [fully qualified path to directory containing first discovery output] -t [fully qualified path to directory containing newest discovery output] -o [fully qualified path to directory where the output will be created] [-verbose]"
	RC=1
elif [ 	"$RC" == "1" ]
then
	RC=1
fi

exit $RC
