#!/bin/sh
###################################################################
#             (C) COPYRIGHT IBM 2012               #
#                      All Rights Reserved                        #
#                                                                 #
#       Licensed Materials - Property of IBM       #
#         Duplication or disclosure prohibited without the        #
#        expressed written consent of the IBM.     #
#                                                                 #
###################################################################

###################################################################
#                          Main                                   #
###################################################################

UsrCmd="$0 $*"
###################################################################
# Get the location of this script.
###################################################################
PROGNAME=$(basename $0)
BASE_DIR=$(pwd)
RC=0

###################################################################
#Check if we have java installed
###################################################################
JAVA_CMD=java
JAVA_VERSION=$(${JAVA_CMD} -version >/dev/null 2>&1)
if [ "$?" != "0" ]; then
	if [ "${JAVA_HOME}" == "" ]; then
		echo "The environment variable JAVA_HOME hasn't been defined."
		exit 1
	else
		JAVA_CMD=${JAVA_HOME}/bin/java
	fi
fi

###################################################################
# CLASSPATH
# The following line defines the jars needed for this application
# to run. If you are not using this script make sure you add to the
# CLASSPATH variable the listed jar files.
###################################################################
DELTABOOKS_CLASSPATH="${BASE_DIR}/lib/deltabooks-0.1.jar:${BASE_DIR}/lib/dl_core.jar"

###################################################################
# JAVA HEAP SIZE
# The following line defines the default Heap size for the JVM. By
# default the Heap size have been set to 512MB.
###################################################################
DELTABOOKS_JOPTIONS="-Xms512m -Xmx512m "

# Parse arguments to find the output directory specified by -o
target_dir=""
while [ $# -gt 0 ]; do
	if [ "$1" == "-o" ]; then
		target_dir="$2"
		shift 2
	else
		shift
	fi
done

if [ -z "${target_dir}" ]; then
	echo "WARN: Target directory -o not specified, defaulting to ${BASE_DIR}"
	target_dir="."
fi

mkdir -p "${target_dir}"

echo "INFO: Running DeltaBooksMain... Target output dir: ${target_dir}"
# Run the Java command, redirecting temp output
${JAVA_CMD} ${DELTABOOKS_JOPTIONS} -classpath ${DELTABOOKS_CLASSPATH} \
	com.ibm.tivoli.zosdla.DeltaBooksMain -b ${BASE_DIR} ${UsrCmd#* } &
>"${target_dir}/deltabooks.log"

RC=$?

echo "INFO: DeltaBooksMain finished with RC=${RC}"

#ping www.baidu.com -t 4

# RC=$?
if [ "$RC" == "0" ]; then
	echo "INFO: DeltaBooksMain successful. Moving output file..."
	# Find the generated .partial file in the base directory
	# Use find with -maxdepth 1 to avoid searching subdirectories
	# Generalize to find any .xml.partial file
	partial_file=$(find "${BASE_DIR}" -maxdepth 1 -name '*.xml.partial' -print -quit)

	if [ -n "${partial_file}" ] && [ -f "${partial_file}" ]; then
		echo "INFO: Found partial file: ${partial_file}"
		# Extract filename
		filename=$(basename "${partial_file}")
		# Construct destination path
		destination="${target_dir}/${filename}"
		echo "INFO: Moving ${partial_file} to ${destination}"
		mv "${partial_file}" "${destination}"
		if [ $? -ne 0 ]; then
			echo "ERROR: Failed to move ${partial_file} to ${destination}"
			RC=1 # Set error code if move fails
		else
			echo "INFO: Successfully moved file to ${destination}"
		fi
	else
		echo "WARN: No .partial file found in ${BASE_DIR} to move."
		# Decide if this is an error condition or not. Assuming it might be okay if no delta was generated.
		# RC=1 # Optionally set error if file is expected
	fi

	# Also move the log file if needed, or handle it differently
	# mv "${target_dir}/deltabooks.log" ...

	RC=0 # Ensure RC is 0 if Java was successful and move (if needed) worked
elif [ "$RC" == "2" ]; then
	###################################################################
	# Usage
	###################################################################
	echo "Usage:"
	echo "deltabooks.sh -f [fully qualified path to directory containing first discovery output] -t [fully qualified path to directory containing newest discovery output] -o [fully qualified path to directory where the output will be created] [-verbose]"
	RC=1
elif [ "$RC" == "1" ]; then
	RC=1
fi

exit $RC
