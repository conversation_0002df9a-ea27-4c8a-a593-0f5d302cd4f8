# FROM icr.io/zoa-oci/zoatools-gcc:8.5.0-x86_64 AS buildgcc

FROM registry.access.redhat.com/ubi8/ubi-minimal:8.10 AS builder

# Build Python

ARG PYTHON_VER=3.11.8
ARG OSSL_VER=see_.buildenv

COPY zoa-docker-base-images/python/python_${PYTHON_VER}.patch /tmp/
# COPY --from=buildgcc /usr/local /usr/local

RUN microdnf update && \
    microdnf install -y libpkgconf glibc-headers pkgconf glibc-devel gmp mpfr libmpc-devel gcc-gfortran bzip2 gcc-c++ gcc-toolset-12 tar glibc-langpack-en xz gzip curl gcc make findutils shadow-utils util-linux git diffutils zlib-devel libffi-devel bzip2-devel sqlite-devel perl patch && \
    microdnf clean all && \
    HERE=$( pwd ) && \
    mkdir build && \
    curl -s -L https://github.com/openssl/openssl/releases/download/openssl-${OSSL_VER}/openssl-${OSSL_VER}.tar.gz -O && \
    curl -s -L https://www.sqlite.org/2025/sqlite-autoconf-3500100.tar.gz -O && \
    curl -s -L https://sourceware.org/ftp/libffi/libffi-3.2.1.tar.gz -O && \
    curl -s -L https://sourceware.org/pub/bzip2/bzip2-1.0.8.tar.gz -O && \
    tar xf openssl-${OSSL_VER}.tar.gz -C build && \
    tar xf sqlite-autoconf-3500100.tar.gz -C build && \
    tar xf libffi-3.2.1.tar.gz -C build && \
    tar xf bzip2-1.0.8.tar.gz -C build && \
    rm *.tar.gz && \
    cd ${HERE} && \
    cd build/openssl-${OSSL_VER} && \
    ./Configure && \
    sed -i -e "s%^install:.*$%install:\ install_sw\ install_ssldirs%g" Makefile && \
    make && \
    # make test && \
    make install && \
    echo '/usr/local/lib' >> /etc/ld.so.conf.d/openssl.conf && \
    echo '/usr/local/lib64' >> /etc/ld.so.conf.d/openssl.conf && \
    ldconfig && \
    # Build sqlite \
    cd ${HERE} && \
    cd build/sqlite-autoconf-3500100 && \
    ./configure && \
    make && \
    # make test && \
    make install && \
    # Build libffi \
    cd ${HERE} && \
    cd build/libffi-3.2.1 && \
    ./configure && \
    make && \
    # make test && \
    make install && \
    cd / && curl -s -L https://www.python.org/ftp/python/${PYTHON_VER}/Python-${PYTHON_VER}.tgz -O && \
    tar xvf Python-${PYTHON_VER}.tgz && cd /Python-${PYTHON_VER} && \
    patch -p1 < /tmp/python_${PYTHON_VER}.patch && \
    ./configure --enable-loadable-sqlite-extensions --enable-optimizations --with-ssl=/usr/local/ssl && \
    sed -i -e "s%^install:.*$%install:\ \ commoninstall\ bininstall%g" Makefile && \
    make && \
    make install && \
    # Build bzip2 \
    cd ${HERE} && \
    cd build/bzip2-1.0.8 && \
    make -f Makefile-libbz2_so && \
    make clean && \
    make && \
    # make test && \
    make install && \
    cp -a libbz2.so* /usr/local/lib


CMD ["bash"]

# Build final image

FROM icr.io/zoa-oci/zoacommon-base-micro:8.10-x86_64

LABEL feature="IBM Z AIOps - Base Image"

COPY --from=builder /usr/local /usr/local

CMD ["bash"]