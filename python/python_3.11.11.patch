diff -ruN a/Makefile.pre.in b/Makefile.pre.in
--- a/Makefile.pre.in	2024-12-03 12:20:40
+++ b/Makefile.pre.in	2025-03-23 18:55:23
@@ -1932,7 +1932,6 @@
 		tkinter \
 		tomllib \
 		turtledemo \
-		unittest \
 		urllib \
 		venv venv/scripts venv/scripts/common venv/scripts/posix \
 		wsgiref \
@@ -1940,136 +1939,7 @@
 		xmlrpc \
 		zoneinfo \
 		__phello__
-TESTSUBDIRS=	ctypes/test \
-		distutils/tests \
-		idlelib/idle_test \
-		lib2to3/tests \
-		lib2to3/tests/data \
-		lib2to3/tests/data/fixers \
-		lib2to3/tests/data/fixers/myfixes \
-		test \
-		test/audiodata \
-		test/certdata \
-		test/certdata/capath \
-		test/cjkencodings \
-		test/crashers \
-		test/configdata \
-		test/data \
-		test/decimaltestdata \
-		test/dtracedata \
-		test/encoded_modules \
-		test/imghdrdata \
-		test/leakers \
-		test/libregrtest \
-		test/regrtestdata \
-		test/regrtestdata/import_from_tests \
-		test/regrtestdata/import_from_tests/test_regrtest_b \
-		test/sndhdrdata \
-		test/subprocessdata \
-		test/support \
-		test/test_asyncio \
-		test/test_capi \
-		test/test_cppext \
-		test/test_dataclasses \
-		test/test_doctest \
-		test/test_email \
-		test/test_email/data \
-		test/test_future_stmt \
-		test/test_gdb \
-		test/test_inspect \
-		test/test_import \
-		test/test_import/data \
-		test/test_import/data/circular_imports \
-		test/test_import/data/circular_imports/subpkg \
-		test/test_import/data/circular_imports/subpkg2 \
-		test/test_import/data/circular_imports/subpkg2/parent \
-		test/test_import/data/package \
-		test/test_import/data/package2 \
-		test/test_import/data/unwritable \
-		test/test_importlib \
-		test/test_importlib/builtin \
-		test/test_importlib/data \
-		test/test_importlib/data01 \
-		test/test_importlib/data01/subdirectory \
-		test/test_importlib/data02 \
-		test/test_importlib/data02/one \
-		test/test_importlib/data02/two \
-		test/test_importlib/data03 \
-		test/test_importlib/data03/namespace \
-		test/test_importlib/data03/namespace/portion1 \
-		test/test_importlib/data03/namespace/portion2 \
-		test/test_importlib/extension \
-		test/test_importlib/frozen \
-		test/test_importlib/import_ \
-		test/test_importlib/namespace_pkgs \
-		test/test_importlib/namespace_pkgs/both_portions \
-		test/test_importlib/namespace_pkgs/both_portions/foo \
-		test/test_importlib/namespace_pkgs/module_and_namespace_package \
-		test/test_importlib/namespace_pkgs/module_and_namespace_package/a_test \
-		test/test_importlib/namespace_pkgs/not_a_namespace_pkg \
-		test/test_importlib/namespace_pkgs/not_a_namespace_pkg/foo \
-		test/test_importlib/namespace_pkgs/portion1 \
-		test/test_importlib/namespace_pkgs/portion1/foo \
-		test/test_importlib/namespace_pkgs/portion2 \
-		test/test_importlib/namespace_pkgs/portion2/foo \
-		test/test_importlib/namespace_pkgs/project1 \
-		test/test_importlib/namespace_pkgs/project1/parent \
-		test/test_importlib/namespace_pkgs/project1/parent/child \
-		test/test_importlib/namespace_pkgs/project2 \
-		test/test_importlib/namespace_pkgs/project2/parent \
-		test/test_importlib/namespace_pkgs/project2/parent/child \
-		test/test_importlib/namespace_pkgs/project3 \
-		test/test_importlib/namespace_pkgs/project3/parent \
-		test/test_importlib/namespace_pkgs/project3/parent/child \
-		test/test_importlib/namespacedata01 \
-		test/test_importlib/partial \
-		test/test_importlib/resources \
-		test/test_importlib/source \
-		test/test_importlib/zipdata01 \
-		test/test_importlib/zipdata02 \
-		test/test_json \
-		test/test_module \
-		test/test_peg_generator \
-		test/test_pydoc \
-		test/test_sqlite3 \
-		test/test_tomllib \
-		test/test_tomllib/data \
-		test/test_tomllib/data/invalid \
-		test/test_tomllib/data/invalid/array \
-		test/test_tomllib/data/invalid/array-of-tables \
-		test/test_tomllib/data/invalid/boolean \
-		test/test_tomllib/data/invalid/dates-and-times \
-		test/test_tomllib/data/invalid/dotted-keys \
-		test/test_tomllib/data/invalid/inline-table \
-		test/test_tomllib/data/invalid/keys-and-vals \
-		test/test_tomllib/data/invalid/literal-str \
-		test/test_tomllib/data/invalid/multiline-basic-str \
-		test/test_tomllib/data/invalid/multiline-literal-str \
-		test/test_tomllib/data/invalid/table \
-		test/test_tomllib/data/valid \
-		test/test_tomllib/data/valid/array \
-		test/test_tomllib/data/valid/dates-and-times \
-		test/test_tomllib/data/valid/multiline-basic-str \
-		test/test_tools \
-		test/test_warnings \
-		test/test_warnings/data \
-		test/test_zoneinfo \
-		test/test_zoneinfo/data \
-		test/tokenizedata \
-		test/tracedmodules \
-		test/typinganndata \
-		test/xmltestdata \
-		test/xmltestdata/c14n-20 \
-		test/ziptestdata \
-		tkinter/test \
-		tkinter/test/test_tkinter \
-		tkinter/test/test_ttk \
-		unittest/test \
-		unittest/test/testmock \
-		test/test_concurrent_futures \
-		test/test_multiprocessing_fork \
-		test/test_multiprocessing_forkserver \
-		test/test_multiprocessing_spawn
+TESTSUBDIRS=
 
 TEST_MODULES=@TEST_MODULES@
 libinstall:	all $(srcdir)/Modules/xxmodule.c
