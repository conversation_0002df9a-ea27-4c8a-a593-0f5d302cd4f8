{"extract": {}, "persist": {"0": {"id": "0", "type": "Graph", "version": "****", "timestamp": 0, "url": "remote:localhost/Demo", "user": "PaKmmegEBcMLYBJzktoLGQ==", "password": "75Ipw7AUFmJZ3W77mW4XKQ=="}, "1": {"id": "1", "type": "Kafka", "version": "****", "timestamp": 0, "url": "", "user": "", "password": "", "option": {"bootstrap.servers": "localhost:9092", "topic": "Discovery", "partition": null, "key": "ZDS", "acks": "all", "batch.size": 16384, "linger.ms": 1, "buffer.memory": 33554432, "retries": 0, "enable.idempotence": false, "key.serializer": "org.apache.kafka.common.serialization.StringSerializer", "value.serializer": "org.apache.kafka.common.serialization.StringSerializer", "compression.type": "gzip"}}}, "process": {"CopyIntoOrient": {"id": 0, "process": "CopyIntoOrient", "version": "process version", "timestamp": 0, "schedule": {}, "invoke": [{"id": 0, "type": "pipeline", "version": "plugin version", "timestamp": 0, "plugin": "sansa", "pipeline": "CopyIntoOrient", "extract": [], "persist": ["0"]}]}, "PopulateDLA": {"id": 1, "process": "PopulateDLA", "version": "process version", "timestamp": 0, "schedule": {}, "invoke": [{"id": 1, "type": "pipeline", "version": "plugin version", "timestamp": 0, "plugin": "sansa", "pipeline": "PopulateDLA", "extract": [], "persist": []}]}, "TransformDLAToKafka": {"id": 2, "process": "TransformDLAToKafka", "version": "process version", "timestamp": 0, "schedule": {}, "invoke": [{"id": 1, "type": "pipeline", "version": "plugin version", "timestamp": 0, "plugin": "sansa", "pipeline": "PopulateDLA", "extract": [], "persist": []}, {"id": 2, "type": "pipeline", "version": "plugin version", "timestamp": 0, "plugin": "sansa", "pipeline": "ExportDLAToKafka", "extract": [], "persist": ["1"]}]}, "ExportDLAFromDBToKafka": {"id": 3, "process": "ExportDLAFromDBToKafka", "version": "process version", "timestamp": 0, "schedule": {}, "invoke": [{"id": 0, "type": "pipeline", "version": "plugin version", "timestamp": 0, "plugin": "sansa", "pipeline": "ExportDLAToKafka", "extract": [], "persist": ["1"]}]}, "DetectExpiredDLAItems": {"id": 4, "process": "DetectExpiredDLAItems", "version": "process version", "timestamp": 0, "schedule": {}, "invoke": [{"id": 0, "type": "pipeline", "version": "plugin version", "timestamp": 0, "plugin": "sansa", "pipeline": "DetectExpiredDLAItems", "extract": [], "persist": []}]}, "PopulateDeltaDLA": {"id": 5, "process": "PopulateDeltaDLA", "version": "process version", "timestamp": 0, "schedule": {}, "invoke": [{"id": 0, "type": "pipeline", "version": "plugin version", "timestamp": 0, "plugin": "sansa", "pipeline": "PopulateDeltaDLA", "extract": [], "persist": []}]}, "APIDataTransfer": {"id": 6, "process": "APIDataTransfer", "version": "process version", "timestamp": 0, "schedule": {}, "invoke": [{"id": 0, "type": "pipeline", "version": "plugin version", "timestamp": 0, "plugin": "jcl", "pipeline": "APIDataTransfer", "extract": [], "persist": []}]}, "JCLOperationDataGenerate": {"process": "JCLOperationDataGenerate", "version": "process version", "schedule": {}, "invoke": [{"version": "plugin version", "type": "pipeline", "plugin": "jcl", "pipeline": "JCLOperationDataGenerate", "extract": [], "persist": []}]}}}