{"java.configuration.updateBuildConfiguration": "automatic", "WhiteSource Advise.Diff.BaseBranch": "master", "java.compile.nullAnalysis.mode": "automatic", "java.project.referencedLibraries": ["lib/**/*.jar", "${env:HOME}/.m2/repository/**/*.jar"], "java.import.maven.enabled": true, "java.maven.downloadSources": true, "java.maven.updateSnapshots": true, "java.project.sourcePaths": ["src/main/java"], "java.project.outputPath": "target/classes", "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable", "sonarlint.connectedMode.project": {"connectionId": "http-zds-node1-fyre-ibm-com-9000", "projectKey": "com.ibm.palantir.sansa:sansa"}}