{"exclude": {"files": "^.secrets.baseline$", "lines": null}, "generated_at": "2024-07-26T07:51:38Z", "plugins_used": [{"name": "AWSKeyDetector"}, {"name": "ArtifactoryDetector"}, {"name": "AzureStorageKeyDetector"}, {"base64_limit": 4.5, "name": "Base64HighEntropyString"}, {"name": "BasicAuthDetector"}, {"name": "BoxDetector"}, {"name": "CloudantDetector"}, {"ghe_instance": "github.ibm.com", "name": "GheDetector"}, {"name": "GitHubTokenDetector"}, {"hex_limit": 3, "name": "HexHighEntropyString"}, {"name": "IbmCloudIamDetector"}, {"name": "IbmCosHmacDetector"}, {"name": "JwtTokenDetector"}, {"keyword_exclude": null, "name": "KeywordDetector"}, {"name": "MailchimpDetector"}, {"name": "NpmDetector"}, {"name": "PrivateKeyDetector"}, {"name": "SlackDetector"}, {"name": "SoftlayerDetector"}, {"name": "SquareOAuthDetector"}, {"name": "StripeDetector"}, {"name": "TwilioKeyDetector"}], "results": {"README.md": [{"hashed_secret": "1f5e25be9b575e9f5d39c82dfd1d9f4d73f1975c", "is_verified": false, "line_number": 28, "type": "Secret Keyword", "verified_result": null}], "src/main/java/com/ibm/palantir/sansa/loader/FileObserverLoader.java": [{"hashed_secret": "852020d2bbcd4c16a91df455e7d4d2587365390d", "is_secret": false, "is_verified": false, "line_number": 62, "type": "Secret Keyword", "verified_result": null}], "src/main/java/com/ibm/palantir/sansa/pipeline/CopyIntoOrient.java": [{"hashed_secret": "d033e22ae348aeb5660fc2140aec35850c4da997", "is_secret": true, "is_verified": false, "line_number": 698, "type": "Secret Keyword", "verified_result": null}]}, "version": "0.13.1+ibm.62.dss", "word_list": {"file": null, "hash": null}}