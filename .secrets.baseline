{"exclude": {"files": "package*.json|^.secrets.baseline$", "lines": null}, "generated_at": "2024-09-08T12:49:37Z", "plugins_used": [{"name": "AWSKeyDetector"}, {"name": "ArtifactoryDetector"}, {"name": "AzureStorageKeyDetector"}, {"base64_limit": 4.5, "name": "Base64HighEntropyString"}, {"name": "BasicAuthDetector"}, {"name": "BoxDetector"}, {"name": "CloudantDetector"}, {"ghe_instance": "github.ibm.com", "name": "GheDetector"}, {"name": "GitHubTokenDetector"}, {"hex_limit": 3, "name": "HexHighEntropyString"}, {"name": "IbmCloudIamDetector"}, {"name": "IbmCosHmacDetector"}, {"name": "JwtTokenDetector"}, {"keyword_exclude": null, "name": "KeywordDetector"}, {"name": "MailchimpDetector"}, {"name": "NpmDetector"}, {"name": "PrivateKeyDetector"}, {"name": "SlackDetector"}, {"name": "SoftlayerDetector"}, {"name": "SquareOAuthDetector"}, {"name": "StripeDetector"}, {"name": "TwilioKeyDetector"}], "results": {"nginx/config/nginx.conf": [{"hashed_secret": "ff2af41a5d6120d9828a13c0b1edb3bbc22fba77", "is_secret": false, "is_verified": false, "line_number": 53, "type": "Secret Keyword", "verified_result": null}]}, "version": "0.13.1+ibm.62.dss", "word_list": {"file": null, "hash": null}}