# Sansa
Data Feed Pipeline plugin for DLA


#### config required

__1__ PostgresQL

- install PosgresQL, 2.2.3e version at least. 
- create new Accout, for example, `tester` with password `admin`, owner authority is best.
- create new db and create schema, for example, `dbTest` and `staging`, so the url is like `*************************************************************`
- set those properties in the Eddard's application.properties 
    - `spring.datasource.primary.url`
    - `spring.datasource.primary.username` 
    - `spring.datasource.primary.password`
 
__2__ OrientDB

- install OrientDB, 3.0.26 version at least.
- run the OrientDB server and then update the password
- set those properties in the CouchBase's persist bucket
    - `user`
    - `password`
    - `url` ends with `dbName`
    ```
    {
      "user":"root",
      "password":"",
      "url":"remote:localhost/wlag",
      "id":"1",
      "type":"Graph",
      "version":"****",
      "timestamp":0
    }
    ```

- Tips for Remote pattern and Local pattern
    - when `url` startswith `remote:`, use Remote pattern to access db through http
    - when `url` startswith `embedded` or `plocal`, use Local pattern to access db through file
    - when using Local pattern, not need to set the `usr` and `password`, they are set `admin/admin` as required
    - When using Local pattern, recommend to shut down the OrientDB server to avoid raise error `file just can be opened once`


#### Build workflow

- run `mvn clean package` under Catelyn
- copy `catelyn-${VERSION}.jar` to Sansa's lib
- copy `catelyn-${VERSION}.jar` to Eddard's libs
- run `mvn clean package` under Sansa
- copy `sansa-${VERSION}.jar` to Eddard's plugins
- run `mvn install` under Eddard to install Catelyn's jar
- run `mvn clean package` under Eddard
- run `jar Eddard-xxx.jar`


#### workflow example

__0.__ config and run the SpringBoot application firstly

__1.__ upload DLA xml file

- access `localhost:8080` 

- choose the xml file, for example `cics55.xml`, and click Upload button, you will get `http://localhost:8080/files/cics55.xml`.

__2.__ populate DLA data from XML to PostgresQL

- for local file, just access `http://localhost:8080/plugin/sansa/pipeline/PopulateDLA/cics55.xml` and wait to complete

- for remote file url, //TODO

__3.__ transform data from PostgresQL to OrientDB

- just access `http://localhost:8080/plugin/sansa/pipeline/CopyIntoOrient`

__4.__ merge with AD data (need integration with Robb) 