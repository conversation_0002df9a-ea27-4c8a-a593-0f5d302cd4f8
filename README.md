# ZDS-Utils

Necessary tools to perform various operations that are needed for testing or supporting our customers.

## ZRD<PERSON>

### Available ZRDDS 1.3 tools

- [clear_databases.sh](ZRDDS/1.3/clear_databases.sh)
  - Clears all the databases used with ZRDDS 1.3.1.2. It is useful when you want to start fresh. or help a customer who had issues with their databases. To use it, you need to have the zrdds-core container running and then run:

  ```bash
  docker ps
  ```

  to find the zrdds-core container name and then run:

  ```bash
  docker cp clear_databases.sh zrdds-core:/app
  ```

  then run:

  ```bash
  docker exec <zrdds container name> chmod +x /app/clear_databases.sh && docker exec <zrdds container name>  /app/clear_databases.sh
  ```

  ***TODO***: It may even work on *******. Need to test.

- [restore_tables.sh](ZRDDS/1.4/restore_tables.sh)
  - Restores exported table data from CSV files into ZRDDS ******* PostgreSQL database.
  - To run the script, copy the CSVs and script into the ZRDDS ******* container and execute:

    ```bash
    docker cp restore_tables.sh zrdds:/tmp/restore_tables.sh
    docker cp exported_tables_from_14 zrdds:/tmp/exported_tables
    docker exec -it zrdds bash -c "chmod +x /tmp/restore_tables.sh && /tmp/restore_tables.sh"
    ```

  - Or use Podman instead of Docker if applicable.

  - ***NOTE***: Assumes exported_tables_from_14 contains valid CSV files exported from ZRDDS 1.4.x using export_tables.sh.

   ***TODO***: Tested on ******* only. Add rollback validation and test on other 1.3.x versions if applicable.

### Available ZRDDS 1.4 tools

- [clear_postgres.sh](ZRDDS/1.4/clear_postgres.sh)
  - Clears all the PostgreSQL databases used with ZRDDS `1.4.0`, `1.4.1`,`1.4.2`
  - To run the script, copy the script into your machine where ZRDDS is running and run:

  ```bash
  chmod +x clear_postgres.sh && ./clear_postgres.sh
  ```

   ***TODO***: Tested only on 1.4.2. Need to test all 1.4 versions.

- [export_tables.sh](ZRDDS/1.4/export_tables.sh)
  - Exports table data from PostgreSQL (excluding specific columns and tables) for backup or downgrade from ZRDDS 1.4.x to *******
  - To run the script, copy it into the ZRDDS PostgreSQL container and execute:

    ```bash
    docker cp export_tables.sh zrdds-postgresql:/tmp/export_tables.sh
    docker exec -it zrdds-postgresql chmod +x /tmp/export_tables.sh
    docker exec -it zrdds-postgresql /tmp/export_tables.sh
    ```

- Or use Podman instead of Docker if applicable.
- Exported CSVs will be saved in /tmp/exported_tables inside the container. Copy them to your host using:

    ```bash
    docker cp zrdds-postgresql:/tmp/exported_tables ./exported_tables_from_14
    ```

   ***TODO***: Tested on 1.4.2 only. Confirm compatibility with other 1.4.x versions.
